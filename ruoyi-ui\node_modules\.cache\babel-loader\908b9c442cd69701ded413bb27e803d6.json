{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\components\\TreeTable\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\components\\TreeTable\\index.vue", "mtime": 1753944289204}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "data", "type", "Array", "default", "<PERSON><PERSON><PERSON>", "String", "currentPage", "Number", "pageSize", "total", "loading", "Boolean", "selectedSources", "dicts", "filterText", "selectedData", "isPagingOperation", "searchDebounceTimer", "selectedClassify", "selectedCountry", "dynamicTableHeight", "computed", "tableData", "showCountryFilter", "$route", "path", "query", "menuType", "id", "getSelectedSourcesText", "length", "totalCount", "names", "map", "item", "label", "concat", "join", "slice", "remainingCount", "tableHeight", "watch", "handler", "_this", "$nextTick", "scrollToTop", "updateTableHeight", "immediate", "_this2", "newVal", "_this3", "clearTimeout", "setTimeout", "handleFilterSearch", "deep", "methods", "handleRowClick", "row", "column", "_this4", "console", "log", "isSelected", "some", "filter", "$refs", "table", "toggleRowSelection", "_objectSpread2", "clearSelection", "$emit", "handleSelectionChange", "selection", "handleReset", "handleSelectAll", "_this5", "for<PERSON>ach", "handleClearAll", "handleClassifyChange", "value", "_this6", "handleCountryChange", "_this7", "handleSizeChange", "size", "_this8", "handleCurrentChange", "page", "_this9", "openUrl", "url", "window", "open", "_this10", "bodyWrapper", "scrollTop", "$el", "tableBody", "querySelector", "keyword", "_this11", "restoreSelection", "itemsToSelect", "_this12", "tableRow", "find", "restoreSelectionSilently", "_this13", "_this14", "tableContainer", "containerHeight", "clientHeight", "mounted", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/components/TreeTable/index.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"tree-table-container\"\r\n    v-loading=\"loading\"\r\n    element-loading-text=\"数据加载中\"\r\n  >\r\n    <!-- 搜索框 -->\r\n    <div class=\"search-container\">\r\n      <el-input\r\n        placeholder=\"输入关键字进行过滤\"\r\n        v-model=\"filterText\"\r\n        clearable\r\n        class=\"input_Fixed\"\r\n        style=\"margin-bottom: 10px\"\r\n      >\r\n        <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n      </el-input>\r\n    </div>\r\n\r\n    <!-- 操作按钮行 -->\r\n    <div class=\"action-container\">\r\n      <!-- 数据源分类筛选 -->\r\n      <div class=\"filter-container\">\r\n        <el-select\r\n          v-model=\"selectedClassify\"\r\n          placeholder=\"数据源分类\"\r\n          clearable\r\n          size=\"mini\"\r\n          style=\"width: 120px\"\r\n          @change=\"handleClassifyChange\"\r\n          class=\"classify-select\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.think_tank_class\"\r\n            :label=\"dict.label\"\r\n            :key=\"'think_tank_class' + dict.value\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <!-- 按国家筛选 - 仅在MonitorUse页面且id=1时显示 -->\r\n        <!-- <el-select\r\n          v-if=\"showCountryFilter\"\r\n          v-model=\"selectedCountry\"\r\n          placeholder=\"国家\"\r\n          clearable\r\n          size=\"mini\"\r\n          style=\"width: 100px; margin-left: 5px\"\r\n          @change=\"handleCountryChange\"\r\n          class=\"country-select\"\r\n        >\r\n          <el-option\r\n            v-for=\"(dict, index) in dict.type.country\"\r\n            :label=\"dict.label\"\r\n            :key=\"'country' + dict.value\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select> -->\r\n      </div>\r\n\r\n      <!-- 全选、取消选中和重置按钮 -->\r\n      <div class=\"button-container\">\r\n        <el-tooltip\r\n          class=\"item\"\r\n          effect=\"dark\"\r\n          content=\"全选当前页\"\r\n          placement=\"top\"\r\n        >\r\n          <el-button\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelectAll\"\r\n            type=\"text\"\r\n            style=\"color: #409eff; padding: 0\"\r\n          >\r\n            全选\r\n          </el-button>\r\n        </el-tooltip>\r\n        <el-tooltip\r\n          class=\"item\"\r\n          effect=\"dark\"\r\n          content=\"取消所有选中\"\r\n          placement=\"top\"\r\n        >\r\n          <el-button\r\n            icon=\"el-icon-close\"\r\n            @click=\"handleClearAll\"\r\n            type=\"text\"\r\n            style=\"color: #f56c6c; padding: 0\"\r\n          >\r\n            取消选中\r\n          </el-button>\r\n        </el-tooltip>\r\n        <el-tooltip class=\"item\" effect=\"dark\" content=\"重置\" placement=\"top\">\r\n          <el-button\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleReset\"\r\n            type=\"text\"\r\n            style=\"color: #666; padding: 0\"\r\n          >\r\n            重置\r\n          </el-button>\r\n        </el-tooltip>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 已勾选数据源显示行 -->\r\n    <div\r\n      class=\"selected-sources-info\"\r\n      v-if=\"selectedSources && selectedSources.length > 0\"\r\n    >\r\n      <span class=\"selected-text\">\r\n        {{ getSelectedSourcesText }}\r\n      </span>\r\n    </div>\r\n\r\n    <!-- 表格 -->\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        style=\"font-size: 16px\"\r\n        :height=\"tableHeight\"\r\n        :show-header=\"false\"\r\n        @row-click=\"handleRowClick\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        ref=\"table\"\r\n        :row-key=\"rowKey\"\r\n        size=\"small\"\r\n        border\r\n      >\r\n        <!-- 序号列 -->\r\n        <el-table-column type=\"index\" label=\"序号\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <!-- 复选框列 -->\r\n        <el-table-column\r\n          type=\"selection\"\r\n          width=\"30\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n\r\n        <!-- 名称+数量+链接列 -->\r\n        <el-table-column label=\"名称\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"name-cell\">\r\n              <span class=\"name-text\">{{ scope.row.label }}</span>\r\n              <div\r\n                class=\"country-text\"\r\n                v-if=\"scope.row.country && scope.row.country !== '0'\"\r\n              >\r\n                <div>[</div>\r\n                <dict-tag\r\n                  :options=\"dict.type.country\"\r\n                  :value=\"scope.row.country\"\r\n                  class=\"country-tag\"\r\n                />\r\n                <div>]</div>\r\n              </div>\r\n              <span class=\"count-text\" v-if=\"scope.row.count !== undefined\">\r\n                ({{ scope.row.count }})\r\n              </span>\r\n              <el-tooltip\r\n                v-if=\"scope.row.url\"\r\n                content=\"打开数据源链接\"\r\n                placement=\"top-start\"\r\n                effect=\"light\"\r\n              >\r\n                <i\r\n                  class=\"el-icon-connection link-icon\"\r\n                  @click.stop=\"openUrl(scope.row.url)\"\r\n                ></i>\r\n              </el-tooltip>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"treeTable-pagination\">\r\n      <el-pagination\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :pager-count=\"5\"\r\n        :page-sizes=\"[50, 100, 150, 200]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next\"\r\n        :total=\"total\"\r\n        small\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SimpleTreeTable\",\r\n  props: {\r\n    data: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    rowKey: {\r\n      type: String,\r\n      default: \"id\",\r\n    },\r\n    currentPage: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      default: 20,\r\n    },\r\n    total: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 已勾选的数据源列表，用于显示勾选信息\r\n    selectedSources: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n  },\r\n  dicts: [\"country\", \"think_tank_class\"],\r\n  data() {\r\n    return {\r\n      filterText: \"\",\r\n      selectedData: [], // 简单的选中数据数组\r\n      isPagingOperation: false, // 标记是否是分页操作\r\n      searchDebounceTimer: null, // 搜索防抖定时器\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      dynamicTableHeight: null, // 动态计算的表格高度\r\n    };\r\n  },\r\n  computed: {\r\n    tableData() {\r\n      // 直接返回传入的数据，过滤由后端接口处理\r\n      return this.data;\r\n    },\r\n    // 判断是否显示国家筛选下拉框\r\n    showCountryFilter() {\r\n      // 当当前页面是MonitorUse并且地址上的id参数等于1时才显示\r\n      return (\r\n        this.$route.path == \"/MonitorUse\" &&\r\n        !this.$route.query.menuType &&\r\n        this.$route.query.id === \"1\"\r\n      );\r\n    },\r\n    // 获取已勾选数据源的显示文本\r\n    getSelectedSourcesText() {\r\n      if (!this.selectedSources || this.selectedSources.length === 0) {\r\n        return \"\";\r\n      }\r\n\r\n      const totalCount = this.selectedSources.length;\r\n\r\n      if (totalCount <= 3) {\r\n        // 小于等于3个时，显示所有名称，不显示\"等X个数据源\"\r\n        const names = this.selectedSources.map(\r\n          (item) => item.label || item.name\r\n        );\r\n        return `当前已勾选${names.join(\"、\")}`;\r\n      } else {\r\n        // 超过3个时，显示前3个名称加上剩余数量\r\n        const names = this.selectedSources\r\n          .slice(0, 3)\r\n          .map((item) => item.label || item.name);\r\n        const remainingCount = totalCount - 3;\r\n        return `当前已勾选${names.join(\"、\")}等${remainingCount}个数据源`;\r\n      }\r\n    },\r\n    // 计算表格高度\r\n    tableHeight() {\r\n      return this.dynamicTableHeight || \"auto\";\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler() {\r\n        // 数据变化时，只滚动到顶部，不清空选择\r\n        this.$nextTick(() => {\r\n          // 滚动到顶部\r\n          this.scrollToTop();\r\n        });\r\n        // 重新计算表格高度\r\n        this.updateTableHeight();\r\n      },\r\n      immediate: true,\r\n    },\r\n    currentPage: {\r\n      handler() {\r\n        // 分页变化时滚动到顶部\r\n        this.$nextTick(() => {\r\n          this.scrollToTop();\r\n        });\r\n      },\r\n    },\r\n    filterText: {\r\n      handler(newVal) {\r\n        // 清除之前的防抖定时器\r\n        if (this.searchDebounceTimer) {\r\n          clearTimeout(this.searchDebounceTimer);\r\n        }\r\n\r\n        // 设置防抖，500ms后执行搜索\r\n        this.searchDebounceTimer = setTimeout(() => {\r\n          this.handleFilterSearch(newVal);\r\n        }, 500);\r\n      },\r\n    },\r\n    // 监听已选择数据源变化，重新计算高度\r\n    selectedSources: {\r\n      handler() {\r\n        this.updateTableHeight();\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 处理行点击 - 重写版本（点击行是单选，直接替换保存勾选数据）\r\n    handleRowClick(row, column) {\r\n      // 如果点击的是复选框列，不处理\r\n      if (column && column.type === \"selection\") {\r\n        return;\r\n      }\r\n\r\n      console.log(\"行点击（单选）:\", row.label);\r\n\r\n      // 检查当前行是否已选中\r\n      const isSelected = this.selectedData.some(\r\n        (item) => item[this.rowKey] === row[this.rowKey]\r\n      );\r\n\r\n      if (isSelected) {\r\n        // 如果已选中，则取消选中\r\n        this.selectedData = this.selectedData.filter(\r\n          (item) => item[this.rowKey] !== row[this.rowKey]\r\n        );\r\n        // 直接操作表格取消选中\r\n        this.$refs.table.toggleRowSelection(row, false);\r\n      } else {\r\n        // 如果未选中，则清空其他选择，只选中当前行（单选）\r\n        this.selectedData = [{ ...row }];\r\n        // 直接操作表格：先清空，再选中当前行\r\n        this.$refs.table.clearSelection();\r\n        this.$refs.table.toggleRowSelection(row, true);\r\n      }\r\n\r\n      console.log(\r\n        \"行点击后选中数据:\",\r\n        this.selectedData.map((item) => item.label)\r\n      );\r\n\r\n      // 触发父组件事件（行点击是单选，直接替换）\r\n      this.$emit(\"selection-change\", this.selectedData, \"row-click\");\r\n    },\r\n\r\n    // 处理复选框选择变化（点击勾选框是多选，往里面push或删除）\r\n    handleSelectionChange(selection) {\r\n      this.selectedData = selection.map((item) => ({ ...item }));\r\n\r\n      // 如果是分页操作导致的选择变化，不触发父组件事件\r\n      if (!this.isPagingOperation) {\r\n        console.log(\"复选框变化触发父组件事件（这会更新保存的勾选数据）\");\r\n        this.$emit(\"selection-change\", this.selectedData, \"checkbox-change\");\r\n      }\r\n    },\r\n\r\n    // 处理重置（重置才更新保存的勾选数据）\r\n    handleReset() {\r\n      // 清空搜索关键字（会触发 watch 调用后端接口）\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null; // 重置数据源分类选择\r\n      this.selectedCountry = null; // 重置国家筛选选择\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"重置，触发父组件事件（这会更新保存的勾选数据）\");\r\n      this.$emit(\"reset\");\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n    },\r\n\r\n    // 处理全选当前页（全选是把当页所有数据都push进去）\r\n    handleSelectAll() {\r\n      if (this.tableData && this.tableData.length > 0) {\r\n        // 选中当前页所有数据\r\n        this.selectedData = this.tableData.map((item) => ({ ...item }));\r\n\r\n        // 更新表格选中状态\r\n        if (this.$refs.table) {\r\n          this.$refs.table.clearSelection();\r\n          this.tableData.forEach((row) => {\r\n            this.$refs.table.toggleRowSelection(row, true);\r\n          });\r\n        }\r\n\r\n        console.log(\"全选当前页，触发父组件事件（这会更新保存的勾选数据）\");\r\n        // 触发父组件事件（全选需要追加数据）\r\n        this.$emit(\"selection-change\", this.selectedData, \"select-all\");\r\n      }\r\n    },\r\n\r\n    // 处理取消所有选中（取消选中是当页所有数据都从保存勾选数据中删除）\r\n    handleClearAll() {\r\n      // 清空选中数据\r\n      this.selectedData = [];\r\n\r\n      // 清空表格选中状态\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"取消所有选中，触发父组件事件（这会更新保存的勾选数据）\");\r\n      // 触发父组件事件（取消选中是直接替换为空）\r\n      this.$emit(\"selection-change\", this.selectedData, \"clear-all\");\r\n    },\r\n\r\n    // 处理数据源分类变化（不更新保存的勾选数据）\r\n    handleClassifyChange(value) {\r\n      this.isPagingOperation = true;\r\n      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"数据源分类变化，不更新保存的勾选数据\");\r\n      // 触发父组件事件，只更新左侧列表，不更新右侧列表\r\n      this.$emit(\"classify-change\", value);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理国家筛选变化（不更新保存的勾选数据）\r\n    handleCountryChange(value) {\r\n      this.isPagingOperation = true;\r\n      // 清空选中数据（仅清空界面显示，不更新保存的勾选数据）\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n\r\n      console.log(\"国家筛选变化，不更新保存的勾选数据\");\r\n      // 触发父组件事件，只更新左侧列表，不更新右侧列表\r\n      this.$emit(\"country-change\", value);\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理分页大小变化（不更新保存的勾选数据）\r\n    handleSizeChange(size) {\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"分页大小变化，不更新保存的勾选数据\");\r\n      this.$emit(\"size-change\", size);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      // 延迟重置标记\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 处理当前页变化（不更新保存的勾选数据）\r\n    handleCurrentChange(page) {\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"当前页变化，不更新保存的勾选数据\");\r\n      this.$emit(\"current-change\", page);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      // 延迟重置标记\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 打开链接\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTop() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table && this.$refs.table.bodyWrapper) {\r\n          this.$refs.table.bodyWrapper.scrollTop = 0;\r\n        }\r\n        // 如果表格的 bodyWrapper 不存在，尝试其他方式\r\n        else if (this.$refs.table && this.$refs.table.$el) {\r\n          const tableBody = this.$refs.table.$el.querySelector(\r\n            \".el-table__body-wrapper\"\r\n          );\r\n          if (tableBody) {\r\n            tableBody.scrollTop = 0;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理过滤搜索（不更新保存的勾选数据）\r\n    handleFilterSearch(keyword) {\r\n      // 触发父组件的过滤搜索事件，传递 filterwords 参数\r\n      this.isPagingOperation = true;\r\n      this.selectedData = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      console.log(\"关键字过滤，不更新保存的勾选数据\");\r\n      this.$emit(\"filter-search\", keyword);\r\n      // 滚动到顶部\r\n      this.scrollToTop();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 恢复选中状态（供父组件调用）\r\n    restoreSelection(itemsToSelect) {\r\n      if (!itemsToSelect || itemsToSelect.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 更新内部选中数据\r\n      this.selectedData = itemsToSelect.map((item) => ({ ...item }));\r\n\r\n      // 等待表格渲染完成后设置选中状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          // 先清空选择\r\n          this.$refs.table.clearSelection();\r\n\r\n          // 逐个设置选中状态\r\n          itemsToSelect.forEach((item) => {\r\n            // 在当前表格数据中查找对应的行\r\n            const tableRow = this.tableData.find(\r\n              (row) => row[this.rowKey] === item[this.rowKey]\r\n            );\r\n            if (tableRow) {\r\n              this.$refs.table.toggleRowSelection(tableRow, true);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 静默恢复选中状态（不触发 selection-change 事件）\r\n    restoreSelectionSilently(itemsToSelect) {\r\n      if (!itemsToSelect || itemsToSelect.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 设置标记，避免触发 selection-change 事件\r\n      this.isPagingOperation = true;\r\n\r\n      // 更新内部选中数据\r\n      this.selectedData = itemsToSelect.map((item) => ({ ...item }));\r\n\r\n      // 等待表格渲染完成后设置选中状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          // 先清空选择\r\n          this.$refs.table.clearSelection();\r\n\r\n          // 逐个设置选中状态\r\n          itemsToSelect.forEach((item) => {\r\n            // 在当前表格数据中查找对应的行\r\n            const tableRow = this.tableData.find(\r\n              (row) => row[this.rowKey] === item[this.rowKey]\r\n            );\r\n            if (tableRow) {\r\n              this.$refs.table.toggleRowSelection(tableRow, true);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 延迟重置标记\r\n        setTimeout(() => {\r\n          this.isPagingOperation = false;\r\n        }, 100);\r\n      });\r\n    },\r\n\r\n    // 计算并更新表格高度\r\n    updateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const tableContainer = this.$el.querySelector(\".table-container\");\r\n        if (tableContainer) {\r\n          const containerHeight = tableContainer.clientHeight;\r\n          this.dynamicTableHeight =\r\n            containerHeight > 0 ? containerHeight : \"auto\";\r\n        }\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    // 初始化表格高度\r\n    this.updateTableHeight();\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener(\"resize\", this.updateTableHeight);\r\n  },\r\n  beforeDestroy() {\r\n    // 清理事件监听器\r\n    window.removeEventListener(\"resize\", this.updateTableHeight);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tree-table-container {\r\n  height: calc(100vh - 58px);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.action-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.filter-container {\r\n  flex: 0 0 auto;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.button-container {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.selected-sources-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #e1f5fe;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.selected-text {\r\n  color: #1976d2;\r\n  font-weight: 500;\r\n}\r\n\r\n.table-container {\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n}\r\n\r\n.name-text {\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .country-text {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 4px;\r\n\r\n  .country-tag {\r\n    span {\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n}\r\n\r\n.count-text {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-left: 4px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.treeTable-pagination {\r\n  flex: 0 0 auto;\r\n  text-align: center;\r\n  padding: 10px 0;\r\n}\r\n\r\n.link-icon {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  margin-left: 4px;\r\n  font-size: 18px;\r\n}\r\n\r\n.link-icon:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n::v-deep .el-pagination__sizes {\r\n  margin-top: -3px;\r\n}\r\n\r\n::v-deep .el-table__cell .cell {\r\n  padding: 0 !important;\r\n  margin: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n::v-deep .classify-select {\r\n  .el-input__inner {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n::v-deep .country-select {\r\n  .el-input__inner {\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAsMA;EACAA,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,WAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAM,KAAA;MACAR,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAO,OAAA;MACAT,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACA;IACAS,eAAA;MACAX,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAU,KAAA;EACAb,IAAA,WAAAA,KAAA;IACA;MACAc,UAAA;MACAC,YAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA;MACA,YAAAtB,IAAA;IACA;IACA;IACAuB,iBAAA,WAAAA,kBAAA;MACA;MACA,OACA,KAAAC,MAAA,CAAAC,IAAA,qBACA,MAAAD,MAAA,CAAAE,KAAA,CAAAC,QAAA,IACA,KAAAH,MAAA,CAAAE,KAAA,CAAAE,EAAA;IAEA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,UAAAjB,eAAA,SAAAA,eAAA,CAAAkB,MAAA;QACA;MACA;MAEA,IAAAC,UAAA,QAAAnB,eAAA,CAAAkB,MAAA;MAEA,IAAAC,UAAA;QACA;QACA,IAAAC,KAAA,QAAApB,eAAA,CAAAqB,GAAA,CACA,UAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAApC,IAAA;QAAA,CACA;QACA,wCAAAsC,MAAA,CAAAJ,KAAA,CAAAK,IAAA;MACA;QACA;QACA,IAAAL,MAAA,QAAApB,eAAA,CACA0B,KAAA,OACAL,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAApC,IAAA;QAAA;QACA,IAAAyC,cAAA,GAAAR,UAAA;QACA,wCAAAK,MAAA,CAAAJ,MAAA,CAAAK,IAAA,iBAAAD,MAAA,CAAAG,cAAA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAApB,kBAAA;IACA;EACA;EACAqB,KAAA;IACAzC,IAAA;MACA0C,OAAA,WAAAA,QAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAC,SAAA;UACA;UACAD,KAAA,CAAAE,WAAA;QACA;QACA;QACA,KAAAC,iBAAA;MACA;MACAC,SAAA;IACA;IACAzC,WAAA;MACAoC,OAAA,WAAAA,QAAA;QAAA,IAAAM,MAAA;QACA;QACA,KAAAJ,SAAA;UACAI,MAAA,CAAAH,WAAA;QACA;MACA;IACA;IACA/B,UAAA;MACA4B,OAAA,WAAAA,QAAAO,MAAA;QAAA,IAAAC,MAAA;QACA;QACA,SAAAjC,mBAAA;UACAkC,YAAA,MAAAlC,mBAAA;QACA;;QAEA;QACA,KAAAA,mBAAA,GAAAmC,UAAA;UACAF,MAAA,CAAAG,kBAAA,CAAAJ,MAAA;QACA;MACA;IACA;IACA;IACArC,eAAA;MACA8B,OAAA,WAAAA,QAAA;QACA,KAAAI,iBAAA;MACA;MACAQ,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAD,MAAA,IAAAA,MAAA,CAAAzD,IAAA;QACA;MACA;MAEA2D,OAAA,CAAAC,GAAA,0OAAAJ,GAAA,CAAAtB,KAAA;;MAEA;MACA,IAAA2B,UAAA,QAAA/C,YAAA,CAAAgD,IAAA,CACA,UAAA7B,IAAA;QAAA,OAAAA,IAAA,CAAAyB,MAAA,CAAAvD,MAAA,MAAAqD,GAAA,CAAAE,MAAA,CAAAvD,MAAA;MAAA,CACA;MAEA,IAAA0D,UAAA;QACA;QACA,KAAA/C,YAAA,QAAAA,YAAA,CAAAiD,MAAA,CACA,UAAA9B,IAAA;UAAA,OAAAA,IAAA,CAAAyB,MAAA,CAAAvD,MAAA,MAAAqD,GAAA,CAAAE,MAAA,CAAAvD,MAAA;QAAA,CACA;QACA;QACA,KAAA6D,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAV,GAAA;MACA;QACA;QACA,KAAA1C,YAAA,QAAAqD,cAAA,CAAAjE,OAAA,MAAAsD,GAAA;QACA;QACA,KAAAQ,KAAA,CAAAC,KAAA,CAAAG,cAAA;QACA,KAAAJ,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAV,GAAA;MACA;MAEAG,OAAA,CAAAC,GAAA,2MACA,gEACA,KAAA9C,YAAA,CAAAkB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,KAAA;MAAA,EACA;;MAEA;MACA,KAAAmC,KAAA,0BAAAvD,YAAA;IACA;IAEA;IACAwD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzD,YAAA,GAAAyD,SAAA,CAAAvC,GAAA,WAAAC,IAAA;QAAA,WAAAkC,cAAA,CAAAjE,OAAA,MAAA+B,IAAA;MAAA;;MAEA;MACA,UAAAlB,iBAAA;QACA4C,OAAA,CAAAC,GAAA;QACA,KAAAS,KAAA,0BAAAvD,YAAA;MACA;IACA;IAEA;IACA0D,WAAA,WAAAA,YAAA;MACA;MACA,KAAA3D,UAAA;MACA,KAAAI,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAJ,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAAS,KAAA;MACA;MACA,KAAAzB,WAAA;IACA;IAEA;IACA6B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAArD,SAAA,SAAAA,SAAA,CAAAQ,MAAA;QACA;QACA,KAAAf,YAAA,QAAAO,SAAA,CAAAW,GAAA,WAAAC,IAAA;UAAA,WAAAkC,cAAA,CAAAjE,OAAA,MAAA+B,IAAA;QAAA;;QAEA;QACA,SAAA+B,KAAA,CAAAC,KAAA;UACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;UACA,KAAA/C,SAAA,CAAAsD,OAAA,WAAAnB,GAAA;YACAkB,MAAA,CAAAV,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAV,GAAA;UACA;QACA;QAEAG,OAAA,CAAAC,GAAA;QACA;QACA,KAAAS,KAAA,0BAAAvD,YAAA;MACA;IACA;IAEA;IACA8D,cAAA,WAAAA,eAAA;MACA;MACA,KAAA9D,YAAA;;MAEA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MAEAT,OAAA,CAAAC,GAAA;MACA;MACA,KAAAS,KAAA,0BAAAvD,YAAA;IACA;IAEA;IACA+D,oBAAA,WAAAA,qBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAAhE,iBAAA;MACA;MACA,KAAAD,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MAEAT,OAAA,CAAAC,GAAA;MACA;MACA,KAAAS,KAAA,oBAAAS,KAAA;MACA;MACA,KAAAlC,WAAA;MACA,KAAAD,SAAA;QACAQ,UAAA;UACA4B,MAAA,CAAAhE,iBAAA;QACA;MACA;IACA;IAEA;IACAiE,mBAAA,WAAAA,oBAAAF,KAAA;MAAA,IAAAG,MAAA;MACA,KAAAlE,iBAAA;MACA;MACA,KAAAD,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MAEAT,OAAA,CAAAC,GAAA;MACA;MACA,KAAAS,KAAA,mBAAAS,KAAA;MACA,KAAAnC,SAAA;QACAQ,UAAA;UACA8B,MAAA,CAAAlE,iBAAA;QACA;MACA;IACA;IAEA;IACAmE,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAArE,iBAAA;MACA,KAAAD,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAAS,KAAA,gBAAAc,IAAA;MACA;MACA,KAAAvC,WAAA;MACA;MACA,KAAAD,SAAA;QACAQ,UAAA;UACAiC,MAAA,CAAArE,iBAAA;QACA;MACA;IACA;IAEA;IACAsE,mBAAA,WAAAA,oBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAxE,iBAAA;MACA,KAAAD,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAAS,KAAA,mBAAAiB,IAAA;MACA;MACA,KAAA1C,WAAA;MACA;MACA,KAAAD,SAAA;QACAQ,UAAA;UACAoC,MAAA,CAAAxE,iBAAA;QACA;MACA;IACA;IAEA;IACAyE,OAAA,WAAAA,QAAAC,GAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IAEA;IACA7C,WAAA,WAAAA,YAAA;MAAA,IAAAgD,OAAA;MACA,KAAAjD,SAAA;QACA,IAAAiD,OAAA,CAAA5B,KAAA,CAAAC,KAAA,IAAA2B,OAAA,CAAA5B,KAAA,CAAAC,KAAA,CAAA4B,WAAA;UACAD,OAAA,CAAA5B,KAAA,CAAAC,KAAA,CAAA4B,WAAA,CAAAC,SAAA;QACA;QACA;QAAA,KACA,IAAAF,OAAA,CAAA5B,KAAA,CAAAC,KAAA,IAAA2B,OAAA,CAAA5B,KAAA,CAAAC,KAAA,CAAA8B,GAAA;UACA,IAAAC,SAAA,GAAAJ,OAAA,CAAA5B,KAAA,CAAAC,KAAA,CAAA8B,GAAA,CAAAE,aAAA,CACA,yBACA;UACA,IAAAD,SAAA;YACAA,SAAA,CAAAF,SAAA;UACA;QACA;MACA;IACA;IAEA;IACA1C,kBAAA,WAAAA,mBAAA8C,OAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAApF,iBAAA;MACA,KAAAD,YAAA;MACA,SAAAkD,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAG,cAAA;MACA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAAS,KAAA,kBAAA6B,OAAA;MACA;MACA,KAAAtD,WAAA;MACA,KAAAD,SAAA;QACAQ,UAAA;UACAgD,OAAA,CAAApF,iBAAA;QACA;MACA;IACA;IAEA;IACAqF,gBAAA,WAAAA,iBAAAC,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAD,aAAA,IAAAA,aAAA,CAAAxE,MAAA;QACA;MACA;;MAEA;MACA,KAAAf,YAAA,GAAAuF,aAAA,CAAArE,GAAA,WAAAC,IAAA;QAAA,WAAAkC,cAAA,CAAAjE,OAAA,MAAA+B,IAAA;MAAA;;MAEA;MACA,KAAAU,SAAA;QACA,IAAA2D,OAAA,CAAAtC,KAAA,CAAAC,KAAA;UACA;UACAqC,OAAA,CAAAtC,KAAA,CAAAC,KAAA,CAAAG,cAAA;;UAEA;UACAiC,aAAA,CAAA1B,OAAA,WAAA1C,IAAA;YACA;YACA,IAAAsE,QAAA,GAAAD,OAAA,CAAAjF,SAAA,CAAAmF,IAAA,CACA,UAAAhD,GAAA;cAAA,OAAAA,GAAA,CAAA8C,OAAA,CAAAnG,MAAA,MAAA8B,IAAA,CAAAqE,OAAA,CAAAnG,MAAA;YAAA,CACA;YACA,IAAAoG,QAAA;cACAD,OAAA,CAAAtC,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAqC,QAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAE,wBAAA,WAAAA,yBAAAJ,aAAA;MAAA,IAAAK,OAAA;MACA,KAAAL,aAAA,IAAAA,aAAA,CAAAxE,MAAA;QACA;MACA;;MAEA;MACA,KAAAd,iBAAA;;MAEA;MACA,KAAAD,YAAA,GAAAuF,aAAA,CAAArE,GAAA,WAAAC,IAAA;QAAA,WAAAkC,cAAA,CAAAjE,OAAA,MAAA+B,IAAA;MAAA;;MAEA;MACA,KAAAU,SAAA;QACA,IAAA+D,OAAA,CAAA1C,KAAA,CAAAC,KAAA;UACA;UACAyC,OAAA,CAAA1C,KAAA,CAAAC,KAAA,CAAAG,cAAA;;UAEA;UACAiC,aAAA,CAAA1B,OAAA,WAAA1C,IAAA;YACA;YACA,IAAAsE,QAAA,GAAAG,OAAA,CAAArF,SAAA,CAAAmF,IAAA,CACA,UAAAhD,GAAA;cAAA,OAAAA,GAAA,CAAAkD,OAAA,CAAAvG,MAAA,MAAA8B,IAAA,CAAAyE,OAAA,CAAAvG,MAAA;YAAA,CACA;YACA,IAAAoG,QAAA;cACAG,OAAA,CAAA1C,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAqC,QAAA;YACA;UACA;QACA;;QAEA;QACApD,UAAA;UACAuD,OAAA,CAAA3F,iBAAA;QACA;MACA;IACA;IAEA;IACA8B,iBAAA,WAAAA,kBAAA;MAAA,IAAA8D,OAAA;MACA,KAAAhE,SAAA;QACA,IAAAiE,cAAA,GAAAD,OAAA,CAAAZ,GAAA,CAAAE,aAAA;QACA,IAAAW,cAAA;UACA,IAAAC,eAAA,GAAAD,cAAA,CAAAE,YAAA;UACAH,OAAA,CAAAxF,kBAAA,GACA0F,eAAA,OAAAA,eAAA;QACA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,KAAAlE,iBAAA;;IAEA;IACA6C,MAAA,CAAAsB,gBAAA,gBAAAnE,iBAAA;EACA;EACAoE,aAAA,WAAAA,cAAA;IACA;IACAvB,MAAA,CAAAwB,mBAAA,gBAAArE,iBAAA;EACA;AACA", "ignoreList": []}]}