{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1753944231349}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_work", "_keywords", "_articleHistory", "_splitpanes", "_vuex", "_index2", "_ai", "_marked", "_config", "components", "Splitpanes", "Pane", "TreeTable", "dicts", "data", "loading", "tableLoading", "queryParams", "id", "pageNum", "pageSize", "dateType", "tags", "tagsSubset", "keywords", "isTechnology", "sortMode", "emotion", "<PERSON><PERSON><PERSON>", "total", "treeDataTransfer", "filterText", "checkList", "ArticleList", "checked", "ids", "multiple", "dialogVisible", "reportOptions", "reportId", "tagsList", "tagsList1", "checkAll", "isIndeterminate", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "total1", "historyList1", "showSummary", "treeCurrentPage", "treePageSize", "treeTotal", "initializationCompleted", "searchDebounceTimer", "queryDebounceTimer", "<PERSON><PERSON><PERSON><PERSON>", "isRightFilter", "isLeftReset", "selectedClassify", "savedCheckboxData", "aiDialogVisible", "chatMessages", "isThinking", "userAvatar", "streamingMessage", "markdownOptions", "gfm", "breaks", "headerIds", "mangle", "headerPrefix", "pedantic", "sanitize", "smartLists", "smartypants", "xhtml", "isRequesting", "isAborted", "currentReader", "aiPlatform", "articleAiPrompt", "nodeCheckList", "chartDialogVisible", "chartHtml", "chartLoading", "currentChartIframe", "difyApikey", "article", "chart", "chartPrompt", "globalLoading", "watch", "handler", "newVal", "oldVal", "handleRightFilterChange", "_this", "listKeywords", "parentId", "then", "res", "handleCheckAllTagsSubset", "catch", "error", "console", "$message", "JSON", "stringify", "scrollToTopImmediately", "queryArticleList", "val", "_this2", "api", "getNewBuilt", "sourceType", "code", "message", "type", "closeReport", "computed", "_objectSpread2", "default", "mapGetters", "created", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getConfigKey", "msg", "$store", "getters", "avatar", "Promise", "all", "getArticleHistory", "filter", "item", "initializeData", "roles", "includes", "t0", "stop", "mounted", "methods", "_this4", "_callee2", "_callee2$", "_context2", "queryTreeData", "$nextTick", "queryTreeAndList", "_this5", "_callee3", "savedData", "_callee3$", "_context3", "_toConsumableArray2", "length", "restoreFromSavedCheckboxData", "setTimeout", "_this6", "matchedItems", "for<PERSON>ach", "savedItem", "foundItem", "find", "treeItem", "sourceSn", "push", "$refs", "treeTable", "restoreSelectionSilently", "queryTreeDataWithRestoreFromSaved", "_this7", "_callee4", "_callee4$", "_context4", "handlePagination", "handleHistoryPagination", "getArticleHistory1", "dialogContent", "document", "querySelector", "scrollTop", "_this8", "_callee5", "params", "dataList", "mapData", "_callee5$", "_context5", "platformType", "m", "label", "join", "filterwords", "thinkTankClassification", "monitoringMedium", "sent", "rows", "map", "index", "concat", "Date", "now", "Math", "random", "toString", "substring", "cnName", "count", "articleCount", "orderNum", "country", "countryOf<PERSON><PERSON>in", "url", "finish", "flag", "_this9", "_callee7", "_callee7$", "_context7", "abrupt", "clearTimeout", "_callee6", "articleList", "_callee6$", "_context6", "isSort", "weChatName", "String", "addArticleHistory", "keyword", "esRetrieval", "list", "cnTitle", "changeColor", "title", "trim", "deduplicateArticles", "max", "ceil", "handleSelectionChange", "selectedData", "operationType", "currentPageIds", "filteredCheckList", "filteredSavedData", "combinedCheckList", "combinedSavedData", "deduplicateBySourceSn", "dataArray", "seen", "Set", "has", "add", "handleReset", "treeClear", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "size", "name", "handleFilterSearch", "handleClassifyChange", "classifyValue", "handleCheckedChange", "value", "checkedCount", "handleSearch", "keywordsChange", "handleTableSelectionChange", "selection", "handleCheckAllChange", "toggleAllSelection", "clearSelection", "openReport", "reportSubmit", "_this10", "_callee8", "keyWordList", "_callee8$", "_context8", "listId", "AddReport", "batchDelete", "_this11", "$confirm", "API", "batchRemove", "response", "openTaizhang", "_this12", "addWork", "publishHot", "_this13", "publishEverydayHot", "removeHistory", "_this14", "_callee9", "_callee9$", "_context9", "delArticleHistory", "focus", "showHistoryList", "hideHistoryList", "_this15", "_this16", "listArticleHistory", "clearHistory", "_this17", "_callee10", "_callee10$", "_context10", "cleanArticleHistory", "moreHistory", "_this18", "openNewView", "window", "open", "docId", "has<PERSON><PERSON>ual<PERSON><PERSON>nt", "text", "contentWithoutTags", "replace", "test", "rightMain", "table", "bodyWrapper", "$el", "str", "regex", "Str", "split", "keyitem", "replaceReg", "RegExp", "replaceString", "resultEvent", "_this19", "warning", "zhuangtai", "row", "snapshotUrl", "$msgbox", "showCancelButton", "confirmButtonText", "beforeClose", "_", "__", "done", "downLoadExportKe", "location", "origin", "openUrl", "difyAiChat", "_this20", "_callee11", "_articlesResponse$dat", "selectedArticles", "titles", "articlesResponse", "articlesContent", "aiMessage", "prompt", "reader", "decoder", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "isInThinkTag", "decodeUnicode", "updateContent", "_yield$reader$read", "lastData", "decodedAnswer", "chunk", "newlineIndex", "line", "jsonData", "answer", "_callee11$", "_context11", "cancel", "log", "resolve", "getListByIds", "Error", "_selectedArticles$ind", "_selectedArticles$ind2", "content", "role", "difyAiQa", "ok", "body", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "match", "fromCharCode", "parseInt", "newContent", "renderedContent", "marked", "scrollHeight", "read", "parse", "e", "warn", "decode", "indexOf", "slice", "startsWith", "t1", "t2", "ollamaAiChat", "_this21", "_callee13", "_articlesResponse$dat2", "_aiMessage", "lastUpdateTime", "isThinkContent", "temp<PERSON><PERSON><PERSON>", "processStream", "_callee13$", "_context13", "_selectedArticles$ind3", "_selectedArticles$ind4", "ollamaAiQa", "currentTime", "_ref2", "_callee12", "_yield$reader$read2", "lines", "_iterator", "_step", "_response", "thinkStartIndex", "thinkEndIndex", "_callee12$", "_context12", "_createForOfIteratorHelper2", "s", "n", "f", "apply", "arguments", "deepseekAiChat", "_this22", "_callee14", "_aiMessage2", "_lastUpdateTime", "_yield$reader$read3", "_iterator2", "_step2", "_jsonData$choices", "_callee14$", "_context14", "_selectedArticles$ind5", "_selectedArticles$ind6", "deepseekAiQa", "choices", "delta", "t3", "t4", "closeAiDialog", "articleAiChat", "chartAiChat", "difyChartAiChat", "deepseekChartAiChat", "_this23", "_callee15", "articleResult", "aiResult", "aiData", "content2", "parsedData", "finalHtml", "_callee15$", "_context15", "chartContent", "innerHTML", "AreaInfo", "json", "html", "onload", "onerror", "iframe", "createElement", "style", "width", "height", "border", "display", "overflow", "append<PERSON><PERSON><PERSON>", "contentWindow", "Chart", "chartScript", "src", "head", "executeIframeScripts", "doc", "write", "close", "close<PERSON>hart<PERSON><PERSON><PERSON>", "_this24", "_callee16", "_callee16$", "_context16", "instances", "Object", "values", "instance", "destroy", "_this25", "articles", "titleMap", "Map", "result", "cleanTitle", "get", "set", "originalTitle", "_ref3"], "sources": ["src/views/InfoEscalation/Wechat.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n        />\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"Form\"\r\n            label-width=\"90px\"\r\n            @submit.native.prevent\r\n          >\r\n            <el-form-item label=\"发布日期:\" prop=\"dateType\">\r\n              <el-radio-group v-model=\"queryParams.dateType\" size=\"small\">\r\n                <el-radio-button :label=\"1\">今天</el-radio-button>\r\n                <el-radio-button :label=\"2\">近2天</el-radio-button>\r\n                <el-radio-button :label=\"4\">近7天</el-radio-button>\r\n                <el-radio-button :label=\"5\">近30天</el-radio-button>\r\n                <el-radio-button :label=\"10\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <div style=\"display: flex\">\r\n              <el-form-item\r\n                label=\"小信优选:\"\r\n                prop=\"isTechnology\"\r\n                style=\"margin-right: 20px\"\r\n              >\r\n                <el-radio-group v-model=\"queryParams.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item label=\"小信精选:\" prop=\"emotion\">\r\n                <el-radio-group v-model=\"queryParams.emotion\" size=\"small\">\r\n                  <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                    >选中</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item label=\"检索词库:\" prop=\"tags\">\r\n              <el-radio-group v-model=\"queryParams.tags\" size=\"small\">\r\n                <el-radio :label=\"''\">全部</el-radio>\r\n                <el-radio\r\n                  v-for=\"item in tagsList1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.id\"\r\n                  >{{ item.name }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              style=\"width: 100%; overflow: auto\"\r\n              label=\"\"\r\n              prop=\"tagsSubset\"\r\n              v-if=\"queryParams.tags != ''\"\r\n            >\r\n              <el-checkbox\r\n                style=\"float: left; margin-right: 30px\"\r\n                :indeterminate=\"isIndeterminate\"\r\n                v-model=\"checkAll\"\r\n                @change=\"handleCheckAllTagsSubset\"\r\n                >全选</el-checkbox\r\n              >\r\n              <el-checkbox-group v-model=\"queryParams.tagsSubset\">\r\n                <el-checkbox\r\n                  v-for=\"item in tagsList\"\r\n                  :key=\"item.name\"\r\n                  :label=\"item.name\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item class=\"keyword\" label=\"关键词:\" prop=\"keywords\">\r\n              <el-input\r\n                ref=\"keywordRef\"\r\n                placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                style=\"width: 430px\"\r\n                v-model=\"queryParams.keywords\"\r\n                @focus=\"showHistoryList()\"\r\n                @blur=\"hideHistoryList()\"\r\n                @keyup.enter.native=\"handleSearch()\"\r\n              >\r\n              </el-input>\r\n              <div class=\"history\" v-show=\"showHistory\">\r\n                <div\r\n                  class=\"historyItem\"\r\n                  v-for=\"(history, index) in historyList\"\r\n                  :key=\"index\"\r\n                  v-loading=\"historyLoading\"\r\n                >\r\n                  <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                    {{ history.keyword }}\r\n                  </div>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"removeHistory(history, 1)\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >删除</el-button\r\n                  >\r\n                </div>\r\n                <div class=\"historyItem\">\r\n                  <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"clearHistory()\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >清空</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                style=\"margin-left: 10px; height: 36px\"\r\n                @click=\"handleSearch\"\r\n                >搜索</el-button\r\n              >\r\n            </el-form-item>\r\n            <div class=\"keyword-tip\">\r\n              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n            </div>\r\n          </el-form>\r\n          <div class=\"TopBtnGroup\">\r\n            <div class=\"TopBtnGroup_left\">\r\n              <el-checkbox v-model=\"checked\" @change=\"handleCheckAllChange\"\r\n                >全选</el-checkbox\r\n              >\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  title=\"批量删除文章\"\r\n                  class=\"icon-shanchu\"\r\n                  @click=\"batchDelete\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"icon-shuaxin-copy\"\r\n                  title=\"刷新\"\r\n                  @click=\"handleSearch('refresh')\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p class=\"toolTitle\">\r\n              <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"openReport\" v-hasPermi=\"['result:report:add']\"></i>\r\n            </p> -->\r\n              <!-- <p class=\"toolTitle\">\r\n              <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\"\r\n                v-hasPermi=\"['article:collection:snapshot']\" @click=\"resultEvent()\"></i>\r\n            </p> -->\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"添加到工作台账\"\r\n                  @click=\"openTaizhang\"\r\n                  v-hasPermi=\"['article:work:add']\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document-add\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"发布到每日最新热点\"\r\n                  @click=\"publishHot\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-chat-dot-round\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"Deepseek深度解读\"\r\n                  @click=\"articleAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n                  >Deepseek深度解读</span\r\n                >\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-pie-chart\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"生成Deepseek图表看板\"\r\n                  @click=\"chartAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"chartAiChat\"\r\n                  >生成Deepseek图表看板</span\r\n                >\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <el-checkbox\r\n                v-model=\"showSummary\"\r\n                @change=\"(e) => (showSummary = e)\"\r\n                style=\"margin-right: 10px\"\r\n                >是否显示摘要</el-checkbox\r\n              >\r\n              <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n              <el-select v-model=\"queryParams.sortMode\" size=\"mini\">\r\n                <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n                <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n                <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n                <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n                <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate0\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技无关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate1\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技有关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate2\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为其他</el-button\r\n              >\r\n            </el-col>\r\n          </el-row> -->\r\n          <el-table\r\n            :data=\"ArticleList\"\r\n            style=\"width: 100%; user-select: text\"\r\n            :show-header=\"false\"\r\n            ref=\"table\"\r\n            :height=\"\r\n              'calc(100vh - ' +\r\n              (374 + (queryParams.tags != '' ? 51 : 0)) +\r\n              'px)'\r\n            \"\r\n            @selection-change=\"handleTableSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"35\" align=\"center\" />\r\n            <el-table-column width=\"50\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #080808; font-size: 15px\">\r\n                  {{\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                    scope.$index +\r\n                    1\r\n                  }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"编号\"\r\n              align=\"center\"\r\n              key=\"id\"\r\n              prop=\"id\"\r\n              width=\"100\"\r\n              v-if=\"false\"\r\n            />\r\n            <el-table-column prop=\"title\" label=\"日期\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span class=\"article_title\" @click=\"openNewView(scope.row)\">\r\n                  <span\r\n                    style=\"color: #080808\"\r\n                    v-html=\"scope.row.title || scope.row.cnTitle\"\r\n                  ></span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ \"(\" + scope.row.publishTime + \")\" }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ scope.row.sourceName }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    大模型筛选:{{ scope.row.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                  </span>\r\n                </span>\r\n                <div\r\n                  class=\"ArticlMain\"\r\n                  style=\"\r\n                    display: -webkit-box;\r\n                    -webkit-box-orient: vertical;\r\n                    -webkit-line-clamp: 2;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    word-break: break-all;\r\n                  \"\r\n                  v-if=\"\r\n                    showSummary &&\r\n                    hasActualContent(scope.row.cnSummary || scope.row.summary)\r\n                  \"\r\n                >\r\n                  <span style=\"color: #9b9b9b\">摘要：</span>\r\n                  <span\r\n                    style=\"color: #4b4b4b\"\r\n                    v-html=\"\r\n                      changeColor(\r\n                        scope.row.cnSummary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        ) ||\r\n                          scope.row.summary.replace(\r\n                            /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                            'span'\r\n                          )\r\n                      )\r\n                    \"\r\n                    @click=\"openNewView(scope.row)\"\r\n                  ></span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\">\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"操作\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"></i>\r\n              </template>\r\n            </el-table-column> -->\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"handlePagination\"\r\n            :autoScroll=\"true\"\r\n          />\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"500px\"\r\n      :before-close=\"closeReport\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeReport\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"handleHistoryPagination\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n        :autoScroll=\"true\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek图表看板\"\r\n      :visible.sync=\"chartDialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"closeChartDialog\"\r\n      custom-class=\"chart-dialog\"\r\n      destroy-on-close\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div\r\n        v-if=\"chartDialogVisible\"\r\n        class=\"chart-container\"\r\n        v-loading=\"chartLoading\"\r\n        element-loading-text=\"正在生成图表看板...\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n      >\r\n        <div class=\"chart-content\" ref=\"chartContent\"></div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeChartDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listWork,\r\n  getWork,\r\n  delWork,\r\n  addWork,\r\n  updateWork,\r\n} from \"@/api/article/work\";\r\nimport { listKeywords } from \"@/api/article/keywords\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n  getListByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport { mapGetters } from \"vuex\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  components: { Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableLoading: false, // 表格loading状态\r\n      queryParams: {\r\n        id: 100,\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        dateType: 4,\r\n        tags: \"\",\r\n        tagsSubset: [],\r\n        keywords: \"\",\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      },\r\n      total: 0,\r\n      treeDataTransfer: [], // 原始树形数据\r\n      filterText: \"\", // 左侧树搜索栏\r\n      checkList: [], // 左侧勾选数据\r\n      ArticleList: [], // 列表数据\r\n      checked: false, // 全选\r\n      ids: [], // 选中的数据\r\n      // 非多个禁用\r\n      multiple: true,\r\n      dialogVisible: false, // 添加到报告弹框\r\n      reportOptions: [], // 报告列表\r\n      reportId: \"\", // 已选择的报告\r\n      tagsList: [], // 检索词库二级列表\r\n      tagsList1: [], // 检索词库一级列表\r\n      checkAll: false, // 检索词库全选\r\n      isIndeterminate: true, // 检索词库选了值\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      showSummary: true,\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 初始化完成标记 */\r\n      initializationCompleted: false,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 查询防抖 */\r\n      queryDebounceTimer: null,\r\n      /* 防止重复查询 */\r\n      isQuerying: false,\r\n      /* 标记右侧筛选条件是否发生变化 */\r\n      isRightFilter: false,\r\n      /* 标记左侧树是否重置 */\r\n      isLeftReset: false,\r\n      /* 选中的数据源分类 */\r\n      selectedClassify: \"5\",\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n      nodeCheckList: [],\r\n      chartDialogVisible: false,\r\n      chartHtml: \"\",\r\n      chartLoading: true,\r\n      currentChartIframe: null, // 添加变量跟踪当前iframe\r\n      difyApikey: {\r\n        article: \"\",\r\n        chart: \"\",\r\n      },\r\n      chartPrompt: \"\",\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"queryParams.dateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.tags\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n\r\n        // this.queryParams.tagsSubset = [];\r\n        this.checkAll = true;\r\n        this.isIndeterminate = false;\r\n\r\n        if (newVal != \"\") {\r\n          // 不在这里设置tableLoading，让后续的queryArticleList来处理\r\n          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })\r\n            .then((res) => {\r\n              this.tagsList = res.data;\r\n              this.handleCheckAllTagsSubset(true);\r\n              // this.handleRightFilterChange();\r\n            })\r\n            .catch((error) => {\r\n              console.error(\"获取检索词库失败:\", error);\r\n              this.$message.error(\"获取检索词库失败\");\r\n            });\r\n        } else {\r\n          this.handleRightFilterChange();\r\n        }\r\n      },\r\n    },\r\n    \"queryParams.tagsSubset\": {\r\n      handler(newVal, oldVal) {\r\n        if (\r\n          !this.initializationCompleted ||\r\n          JSON.stringify(newVal) === JSON.stringify(oldVal)\r\n        )\r\n          return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      },\r\n    },\r\n    dialogVisible(val) {\r\n      if (val) {\r\n        api.getNewBuilt({ sourceType: \"1\" }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n            this.closeReport();\r\n          }\r\n        });\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\"]),\r\n  },\r\n  async created() {\r\n    getConfigKey(\"sys.ai.platform\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.aiPlatform = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.articleAiPrompt = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.chartPrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.chartPrompt = res.msg;\r\n      }\r\n    });\r\n    // 获取用户头像\r\n    this.userAvatar = this.$store.getters.avatar;\r\n    try {\r\n      // 先加载基础数据\r\n      Promise.all([\r\n        this.getArticleHistory(),\r\n        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {\r\n          this.tagsList1 = res.data.filter((item) => item.parentId == 0);\r\n        }),\r\n      ]);\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      if (this.roles.includes(\"information\")) {\r\n        this.showSummary = false;\r\n      }\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理右侧筛选条件变化\r\n    handleRightFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handlePagination() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 历史记录分页处理\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n      this.$nextTick(() => {\r\n        const dialogContent = document.querySelector(\".el-dialog__body\");\r\n        if (dialogContent) {\r\n          dialogContent.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          platformType: 0,\r\n          id: this.queryParams.id,\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          m: 1,\r\n          dateType:\r\n            this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n          tags: this.queryParams.tags,\r\n          tagsSubset: this.queryParams.tagsSubset,\r\n          keywords: this.queryParams.keywords,\r\n          isTechnology: this.queryParams.isTechnology,\r\n          emotion: this.queryParams.emotion,\r\n          label: this.queryParams.tagsSubset.join(\",\"),\r\n          // 添加关键字过滤参数\r\n          filterwords: this.filterText || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          hasCache: this.queryParams.hasCache,\r\n        };\r\n\r\n        if (!this.queryParams.tags) {\r\n          params.tagsSubset = [];\r\n          params.label = \"\";\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.queryParams.pageNum,\r\n            pageSize: this.queryParams.pageSize,\r\n            id: this.queryParams.id,\r\n            isSort: this.queryParams.sortMode,\r\n            dateType:\r\n              this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n            tags: this.queryParams.tags,\r\n            tagsSubset: this.queryParams.tagsSubset,\r\n            keywords: this.queryParams.keywords,\r\n            isTechnology: this.queryParams.isTechnology,\r\n            emotion: this.queryParams.emotion,\r\n            label: this.queryParams.tagsSubset.join(\",\"),\r\n            platformType: 0,\r\n          };\r\n\r\n          if (!this.queryParams.tags) {\r\n            params.tagsSubset = [];\r\n            params.label = \"\";\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 1 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.esRetrieval(params);\r\n\r\n          if (res.code == 200) {\r\n            let articleList = res.data.list\r\n              ? res.data.list.map((item) => {\r\n                  item.cnTitle = item.cnTitle\r\n                    ? this.changeColor(item.cnTitle)\r\n                    : null;\r\n                  item.title = this.changeColor(item.title);\r\n                  return item;\r\n                })\r\n              : [];\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.queryParams.keywords ||\r\n              this.queryParams.keywords.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n            this.total = res.data.total || 0;\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=\r\n                this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.queryParams.pageNum = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.queryParams.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.queryParams.pageNum = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤文本，避免触发 handleFilterSearch\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 重置树选择（保留原方法名以兼容）\r\n    treeClear() {\r\n      this.handleReset();\r\n    },\r\n\r\n    // 处理树分页\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库全选处理\r\n    handleCheckAllTagsSubset(val) {\r\n      this.queryParams.tagsSubset = val\r\n        ? this.tagsList.map((item) => item.name)\r\n        : [];\r\n      this.isIndeterminate = false;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 更新过滤文本\r\n      this.filterText = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库多选处理\r\n    handleCheckedChange(value) {\r\n      let checkedCount = value.length;\r\n      this.checkAll = checkedCount === this.tagsList.length;\r\n      this.isIndeterminate =\r\n        checkedCount > 0 && checkedCount < this.tagsList.length;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n      this.handleRightFilterChange();\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch(flag) {\r\n      this.scrollToTopImmediately();\r\n      if (!flag) {\r\n        this.queryParams.pageNum = 1;\r\n      }\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 关键词历史选择\r\n    keywordsChange(item) {\r\n      this.queryParams.keywords = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.queryParams.pageNum = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 右侧表格多选框选中数据\r\n    handleTableSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      if (selection.length == this.ArticleList.length) {\r\n        this.checked = true;\r\n      } else {\r\n        this.checked = false;\r\n      }\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 全选\r\n    handleCheckAllChange(val) {\r\n      if (val) {\r\n        this.$refs[\"table\"].toggleAllSelection();\r\n      } else {\r\n        this.$refs[\"table\"].clearSelection();\r\n      }\r\n    },\r\n    // 打开添加到报告\r\n    openReport() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确定添加到报告\r\n    async reportSubmit() {\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      let keyWordList = this.ids.map((item) => {\r\n        return { reportId: this.reportId, listId: item };\r\n      });\r\n      let res = await api.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.queryArticleList();\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.$refs[\"table\"].clearSelection();\r\n      this.checked = false;\r\n      this.closeReport();\r\n    },\r\n    // 关闭添加到报告\r\n    closeReport() {\r\n      this.reportId = \"\";\r\n      this.dialogVisible = false;\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要删除的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.ids.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 添加到台账\r\n    openTaizhang() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认添加已勾选的数据项到台账统计?\")\r\n        .then(() => {\r\n          addWork(this.ids).then(() => {\r\n            this.$message({ type: \"success\", message: \"添加成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 发布到每日最新热点\r\n    publishHot() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.ids.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 历史记录相关方法\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id]);\r\n      if (type == 1) {\r\n        this.$refs[\"keywordRef\"].focus();\r\n        this.getArticleHistory();\r\n      } else {\r\n        this.getArticleHistory();\r\n        this.getArticleHistory1();\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n\r\n    getArticleHistory() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(\r\n        (response) => {\r\n          this.historyList = response.rows;\r\n          this.historyLoading = false;\r\n        }\r\n      );\r\n    },\r\n\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.$refs[\"keywordRef\"].focus();\r\n      await cleanArticleHistory(1);\r\n      this.getArticleHistory();\r\n    },\r\n\r\n    moreHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.showHistory = false;\r\n      this.historyLoading = true;\r\n      this.getArticleHistory1();\r\n      this.dialogVisible1 = true;\r\n    },\r\n\r\n    getArticleHistory1() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false;\r\n      });\r\n    },\r\n\r\n    // 文章详情\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 检查文本是否有实际内容\r\n    hasActualContent(text) {\r\n      if (!text) return false;\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      if (this.$refs.rightMain) {\r\n        this.$refs.rightMain.scrollTop = 0;\r\n      }\r\n\r\n      if (this.$refs.table) {\r\n        const bodyWrapper = this.$refs.table.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (bodyWrapper) {\r\n          bodyWrapper.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关键字高亮\r\n    changeColor(str) {\r\n      const regex = /<img\\b[^>]*>/gi;\r\n      let Str = str && str.replace(regex, \"\");\r\n      if (\r\n        Str &&\r\n        ((this.queryParams.tags &&\r\n          this.queryParams.tagsSubset &&\r\n          this.queryParams.tagsSubset.length) ||\r\n          this.queryParams.keywords)\r\n      ) {\r\n        let keywords = [\r\n          ...this.queryParams.tagsSubset,\r\n          ...(this.queryParams.keywords\r\n            ? this.queryParams.keywords.split(\",\")\r\n            : []),\r\n        ];\r\n        keywords.forEach((keyitem) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            let replaceString =\r\n              '<span class=\"highlight\" style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n\r\n    // 快照生成\r\n    resultEvent() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message.warning(\"请先选择文章\");\r\n      }\r\n      let ids = this.ids;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (ids.length == 1) {\r\n        let row = this.ArticleList.filter((item) => item.id == ids[0]);\r\n        if (row && row.snapshotUrl) zhuangtai = \"查看\";\r\n        url = row.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        this.$msgbox({\r\n          title: \"提示\",\r\n          message: \"快照正在生成中，请稍后查看\",\r\n          showCancelButton: false,\r\n          confirmButtonText: \"关闭\",\r\n          beforeClose: (_, __, done) => {\r\n            done();\r\n          },\r\n        });\r\n        API.downLoadExportKe(ids)\r\n          .then((response) => {\r\n            if (response.code != 200) {\r\n              this.$message({\r\n                message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                type: \"error\",\r\n              });\r\n            }\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.ids.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n    chartAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyChartAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekChartAiChat();\r\n      }\r\n    },\r\n    // dify图表看板\r\n    async difyChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await difyAiQa(\r\n          articleResult.data.content,\r\n          \"blocking\",\r\n          \"dify.chart.apikey\"\r\n        );\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.answer) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = \"\";\r\n\r\n        try {\r\n          // 尝试解析JSON格式（有些返回可能是JSON字符串）\r\n          const parsedData = JSON.parse(aiData.answer);\r\n          content2 =\r\n            parsedData.answer ||\r\n            parsedData.html ||\r\n            parsedData.content ||\r\n            aiData.answer;\r\n        } catch (e) {\r\n          // 如果不是JSON格式，直接使用原始内容\r\n          content2 = aiData.answer;\r\n        }\r\n\r\n        // 处理思考标记\r\n        const thinkStartIndex = content2.indexOf(\"<think>\");\r\n        const thinkEndIndex = content2.indexOf(\"</think>\");\r\n\r\n        // 提取有效内容\r\n        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {\r\n          // 如果存在思考标记，只取</think>后面的内容\r\n          content2 = content2.substring(thinkEndIndex + 8).trim();\r\n        }\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // deepseek图表看板\r\n    async deepseekChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        const prompt = this.chartPrompt + `\\n\\n${articleResult.data.content}`;\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await deepseekAiQa(prompt, false);\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.choices) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = aiData.choices[0].message.content;\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // 关闭图表对话框\r\n    closeChartDialog() {\r\n      this.isAborted = true;\r\n      this.chartDialogVisible = false;\r\n      this.chartHtml = \"\";\r\n      this.chartLoading = false;\r\n      this.isRequesting = false;\r\n\r\n      // 清理Chart实例\r\n      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {\r\n        try {\r\n          // 尝试销毁所有Chart实例\r\n          if (this.currentChartIframe.contentWindow.Chart) {\r\n            const instances =\r\n              this.currentChartIframe.contentWindow.Chart.instances;\r\n            if (instances) {\r\n              Object.values(instances).forEach((instance) => {\r\n                if (instance && typeof instance.destroy === \"function\") {\r\n                  instance.destroy();\r\n                }\r\n              });\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"清理Chart实例失败:\", e);\r\n        }\r\n      }\r\n\r\n      // 清空图表容器内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      // 清理iframe引用\r\n      if (this.currentChartIframe) {\r\n        try {\r\n          this.currentChartIframe.onload = null;\r\n          this.currentChartIframe.onerror = null;\r\n          this.currentChartIframe = null;\r\n        } catch (e) {\r\n          console.error(\"清理iframe失败:\", e);\r\n        }\r\n      }\r\n    },\r\n    // 执行iframe内的所有内联脚本\r\n    executeIframeScripts(iframe) {\r\n      // 简化后的方法，不再尝试手动执行脚本\r\n      console.log(\"图表iframe已加载，等待自然渲染...\");\r\n\r\n      // 确保所有图表都有机会渲染后再隐藏loading\r\n      setTimeout(() => {\r\n        this.chartLoading = false;\r\n      }, 800);\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input_Fixed {\r\n  width: 100%;\r\n}\r\n\r\n.treeBox {\r\n  // margin-top:70px;\r\n  width: 100%;\r\n  height: calc(100vh - 178px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.rightMain {\r\n  height: calc(100vh - 60px);\r\n  overflow: hidden;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    // background: #dbdbd8;\r\n    // margin-bottom: 20px;\r\n    height: 60px;\r\n    box-shadow: 0 0px 10px 0px #cecdcd;\r\n    border-bottom: solid 1px #e2e2e2;\r\n\r\n    .TopBtnGroup_left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .toolTitle {\r\n      margin: 0 10px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      .deepseek-text {\r\n        color: #5589f5; // 使用与图标相同的颜色\r\n        margin-left: 4px;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      &:nth-of-type(1) {\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ArticlMain {\r\n    padding: 0 0 0 30px;\r\n    color: #3f3f3f;\r\n    font-size: 14px;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .ArticlMain > span:hover {\r\n    color: #1889f3;\r\n    border-bottom: solid 1px #0798f8;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n::v-deep .drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-document:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-pie-chart:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-table td.el-table__cell div {\r\n  padding-left: 10px;\r\n}\r\n\r\n::v-deep .el-table-column--selection .cell {\r\n  padding-right: 0px;\r\n  padding-left: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 0;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 90px;\r\n  line-height: 1;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-table--medium .el-table__cell {\r\n  padding: 10px 0;\r\n}\r\n\r\n.article_title {\r\n  margin-left: 10px;\r\n  font-size: 15px;\r\n}\r\n\r\n.article_title:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n  cursor: pointer;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  min-height: 600px;\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 0;\r\n  position: relative;\r\n\r\n  .chart-content {\r\n    width: 100%;\r\n    height: 600px;\r\n    overflow: auto;\r\n    display: block;\r\n  }\r\n}\r\n\r\n::v-deep .chart-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // padding: 15px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 10px 15px;\r\n    border-top: 1px solid #e4e7ed;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAOA,IAAAE,SAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAH,OAAA;AAOA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,GAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MAAA;MACAC,WAAA;QACAC,EAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,UAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACAC,OAAA;MAAA;MACAC,GAAA;MAAA;MACA;MACAC,QAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,QAAA;MAAA;MACAC,QAAA;MAAA;MACAC,SAAA;MAAA;MACAC,QAAA;MAAA;MACAC,eAAA;MAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACA9B,OAAA;QACAC,QAAA;MACA;MACA8B,MAAA;MACAC,YAAA;MACAC,WAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;MACA;MACAC,kBAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;MACA;MACAC,gBAAA;MACA;MACAC,iBAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,UAAA;MACAC,UAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,GAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,eAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,kBAAA;MAAA;MACAC,UAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAE,KAAA;QACA,UAAA5C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;;QAEA;QACA,KAAAxD,QAAA;QACA,KAAAC,eAAA;QAEA,IAAAsD,MAAA;UACA;UACA,IAAAI,sBAAA;YAAAC,QAAA,EAAAL,MAAA;YAAA9E,OAAA;YAAAC,QAAA;UAAA,GACAmF,IAAA,WAAAC,GAAA;YACAJ,KAAA,CAAA5D,QAAA,GAAAgE,GAAA,CAAA1F,IAAA;YACAsF,KAAA,CAAAK,wBAAA;YACA;UACA,GACAC,KAAA,WAAAC,KAAA;YACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;YACAP,KAAA,CAAAS,QAAA,CAAAF,KAAA;UACA;QACA;UACA,KAAAR,uBAAA;QACA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IACA,MAAA1C,uBAAA,IACAsD,IAAA,CAAAC,SAAA,CAAAd,MAAA,MAAAa,IAAA,CAAAC,SAAA,CAAAb,MAAA,GAEA;QACA,KAAAC,uBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAA1C,uBAAA,IAAAyC,MAAA,KAAAC,MAAA;QACA,KAAAc,sBAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA5E,aAAA,WAAAA,cAAA6E,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,GAAA;QACAE,cAAA,CAAAC,WAAA;UAAAC,UAAA;QAAA,GAAAf,IAAA,WAAAzF,IAAA;UACA,IAAAA,IAAA,CAAAyG,IAAA;YACAJ,MAAA,CAAA7E,aAAA,GAAAxB,IAAA,CAAAA,IAAA;UACA;YACAqG,MAAA,CAAAN,QAAA;cAAAW,OAAA;cAAAC,IAAA;YAAA;YACAN,MAAA,CAAAO,WAAA;UACA;QACA;MACA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,aACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA,IAAAC,oBAAA,qBAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAA7C,UAAA,GAAAqB,GAAA,CAAAmC,GAAA;cACA;YACA;YACA,IAAAD,oBAAA,6BAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAA5C,eAAA,GAAAoB,GAAA,CAAAmC,GAAA;cACA;YACA;YACA,IAAAD,oBAAA,2BAAAnC,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAe,IAAA;gBACAS,MAAA,CAAAnC,WAAA,GAAAW,GAAA,CAAAmC,GAAA;cACA;YACA;YACA;YACAX,MAAA,CAAA7D,UAAA,GAAA6D,MAAA,CAAAY,MAAA,CAAAC,OAAA,CAAAC,MAAA;YAAAP,QAAA,CAAAC,IAAA;YAEA;YACAO,OAAA,CAAAC,GAAA,EACAhB,MAAA,CAAAiB,iBAAA,IACA,IAAA5C,sBAAA;cAAAC,QAAA;cAAAnF,OAAA;cAAAC,QAAA;YAAA,GAAAmF,IAAA,WAAAC,GAAA;cACAwB,MAAA,CAAAvF,SAAA,GAAA+D,GAAA,CAAA1F,IAAA,CAAAoI,MAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA7C,QAAA;cAAA;YACA,GACA;;YAEA;YAAAiC,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAoB,cAAA;UAAA;YAEA,IAAApB,MAAA,CAAAqB,KAAA,CAAAC,QAAA;cACAtB,MAAA,CAAA5E,WAAA;YACA;;YAEA;YACA4E,MAAA,CAAAxE,uBAAA;YAAA+E,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAgB,EAAA,GAAAhB,QAAA;YAEA3B,OAAA,CAAAD,KAAA,aAAA4B,QAAA,CAAAgB,EAAA;YACAvB,MAAA,CAAAnB,QAAA,CAAAF,KAAA;UAAA;UAAA;YAAA,OAAA4B,QAAA,CAAAiB,IAAA;QAAA;MAAA,GAAApB,OAAA;IAAA;EAEA;EAEAqB,OAAA,WAAAA,QAAA;EACAC,OAAA;IACA;IACAN,cAAA,WAAAA,eAAA;MAAA,IAAAO,MAAA;MAAA,WAAA1B,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAyB,SAAA;QAAA,WAAA1B,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAwB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtB,IAAA,GAAAsB,SAAA,CAAArB,IAAA;YAAA;cAAAqB,SAAA,CAAAtB,IAAA;cAEA;cACAmB,MAAA,CAAA1C,gBAAA;cACA;cAAA6C,SAAA,CAAArB,IAAA;cAAA,OACAkB,MAAA,CAAAI,aAAA;YAAA;cAAAD,SAAA,CAAArB,IAAA;cAAA,OAEAkB,MAAA,CAAAK,SAAA;YAAA;cAAAF,SAAA,CAAArB,IAAA;cAAA;YAAA;cAAAqB,SAAA,CAAAtB,IAAA;cAAAsB,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEAlD,OAAA,CAAAD,KAAA,aAAAmD,SAAA,CAAAP,EAAA;cACAI,MAAA,CAAA9C,QAAA,CAAAF,KAAA;YAAA;YAAA;cAAA,OAAAmD,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IAEA;IACAzD,uBAAA,WAAAA,wBAAA;MACA,KAAAvC,aAAA;;MAEA;MACA;;MAEA;MACA,KAAA3C,WAAA,CAAAE,OAAA;MACA,KAAAkC,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAAoF,sBAAA;;MAEA;MACA,KAAAiD,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAgC,SAAA;QAAA,IAAAC,SAAA;QAAA,WAAAlC,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAgC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,IAAA,GAAA8B,SAAA,CAAA7B,IAAA;YAAA;cAAA6B,SAAA,CAAA9B,IAAA;cAEA;cACA4B,SAAA,OAAAG,mBAAA,CAAA1C,OAAA,EAAAqC,MAAA,CAAAnG,iBAAA,GAEA;cACA,IAAAqG,SAAA,IAAAA,SAAA,CAAAI,MAAA;gBACAN,MAAA,CAAAlI,SAAA,OAAAuI,mBAAA,CAAA1C,OAAA,EAAAuC,SAAA;cACA;gBACA;gBACAF,MAAA,CAAAlI,SAAA;cACA;;cAEA;cAAAsI,SAAA,CAAA7B,IAAA;cAAA,OACAM,OAAA,CAAAC,GAAA,EACAkB,MAAA,CAAAH,aAAA,IACAG,MAAA,CAAAjD,gBAAA;cAAA,CACA;YAAA;cAEA;cACAiD,MAAA,CAAAnG,iBAAA,GAAAqG,SAAA;;cAEA;cACA,IAAAF,MAAA,CAAAnG,iBAAA,IAAAmG,MAAA,CAAAnG,iBAAA,CAAAyG,MAAA;gBACAN,MAAA,CAAAO,4BAAA;cACA;;cAEA;cACAP,MAAA,CAAAtG,aAAA;cACA8G,UAAA;gBACAR,MAAA,CAAArG,WAAA;cACA;cAAAyG,SAAA,CAAA7B,IAAA;cAAA;YAAA;cAAA6B,SAAA,CAAA9B,IAAA;cAAA8B,SAAA,CAAAf,EAAA,GAAAe,SAAA;cAEA1D,OAAA,CAAAD,KAAA,gBAAA2D,SAAA,CAAAf,EAAA;cACAW,MAAA,CAAArD,QAAA,CAAAF,KAAA;cACA;cACAuD,MAAA,CAAAtG,aAAA;cACA8G,UAAA;gBACAR,MAAA,CAAArG,WAAA;cACA;YAAA;YAAA;cAAA,OAAAyG,SAAA,CAAAd,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAEA;IAEA;IAEA;IACAM,4BAAA,WAAAA,6BAAA;MAAA,IAAAE,MAAA;MACA,UAAA5G,iBAAA,SAAAA,iBAAA,CAAAyG,MAAA;QACA;MACA;;MAEA;MACA,IAAAI,YAAA;MACA,KAAA7G,iBAAA,CAAA8G,OAAA,WAAAC,SAAA;QACA,IAAAC,SAAA,GAAAJ,MAAA,CAAA7I,gBAAA,CAAAkJ,IAAA,CACA,UAAAC,QAAA;UAAA,OAAAA,QAAA,CAAAC,QAAA,KAAAJ,SAAA,CAAAI,QAAA;QAAA,CACA;QACA,IAAAH,SAAA;UACAH,YAAA,CAAAO,IAAA,CAAAJ,SAAA;QACA;MACA;MAEA,IAAAH,YAAA,CAAAJ,MAAA;QACA;QACA,KAAAxI,SAAA,GAAA4I,YAAA;QACA;QACA,KAAAZ,SAAA;UACA,IAAAW,MAAA,CAAAS,KAAA,CAAAC,SAAA;YACAV,MAAA,CAAAS,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAV,YAAA;UACA;QACA;MACA;QACA;QACA,KAAA5I,SAAA;MACA;IACA;IAEA;IAEA;IACAuJ,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvD,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAsD,SAAA;QAAA,WAAAvD,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cAAAkD,SAAA,CAAAnD,IAAA;cAEA;cACA,IAAAgD,MAAA,CAAAzH,iBAAA,IAAAyH,MAAA,CAAAzH,iBAAA,CAAAyG,MAAA;gBACAgB,MAAA,CAAAxJ,SAAA,OAAAuI,mBAAA,CAAA1C,OAAA,EAAA2D,MAAA,CAAAzH,iBAAA;cACA;gBACAyH,MAAA,CAAAxJ,SAAA;cACA;;cAEA;cAAA2J,SAAA,CAAAlD,IAAA;cAAA,OACA+C,MAAA,CAAAzB,aAAA;YAAA;cAEA;cACA,IAAAyB,MAAA,CAAAzH,iBAAA,IAAAyH,MAAA,CAAAzH,iBAAA,CAAAyG,MAAA;gBACAgB,MAAA,CAAAf,4BAAA;cACA;cAAAkB,SAAA,CAAAlD,IAAA;cAAA;YAAA;cAAAkD,SAAA,CAAAnD,IAAA;cAAAmD,SAAA,CAAApC,EAAA,GAAAoC,SAAA;cAEA/E,OAAA,CAAAD,KAAA,CACA,6BAAAgF,SAAA,CAAApC,EAEA;YAAA;YAAA;cAAA,OAAAoC,SAAA,CAAAnC,IAAA;UAAA;QAAA,GAAAiC,QAAA;MAAA;IAEA;IAEA;IACAG,gBAAA,WAAAA,iBAAA;MACA,KAAA5E,sBAAA;MACA,KAAAC,gBAAA;IACA;IAEA;IACA4E,uBAAA,WAAAA,wBAAA;MACA,KAAAC,kBAAA;MACA,KAAA9B,SAAA;QACA,IAAA+B,aAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,aAAA;UACAA,aAAA,CAAAG,SAAA;QACA;MACA;IACA;IAEA;IACAnC,aAAA,WAAAA,cAAA;MAAA,IAAAoC,MAAA;MAAA,WAAAlE,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAiE,SAAA;QAAA,IAAAC,MAAA,EAAA7F,GAAA,EAAA8F,QAAA,EAAAzK,KAAA,EAAA0K,OAAA;QAAA,WAAArE,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAmE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,IAAA,GAAAiE,SAAA,CAAAhE,IAAA;YAAA;cACA0D,MAAA,CAAApL,OAAA;cAAA0L,SAAA,CAAAjE,IAAA;cAEA6D,MAAA;gBACAK,YAAA;gBACAxL,EAAA,EAAAiL,MAAA,CAAAlL,WAAA,CAAAC,EAAA;gBACAC,OAAA,EAAAgL,MAAA,CAAA9I,eAAA;gBACAjC,QAAA,EAAA+K,MAAA,CAAA7I,YAAA;gBACAqJ,CAAA;gBACAtL,QAAA,EACA8K,MAAA,CAAAlL,WAAA,CAAAI,QAAA,QAAA8K,MAAA,CAAAlL,WAAA,CAAAI,QAAA;gBACAC,IAAA,EAAA6K,MAAA,CAAAlL,WAAA,CAAAK,IAAA;gBACAC,UAAA,EAAA4K,MAAA,CAAAlL,WAAA,CAAAM,UAAA;gBACAC,QAAA,EAAA2K,MAAA,CAAAlL,WAAA,CAAAO,QAAA;gBACAC,YAAA,EAAA0K,MAAA,CAAAlL,WAAA,CAAAQ,YAAA;gBACAE,OAAA,EAAAwK,MAAA,CAAAlL,WAAA,CAAAU,OAAA;gBACAiL,KAAA,EAAAT,MAAA,CAAAlL,WAAA,CAAAM,UAAA,CAAAsL,IAAA;gBACA;gBACAC,WAAA,EAAAX,MAAA,CAAApK,UAAA;gBACA;gBACAgL,uBAAA,EAAAZ,MAAA,CAAArI,gBAAA;gBACAlC,QAAA,EAAAuK,MAAA,CAAAlL,WAAA,CAAAW;cACA;cAEA,KAAAuK,MAAA,CAAAlL,WAAA,CAAAK,IAAA;gBACA+K,MAAA,CAAA9K,UAAA;gBACA8K,MAAA,CAAAO,KAAA;cACA;cAAAH,SAAA,CAAAhE,IAAA;cAAA,OAEArB,cAAA,CAAA4F,gBAAA,CAAAX,MAAA;YAAA;cAAA7F,GAAA,GAAAiG,SAAA,CAAAQ,IAAA;cAEA,IAAAzG,GAAA,CAAAe,IAAA;gBACA+E,QAAA,GAAA9F,GAAA,CAAA0G,IAAA;gBACArL,KAAA,GAAA2E,GAAA,CAAA3E,KAAA;gBAEA0K,OAAA,YAAAA,QAAAzL,IAAA;kBAAA,OACAA,IAAA,CAAAqM,GAAA,WAAAhE,IAAA,EAAAiE,KAAA;oBAAA;sBACAlM,EAAA,KAAAmM,MAAA,CACAlE,IAAA,CAAA+B,QAAA,oBAAAmC,MAAA,CACAD,KAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA,SAAAF,MAAA,CAAAG,IAAA,CAAAC,MAAA,GACAC,QAAA,KACAC,SAAA;sBAAA;sBACAf,KAAA,EAAAzD,IAAA,CAAAyE,MAAA;sBACAC,KAAA,EAAA1E,IAAA,CAAA2E,YAAA;sBACAC,QAAA,EAAA5E,IAAA,CAAA4E,QAAA;sBACAC,OAAA,EAAA7E,IAAA,CAAA8E,eAAA;sBACA/C,QAAA,EAAA/B,IAAA,CAAA+B,QAAA;sBACAgD,GAAA,EAAA/E,IAAA,CAAA+E,GAAA;oBACA;kBAAA;gBAAA;gBAEA/B,MAAA,CAAArK,gBAAA,GAAAyK,OAAA,CAAAD,QAAA;gBACAH,MAAA,CAAA5I,SAAA,GAAA1B,KAAA;cACA;cAAA4K,SAAA,CAAAhE,IAAA;cAAA;YAAA;cAAAgE,SAAA,CAAAjE,IAAA;cAAAiE,SAAA,CAAAlD,EAAA,GAAAkD,SAAA;cAEA7F,OAAA,CAAAD,KAAA,aAAA8F,SAAA,CAAAlD,EAAA;cACA4C,MAAA,CAAAtF,QAAA,CAAAF,KAAA;YAAA;cAAA8F,SAAA,CAAAjE,IAAA;cAEA2D,MAAA,CAAApL,OAAA;cAAA,OAAA0L,SAAA,CAAA0B,MAAA;YAAA;YAAA;cAAA,OAAA1B,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA4C,QAAA;MAAA;IAEA;IAEA;IACAnF,gBAAA,WAAAA,iBAAAmH,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAApG,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAmG,SAAA;QAAA,WAAApG,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhG,IAAA,GAAAgG,SAAA,CAAA/F,IAAA;YAAA;cAAA,KAEA4F,MAAA,CAAA1K,UAAA;gBAAA6K,SAAA,CAAA/F,IAAA;gBAAA;cAAA;cAAA,OAAA+F,SAAA,CAAAC,MAAA;YAAA;cAIA,KAAAL,IAAA;gBACAC,MAAA,CAAArN,YAAA;cACA;;cAEA;cACA,IAAAqN,MAAA,CAAA3K,kBAAA;gBACAgL,YAAA,CAAAL,MAAA,CAAA3K,kBAAA;cACA;;cAEA;cACA2K,MAAA,CAAA3K,kBAAA,GAAAgH,UAAA,kBAAAzC,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAwG,SAAA;gBAAA,IAAAtC,MAAA,EAAAvL,IAAA,EAAAoK,QAAA,EAAA1E,GAAA,EAAAoI,WAAA;gBAAA,WAAA1G,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAwG,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAtG,IAAA,GAAAsG,SAAA,CAAArG,IAAA;oBAAA;sBAAAqG,SAAA,CAAAtG,IAAA;sBAEA,IAAA4F,IAAA;wBACAC,MAAA,CAAAvI,aAAA;sBACA;sBAEAuI,MAAA,CAAA1K,UAAA;sBAEA0I,MAAA;wBACAM,CAAA;wBACAxL,OAAA,EAAAkN,MAAA,CAAApN,WAAA,CAAAE,OAAA;wBACAC,QAAA,EAAAiN,MAAA,CAAApN,WAAA,CAAAG,QAAA;wBACAF,EAAA,EAAAmN,MAAA,CAAApN,WAAA,CAAAC,EAAA;wBACA6N,MAAA,EAAAV,MAAA,CAAApN,WAAA,CAAAS,QAAA;wBACAL,QAAA,EACAgN,MAAA,CAAApN,WAAA,CAAAI,QAAA,QAAAgN,MAAA,CAAApN,WAAA,CAAAI,QAAA;wBACAC,IAAA,EAAA+M,MAAA,CAAApN,WAAA,CAAAK,IAAA;wBACAC,UAAA,EAAA8M,MAAA,CAAApN,WAAA,CAAAM,UAAA;wBACAC,QAAA,EAAA6M,MAAA,CAAApN,WAAA,CAAAO,QAAA;wBACAC,YAAA,EAAA4M,MAAA,CAAApN,WAAA,CAAAQ,YAAA;wBACAE,OAAA,EAAA0M,MAAA,CAAApN,WAAA,CAAAU,OAAA;wBACAiL,KAAA,EAAAyB,MAAA,CAAApN,WAAA,CAAAM,UAAA,CAAAsL,IAAA;wBACAH,YAAA;sBACA;sBAEA,KAAA2B,MAAA,CAAApN,WAAA,CAAAK,IAAA;wBACA+K,MAAA,CAAA9K,UAAA;wBACA8K,MAAA,CAAAO,KAAA;sBACA;;sBAEA;sBACA,IAAAyB,MAAA,CAAAtK,iBAAA,IAAAsK,MAAA,CAAAtK,iBAAA,CAAAyG,MAAA;wBACA1J,IAAA,GAAAuN,MAAA,CAAAtK,iBAAA,CAAAoJ,GAAA,WAAAhE,IAAA;0BAAA,OAAAA,IAAA,CAAAyD,KAAA;wBAAA;wBACA1B,QAAA,GAAAmD,MAAA,CAAAtK,iBAAA,CAAAoJ,GAAA,CACA,UAAAhE,IAAA;0BAAA,OAAAA,IAAA,CAAA+B,QAAA;wBAAA,CACA;wBAEAmB,MAAA,CAAA2C,UAAA,GAAAC,MAAA,CAAAnO,IAAA;wBACAuL,MAAA,CAAAnB,QAAA,GAAA+D,MAAA,CAAA/D,QAAA;sBACA;;sBAEA;sBACA,IAAAmB,MAAA,CAAA7K,QAAA;wBACA,IAAA0N,iCAAA;0BAAAC,OAAA,EAAA9C,MAAA,CAAA7K,QAAA;0BAAAiG,IAAA;wBAAA,GAAAlB,IAAA,CACA;0BACA8H,MAAA,CAAApF,iBAAA;wBACA,CACA;sBACA;sBAAA6F,SAAA,CAAArG,IAAA;sBAAA,OAEArB,cAAA,CAAAgI,WAAA,CAAA/C,MAAA;oBAAA;sBAAA7F,GAAA,GAAAsI,SAAA,CAAA7B,IAAA;sBAAA,MAEAzG,GAAA,CAAAe,IAAA;wBAAAuH,SAAA,CAAArG,IAAA;wBAAA;sBAAA;sBACAmG,WAAA,GAAApI,GAAA,CAAA1F,IAAA,CAAAuO,IAAA,GACA7I,GAAA,CAAA1F,IAAA,CAAAuO,IAAA,CAAAlC,GAAA,WAAAhE,IAAA;wBACAA,IAAA,CAAAmG,OAAA,GAAAnG,IAAA,CAAAmG,OAAA,GACAjB,MAAA,CAAAkB,WAAA,CAAApG,IAAA,CAAAmG,OAAA,IACA;wBACAnG,IAAA,CAAAqG,KAAA,GAAAnB,MAAA,CAAAkB,WAAA,CAAApG,IAAA,CAAAqG,KAAA;wBACA,OAAArG,IAAA;sBACA,KACA,IAEA;sBACA,IACA,CAAAkF,MAAA,CAAApN,WAAA,CAAAO,QAAA,IACA6M,MAAA,CAAApN,WAAA,CAAAO,QAAA,CAAAiO,IAAA,WACA;wBACAb,WAAA,GAAAP,MAAA,CAAAqB,mBAAA,CAAAd,WAAA;sBACA;sBAEAP,MAAA,CAAApM,WAAA,GAAA2M,WAAA;sBACAP,MAAA,CAAAxM,KAAA,GAAA2E,GAAA,CAAA1F,IAAA,CAAAe,KAAA;;sBAEA;sBACA,IAAAwM,MAAA,CAAAtK,iBAAA,IAAAsK,MAAA,CAAAtK,iBAAA,CAAAyG,MAAA;wBACA6D,MAAA,CAAA5D,4BAAA;sBACA;;sBAEA;sBAAA,MAEA4D,MAAA,CAAApM,WAAA,CAAAuI,MAAA,SACA6D,MAAA,CAAApN,WAAA,CAAAG,QAAA,IAAAiN,MAAA,CAAApN,WAAA,CAAAE,OAAA,SACAkN,MAAA,CAAAxM,KAAA,IACAwM,MAAA,CAAAxM,KAAA;wBAAAiN,SAAA,CAAArG,IAAA;wBAAA;sBAAA;sBAEA4F,MAAA,CAAApN,WAAA,CAAAE,OAAA,GAAAqM,IAAA,CAAAmC,GAAA,CACA,GACAnC,IAAA,CAAAoC,IAAA,CAAAvB,MAAA,CAAAxM,KAAA,GAAAwM,MAAA,CAAApN,WAAA,CAAAG,QAAA,CACA;sBACA;sBAAA0N,SAAA,CAAArG,IAAA;sBAAA,OACA4F,MAAA,CAAApH,gBAAA;oBAAA;sBAAA,OAAA6H,SAAA,CAAAL,MAAA;oBAAA;sBAAAK,SAAA,CAAArG,IAAA;sBAAA;oBAAA;sBAIA4F,MAAA,CAAAxH,QAAA,CAAAF,KAAA,CAAAH,GAAA,CAAAmC,GAAA;oBAAA;sBAAAmG,SAAA,CAAArG,IAAA;sBAAA;oBAAA;sBAAAqG,SAAA,CAAAtG,IAAA;sBAAAsG,SAAA,CAAAvF,EAAA,GAAAuF,SAAA;sBAGAlI,OAAA,CAAAD,KAAA,cAAAmI,SAAA,CAAAvF,EAAA;sBACA8E,MAAA,CAAAxH,QAAA,CAAAF,KAAA;oBAAA;sBAAAmI,SAAA,CAAAtG,IAAA;sBAEA6F,MAAA,CAAA1K,UAAA;sBACA0K,MAAA,CAAAvI,aAAA;sBACAuI,MAAA,CAAArN,YAAA;sBAAA,OAAA8N,SAAA,CAAAX,MAAA;oBAAA;oBAAA;sBAAA,OAAAW,SAAA,CAAAtF,IAAA;kBAAA;gBAAA,GAAAmF,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA8E,QAAA;MAAA;IACA;IAEA;IAEA;IACAuB,qBAAA,WAAAA,sBAAAC,YAAA,EAAAC,aAAA;MACA,IAAAA,aAAA,oBAAAA,aAAA;QACA;QACA,KAAA/N,SAAA,OAAAuI,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;QACA,KAAA/L,iBAAA,OAAAwG,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;MACA,WACAC,aAAA,0BACAA,aAAA,mBACA;QACA;QACA;QACA,IAAAC,cAAA,QAAAlO,gBAAA,CAAAqL,GAAA,CACA,UAAAhE,IAAA;UAAA,OAAAA,IAAA,CAAA+B,QAAA;QAAA,CACA;QACA,IAAA+E,iBAAA,QAAAjO,SAAA,CAAAkH,MAAA,CACA,UAAAC,IAAA;UAAA,QAAA6G,cAAA,CAAA1G,QAAA,CAAAH,IAAA,CAAA+B,QAAA;QAAA,CACA;QACA,IAAAgF,iBAAA,QAAAnM,iBAAA,CAAAmF,MAAA,CACA,UAAAC,IAAA;UAAA,QAAA6G,cAAA,CAAA1G,QAAA,CAAAH,IAAA,CAAA+B,QAAA;QAAA,CACA;;QAEA;QACA,IAAAiF,iBAAA,MAAA9C,MAAA,KAAA9C,mBAAA,CAAA1C,OAAA,EAAAoI,iBAAA,OAAA1F,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;QACA,IAAAM,iBAAA,MAAA/C,MAAA,KAAA9C,mBAAA,CAAA1C,OAAA,EAAAqI,iBAAA,OAAA3F,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;;QAEA;QACA,KAAA9N,SAAA,QAAAqO,qBAAA,CAAAF,iBAAA;QACA,KAAApM,iBAAA,QAAAsM,qBAAA,CAAAD,iBAAA;MACA;QACA;QACA,KAAApO,SAAA,OAAAuI,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;QACA,KAAA/L,iBAAA,OAAAwG,mBAAA,CAAA1C,OAAA,EAAAiI,YAAA;MACA;;MAEA;MACA,KAAA7O,WAAA,CAAAE,OAAA;MACA,KAAA6F,sBAAA;MACA,UAAApD,aAAA;QACA,KAAAqD,gBAAA;MACA;IACA;IAEA;IACAoJ,qBAAA,WAAAA,sBAAAC,SAAA;MACA,IAAAC,IAAA,OAAAC,GAAA;MACA,OAAAF,SAAA,CAAApH,MAAA,WAAAC,IAAA;QACA,IAAAoH,IAAA,CAAAE,GAAA,CAAAtH,IAAA,CAAA+B,QAAA;UACA;QACA;QACAqF,IAAA,CAAAG,GAAA,CAAAvH,IAAA,CAAA+B,QAAA;QACA;MACA;IACA;IAEA;IACAyF,WAAA,WAAAA,YAAA;MACA;MACA,KAAA5O,UAAA;MACA,KAAA+B,gBAAA;;MAEA;MACA,KAAAD,WAAA;;MAEA;MACA,KAAA7B,SAAA;;MAEA;MACA,KAAA+B,iBAAA;;MAEA;MACA,KAAA9C,WAAA,CAAAE,OAAA;MACA,KAAAkC,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;MACA,KAAAoF,sBAAA;;MAEA;MACA,KAAAiD,gBAAA;IACA;IAEA;IACA2G,SAAA,WAAAA,UAAA;MACA,KAAAD,WAAA;IACA;IAEA;IACAE,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAzN,eAAA,GAAAyN,IAAA;MACA,KAAA7P,WAAA,CAAAW,QAAA;MACA,KAAA2J,iCAAA;IACA;IAEAwF,wBAAA,WAAAA,yBAAAC,IAAA;MACA,KAAA1N,YAAA,GAAA0N,IAAA;MACA,KAAA3N,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;MACA,KAAA2J,iCAAA;IACA;IAEA;IACA9E,wBAAA,WAAAA,yBAAAS,GAAA;MACA,KAAAjG,WAAA,CAAAM,UAAA,GAAA2F,GAAA,GACA,KAAA1E,QAAA,CAAA2K,GAAA,WAAAhE,IAAA;QAAA,OAAAA,IAAA,CAAA8H,IAAA;MAAA,KACA;MACA,KAAAtO,eAAA;MACA,KAAA1B,WAAA,CAAAE,OAAA;MAEA,UAAAqC,uBAAA;MAEA,KAAAwD,sBAAA;IACA;IAEA;IACAkK,kBAAA,WAAAA,mBAAA/B,OAAA;MACA,SAAAtL,WAAA;QACA;MACA;;MAEA;MACA,KAAA9B,UAAA,GAAAoN,OAAA;;MAEA;MACA,KAAA9L,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAA2J,iCAAA;IACA;IAEA;IACA4F,oBAAA,WAAAA,qBAAAC,aAAA;MACA;MACA,KAAAtN,gBAAA,GAAAsN,aAAA;;MAEA;MACA,KAAA/N,eAAA;MACA,KAAApC,WAAA,CAAAW,QAAA;;MAEA;MACA,KAAA2J,iCAAA;IACA;IAEA;IACA8F,mBAAA,WAAAA,oBAAAC,KAAA;MACA,IAAAC,YAAA,GAAAD,KAAA,CAAA9G,MAAA;MACA,KAAA9H,QAAA,GAAA6O,YAAA,UAAA/O,QAAA,CAAAgI,MAAA;MACA,KAAA7H,eAAA,GACA4O,YAAA,QAAAA,YAAA,QAAA/O,QAAA,CAAAgI,MAAA;MACA,KAAAvJ,WAAA,CAAAE,OAAA;MAEA,UAAAqC,uBAAA;MAEA,KAAAwD,sBAAA;MACA,KAAAb,uBAAA;IACA;IAEA;IACAqL,YAAA,WAAAA,aAAApD,IAAA;MACA,KAAApH,sBAAA;MACA,KAAAoH,IAAA;QACA,KAAAnN,WAAA,CAAAE,OAAA;MACA;MACA,KAAA8F,gBAAA;IACA;IAEA;IACAwK,cAAA,WAAAA,eAAAtI,IAAA;MACA,KAAAlI,WAAA,CAAAO,QAAA,GAAA2H,IAAA,CAAAgG,OAAA;MACA,KAAApM,cAAA;MACA,KAAAiE,sBAAA;MACA,KAAA/F,WAAA,CAAAE,OAAA;MACA;MACA,KAAA8F,gBAAA;IACA;IAEA;IACAyK,0BAAA,WAAAA,2BAAAC,SAAA;MACA,KAAAxP,GAAA,GAAAwP,SAAA,CAAAxE,GAAA,WAAAhE,IAAA;QAAA,OAAAA,IAAA,CAAAjI,EAAA;MAAA;MACA,IAAAyQ,SAAA,CAAAnH,MAAA,SAAAvI,WAAA,CAAAuI,MAAA;QACA,KAAAtI,OAAA;MACA;QACA,KAAAA,OAAA;MACA;MACA,KAAAE,QAAA,IAAAuP,SAAA,CAAAnH,MAAA;IACA;IACA;IACAoH,oBAAA,WAAAA,qBAAA1K,GAAA;MACA,IAAAA,GAAA;QACA,KAAAkE,KAAA,UAAAyG,kBAAA;MACA;QACA,KAAAzG,KAAA,UAAA0G,cAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAA5P,GAAA,CAAAqI,MAAA;QACA,YAAA3D,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAApF,aAAA;IACA;IACA;IACA2P,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhK,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA+J,SAAA;QAAA,IAAAC,WAAA,EAAA3L,GAAA;QAAA,WAAA0B,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA+J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7J,IAAA,GAAA6J,SAAA,CAAA5J,IAAA;YAAA;cAAA,IACAwJ,OAAA,CAAA1P,QAAA;gBAAA8P,SAAA,CAAA5J,IAAA;gBAAA;cAAA;cAAA,OAAA4J,SAAA,CAAA5D,MAAA,WACAwD,OAAA,CAAApL,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cACA0K,WAAA,GAAAF,OAAA,CAAA9P,GAAA,CAAAgL,GAAA,WAAAhE,IAAA;gBACA;kBAAA5G,QAAA,EAAA0P,OAAA,CAAA1P,QAAA;kBAAA+P,MAAA,EAAAnJ;gBAAA;cACA;cAAAkJ,SAAA,CAAA5J,IAAA;cAAA,OACArB,cAAA,CAAAmL,SAAA,CAAAJ,WAAA;YAAA;cAAA3L,GAAA,GAAA6L,SAAA,CAAApF,IAAA;cACA,IAAAzG,GAAA,CAAAe,IAAA;gBACA0K,OAAA,CAAApL,QAAA;kBAAAW,OAAA;kBAAAC,IAAA;gBAAA;gBACAwK,OAAA,CAAAhL,gBAAA;cACA;gBACAgL,OAAA,CAAApL,QAAA;kBACAW,OAAA;kBACAC,IAAA;gBACA;cACA;cACAwK,OAAA,CAAA7G,KAAA,UAAA0G,cAAA;cACAG,OAAA,CAAA/P,OAAA;cACA+P,OAAA,CAAAvK,WAAA;YAAA;YAAA;cAAA,OAAA2K,SAAA,CAAA7I,IAAA;UAAA;QAAA,GAAA0I,QAAA;MAAA;IACA;IACA;IACAxK,WAAA,WAAAA,YAAA;MACA,KAAAnF,QAAA;MACA,KAAAF,aAAA;IACA;IACA;IACAmQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAAtQ,GAAA,CAAAqI,MAAA;QACA,YAAA3D,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAAiL,QAAA,mBACAnM,IAAA;QACAoM,cAAA,CAAAC,WAAA,CAAAH,OAAA,CAAAtQ,GAAA,CAAA0K,IAAA,OAAAtG,IAAA,WAAAsM,QAAA;UACAJ,OAAA,CAAA5L,QAAA;YAAAW,OAAA;YAAAC,IAAA;UAAA;UACAgL,OAAA,CAAAxL,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACAoM,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,SAAA5Q,GAAA,CAAAqI,MAAA;QACA,YAAA3D,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAAiL,QAAA,wBACAnM,IAAA;QACA,IAAAyM,aAAA,EAAAD,OAAA,CAAA5Q,GAAA,EAAAoE,IAAA;UACAwM,OAAA,CAAAlM,QAAA;YAAAY,IAAA;YAAAD,OAAA;UAAA;UACAuL,OAAA,CAAA9L,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACAuM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,SAAA/Q,GAAA,CAAAqI,MAAA;QACA,YAAA3D,QAAA;UACAW,OAAA;UACAC,IAAA;QACA;MACA;MACA,KAAAiL,QAAA,0BACAnM,IAAA;QACAoM,cAAA,CAAAQ,kBAAA,CAAAD,OAAA,CAAA/Q,GAAA,CAAA0K,IAAA,OAAAtG,IAAA;UACA2M,OAAA,CAAArM,QAAA;YAAAY,IAAA;YAAAD,OAAA;UAAA;UACA0L,OAAA,CAAAjM,gBAAA;QACA;MACA,GACAP,KAAA;IACA;IACA;IACA0M,aAAA,WAAAA,cAAAjK,IAAA,EAAA1B,IAAA;MAAA,IAAA4L,OAAA;MAAA,WAAApL,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAmL,SAAA;QAAA,WAAApL,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhL,IAAA,GAAAgL,SAAA,CAAA/K,IAAA;YAAA;cACAiG,YAAA,CAAA2E,OAAA,CAAAvQ,cAAA;cAAA0Q,SAAA,CAAA/K,IAAA;cAAA,OACA,IAAAgL,iCAAA,GAAAtK,IAAA,CAAAjI,EAAA;YAAA;cACA,IAAAuG,IAAA;gBACA4L,OAAA,CAAAjI,KAAA,eAAAsI,KAAA;gBACAL,OAAA,CAAApK,iBAAA;cACA;gBACAoK,OAAA,CAAApK,iBAAA;gBACAoK,OAAA,CAAAvH,kBAAA;cACA;YAAA;YAAA;cAAA,OAAA0H,SAAA,CAAAhK,IAAA;UAAA;QAAA,GAAA8J,QAAA;MAAA;IACA;IAEAK,eAAA,WAAAA,gBAAA;MACA,KAAA/Q,WAAA;IACA;IAEAgR,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,KAAA/Q,cAAA,GAAA4H,UAAA;QACAmJ,OAAA,CAAAjR,WAAA;MACA;IACA;IAEAqG,iBAAA,WAAAA,kBAAA;MAAA,IAAA6K,OAAA;MACA,KAAA9Q,cAAA;MACA,IAAA+Q,kCAAA;QAAA5S,OAAA;QAAAC,QAAA;QAAAqG,IAAA;MAAA,GAAAlB,IAAA,CACA,UAAAsM,QAAA;QACAiB,OAAA,CAAAjR,WAAA,GAAAgQ,QAAA,CAAA3F,IAAA;QACA4G,OAAA,CAAA9Q,cAAA;MACA,CACA;IACA;IAEAgR,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhM,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA+L,UAAA;QAAA,WAAAhM,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA8L,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5L,IAAA,GAAA4L,UAAA,CAAA3L,IAAA;YAAA;cACAiG,YAAA,CAAAuF,OAAA,CAAAnR,cAAA;cACAmR,OAAA,CAAA7I,KAAA,eAAAsI,KAAA;cAAAU,UAAA,CAAA3L,IAAA;cAAA,OACA,IAAA4L,mCAAA;YAAA;cACAJ,OAAA,CAAAhL,iBAAA;YAAA;YAAA;cAAA,OAAAmL,UAAA,CAAA5K,IAAA;UAAA;QAAA,GAAA0K,SAAA;MAAA;IACA;IAEAI,WAAA,WAAAA,YAAA;MACA5F,YAAA,MAAA5L,cAAA;MACA,KAAAF,WAAA;MACA,KAAAI,cAAA;MACA,KAAA8I,kBAAA;MACA,KAAA/I,cAAA;IACA;IAEA+I,kBAAA,WAAAA,mBAAA;MAAA,IAAAyI,OAAA;MACA,KAAAvR,cAAA;MACA,IAAA+Q,kCAAA,MAAAnM,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAA5E,YAAA;QAAAwE,IAAA;MAAA,IAAAlB,IAAA,WAAAsM,QAAA;QACA0B,OAAA,CAAApR,YAAA,GAAA0P,QAAA,CAAA3F,IAAA;QACAqH,OAAA,CAAArR,MAAA,GAAA2P,QAAA,CAAAhR,KAAA;QACA0S,OAAA,CAAAvR,cAAA;MACA;IACA;IAEA;IACAwR,WAAA,WAAAA,YAAArL,IAAA;MACAsL,MAAA,CAAAC,IAAA,uBAAArH,MAAA,CACAlE,IAAA,CAAAjI,EAAA,aAAAmM,MAAA,CAAAlE,IAAA,CAAAwL,KAAA,kBAAAtH,MAAA,CAAAlE,IAAA,CAAA7B,UAAA,GACA,QACA;IACA;IAEA;IACAsN,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,kBAAA,GAAAD,IAAA,CAAAE,OAAA;MACA,kCAAAC,IAAA,CAAAF,kBAAA;IACA;IAEA;IACA9N,sBAAA,WAAAA,uBAAA;MACA,SAAAoE,KAAA,CAAA6J,SAAA;QACA,KAAA7J,KAAA,CAAA6J,SAAA,CAAA/I,SAAA;MACA;MAEA,SAAAd,KAAA,CAAA8J,KAAA;QACA,IAAAC,WAAA,QAAA/J,KAAA,CAAA8J,KAAA,CAAAE,GAAA,CAAAnJ,aAAA,CACA,yBACA;QACA,IAAAkJ,WAAA;UACAA,WAAA,CAAAjJ,SAAA;QACA;MACA;IACA;IAEA;IACAqD,WAAA,WAAAA,YAAA8F,GAAA;MACA,IAAAC,KAAA;MACA,IAAAC,GAAA,GAAAF,GAAA,IAAAA,GAAA,CAAAN,OAAA,CAAAO,KAAA;MACA,IACAC,GAAA,KACA,KAAAtU,WAAA,CAAAK,IAAA,IACA,KAAAL,WAAA,CAAAM,UAAA,IACA,KAAAN,WAAA,CAAAM,UAAA,CAAAiJ,MAAA,IACA,KAAAvJ,WAAA,CAAAO,QAAA,GACA;QACA,IAAAA,QAAA,MAAA6L,MAAA,KAAA9C,mBAAA,CAAA1C,OAAA,EACA,KAAA5G,WAAA,CAAAM,UAAA,OAAAgJ,mBAAA,CAAA1C,OAAA,EACA,KAAA5G,WAAA,CAAAO,QAAA,GACA,KAAAP,WAAA,CAAAO,QAAA,CAAAgU,KAAA,QACA,IACA;QACAhU,QAAA,CAAAqJ,OAAA,WAAA4K,OAAA;UACA,IAAAA,OAAA,IAAAA,OAAA,CAAAjL,MAAA;YACA,IAAAkL,UAAA,OAAAC,MAAA,CAAAF,OAAA;YACA,IAAAG,aAAA,GACA,iDACAH,OAAA,GACA;YACAF,GAAA,GAAAA,GAAA,CAAAR,OAAA,CAAAW,UAAA,EAAAE,aAAA;UACA;QACA;MACA;MACA,OAAAL,GAAA;IACA;IAEA;IACAM,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAA3T,GAAA,CAAAqI,MAAA;QACA,YAAA3D,QAAA,CAAAkP,OAAA;MACA;MACA,IAAA5T,GAAA,QAAAA,GAAA;MACA,IAAA6T,SAAA;MACA,IAAA9H,GAAA;MACA,IAAA/L,GAAA,CAAAqI,MAAA;QACA,IAAAyL,GAAA,QAAAhU,WAAA,CAAAiH,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAjI,EAAA,IAAAiB,GAAA;QAAA;QACA,IAAA8T,GAAA,IAAAA,GAAA,CAAAC,WAAA,EAAAF,SAAA;QACA9H,GAAA,GAAA+H,GAAA,CAAAC,WAAA;MACA;MACA,IAAAF,SAAA;QACA,KAAAG,OAAA;UACA3G,KAAA;UACAhI,OAAA;UACA4O,gBAAA;UACAC,iBAAA;UACAC,WAAA,WAAAA,YAAAC,CAAA,EAAAC,EAAA,EAAAC,IAAA;YACAA,IAAA;UACA;QACA;QACA9D,cAAA,CAAA+D,gBAAA,CAAAvU,GAAA,EACAoE,IAAA,WAAAsM,QAAA;UACA,IAAAA,QAAA,CAAAtL,IAAA;YACAuO,OAAA,CAAAjP,QAAA;cACAW,OAAA;cACAC,IAAA;YACA;UACA;QACA,GACAf,KAAA;MACA;QACAwH,GAAA,GAAAA,GAAA,CAAA6G,OAAA,KAAAY,MAAA;QACAzH,GAAA,GAAAA,GAAA,CAAA6G,OAAA,KAAAY,MAAA;QACAlB,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAkC,QAAA,CAAAC,MAAA,GAAA1I,GAAA;MACA;IACA;IAEA2I,OAAA,WAAAA,QAAA3I,GAAA;MACAuG,MAAA,CAAAC,IAAA,CAAAxG,GAAA;IACA;IAEA;IACA;IACA4I,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9O,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA6O,UAAA;QAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,MAAA,EAAA1E,QAAA,EAAA2E,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA,EAAAtB,IAAA,EAAAnF,KAAA,EAAA0G,QAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,IAAA,EAAAtX,IAAA,EAAAuX,QAAA,EAAAC,MAAA;QAAA,WAAApQ,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhQ,IAAA,GAAAgQ,UAAA,CAAA/P,IAAA;YAAA;cAAA,MACAsO,OAAA,CAAA5U,GAAA,CAAAqI,MAAA;gBAAAgO,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAA/J,MAAA,WACAsI,OAAA,CAAAlQ,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIAsP,OAAA,CAAA/R,YAAA;gBAAAwT,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cACAsO,OAAA,CAAA9R,SAAA;cAAA,KACA8R,OAAA,CAAA7R,aAAA;gBAAAsT,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA+P,UAAA,CAAAhQ,IAAA;cAAAgQ,UAAA,CAAA/P,IAAA;cAAA,OAEAsO,OAAA,CAAA7R,aAAA,CAAAuT,MAAA;YAAA;cAAAD,UAAA,CAAA/P,IAAA;cAAA;YAAA;cAAA+P,UAAA,CAAAhQ,IAAA;cAAAgQ,UAAA,CAAAjP,EAAA,GAAAiP,UAAA;cAEA5R,OAAA,CAAA8R,GAAA,qOAAAF,UAAA,CAAAjP,EAAA;YAAA;cAAAiP,UAAA,CAAA/P,IAAA;cAAA,OAGA,IAAAM,OAAA,WAAA4P,OAAA;gBAAA,OAAAjO,UAAA,CAAAiO,OAAA;cAAA;YAAA;cAGA5B,OAAA,CAAA/R,YAAA;cACA+R,OAAA,CAAA9R,SAAA;cACA8R,OAAA,CAAA/S,eAAA;cACA+S,OAAA,CAAA9S,YAAA;cACA8S,OAAA,CAAA7S,UAAA;cAAAsU,UAAA,CAAAhQ,IAAA;cAGA;cACA0O,gBAAA,GAAAH,OAAA,CAAA9U,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACAoR,OAAA,CAAA5U,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAiW,MAAA,GAAAD,gBAAA,CACA/J,GAAA,WAAAxH,OAAA;gBAAA,gBAAA0H,MAAA,CAAA1H,OAAA,CAAA2J,OAAA,IAAA3J,OAAA,CAAA6J,KAAA;cAAA,GACA3C,IAAA,QAEA;cAAA2L,UAAA,CAAA/P,IAAA;cAAA,OACA,IAAAmQ,4BAAA,EAAA7B,OAAA,CAAA5U,GAAA,CAAA0K,IAAA;YAAA;cAAAuK,gBAAA,GAAAoB,UAAA,CAAAvL,IAAA;cAAA,KAAAgK,qBAAA,GACAG,gBAAA,CAAAtW,IAAA,cAAAmW,qBAAA,eAAAA,qBAAA,CAAAzM,MAAA;gBAAAgO,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAtW,IAAA,CACAqM,GAAA,WAAAxH,OAAA,EAAAyH,KAAA;gBAAA,IAAA0L,qBAAA,EAAAC,sBAAA;gBACA,IAAAvJ,KAAA,GACA,EAAAsJ,qBAAA,GAAA5B,gBAAA,CAAA9J,KAAA,eAAA0L,qBAAA,uBAAAA,qBAAA,CAAAxJ,OAAA,OAAAyJ,sBAAA,GACA7B,gBAAA,CAAA9J,KAAA,eAAA2L,sBAAA,uBAAAA,sBAAA,CAAAvJ,KAAA,KACA;gBACA,IAAAwJ,OAAA,GAAArT,OAAA,CAAAqT,OAAA;gBACA,uBAAA3L,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAA2L,OAAA;cACA,GACAnM,IAAA,yDAEA;cACAkK,OAAA,CAAA9S,YAAA,CAAAkH,IAAA;gBACA8N,IAAA;gBACAD,OAAA,qDAAA3L,MAAA,CAAA0J,OAAA,CAAA5U,GAAA,CAAAqI,MAAA,gCAAA6C,MAAA,CAAA8J,MAAA;cACA;;cAEA;cACAG,SAAA;gBACA2B,IAAA;gBACAD,OAAA;cACA;cACAjC,OAAA,CAAA9S,YAAA,CAAAkH,IAAA,CAAAmM,SAAA;;cAEA;cACAC,MAAA,GACAR,OAAA,CAAA3R,eAAA,CACA2P,OAAA,kBAAAgC,OAAA,CAAA5U,GAAA,CAAAqI,MAAA,EACAuK,OAAA,yFAAA1H,MAAA,CACAgK,eAAA,GAEA;cAAAmB,UAAA,CAAA/P,IAAA;cAAA,OACA,IAAAyQ,YAAA,EACA7B,eAAA,EACA,aACA,qBACA;YAAA;cAJAxE,QAAA,GAAA2F,UAAA,CAAAvL,IAAA;cAAA,IAKA4F,QAAA,CAAAsG,EAAA;gBAAAX,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA3E,QAAA,CAAAuG,IAAA,CAAAC,SAAA;cACAtC,OAAA,CAAA7R,aAAA,GAAAsS,MAAA;cACAC,OAAA,OAAA6B,WAAA;cACA5B,MAAA;cACAC,aAAA;cACAC,YAAA;cAEA;cACAC,aAAA,YAAAA,cAAAxC,GAAA;gBACA,OAAAA,GAAA,CAAAN,OAAA,gCAAAwE,KAAA;kBACA,OAAAtK,MAAA,CAAAuK,YAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAxE,OAAA;gBACA;cACA,GAEA;cACA+C,aAAA,YAAAA,cAAA4B,UAAA;gBACA;kBACA,IAAAC,eAAA,OAAAC,cAAA,EAAAF,UAAA,EAAA3C,OAAA,CAAA1S,eAAA;kBACAiT,SAAA,CAAA0B,OAAA,GAAAW,eAAA;;kBAEA;kBACA5C,OAAA,CAAA/M,SAAA;oBACA,IAAA/F,YAAA,GAAA8S,OAAA,CAAA3L,KAAA,CAAAnH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAA4V,YAAA;oBACA;kBACA;gBACA,SAAAlT,KAAA;kBACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;gBACA;cACA,GAEA;YAAA;cAAA,KACA;gBAAA6R,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,KAEAsO,OAAA,CAAA9R,SAAA;gBAAAuT,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAAAL,UAAA,CAAA/P,IAAA;cAAA,OAGA+O,MAAA,CAAAsC,IAAA;YAAA;cAAA/B,kBAAA,GAAAS,UAAA,CAAAvL,IAAA;cAAAwJ,IAAA,GAAAsB,kBAAA,CAAAtB,IAAA;cAAAnF,KAAA,GAAAyG,kBAAA,CAAAzG,KAAA;cAAA,KAEAmF,IAAA;gBAAA+B,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cACA;cACA,IAAAkP,aAAA;gBACA;kBACAK,QAAA,GAAAlR,IAAA,CAAAiT,KAAA,CAAApC,aAAA;kBACA,IAAAK,QAAA,CAAAM,MAAA;oBACA;oBACAL,aAAA,GAAAJ,aAAA,CAAAG,QAAA,CAAAM,MAAA;oBACAZ,MAAA,IAAAO,aAAA;oBACAH,aAAA,CAAAJ,MAAA;kBACA;gBACA,SAAAsC,CAAA;kBACApT,OAAA,CAAAqT,IAAA,gBAAAD,CAAA;gBACA;cACA;cAAA,OAAAxB,UAAA,CAAA/J,MAAA;YAAA;cAIAyJ,KAAA,GAAAT,OAAA,CAAAyC,MAAA,CAAA5I,KAAA;cACAqG,aAAA,IAAAO,KAAA;;cAEA;YAAA;cAAA,KACAP,aAAA,CAAArO,QAAA;gBAAAkP,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cACA0P,YAAA,GAAAR,aAAA,CAAAwC,OAAA;cACA/B,IAAA,GAAAT,aAAA,CAAAyC,KAAA,IAAAjC,YAAA,EAAA1I,IAAA;cACAkI,aAAA,GAAAA,aAAA,CAAAyC,KAAA,CAAAjC,YAAA;cAAA,MAEA,CAAAC,IAAA,IAAAA,IAAA,iBAAAA,IAAA,CAAAiC,UAAA;gBAAA7B,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAA/J,MAAA;YAAA;cAAA+J,UAAA,CAAAhQ,IAAA;cAKA1H,IAAA,GAAAsX,IAAA,CAAAgC,KAAA,IAAA3K,IAAA;cAAA,MACA3O,IAAA;gBAAA0X,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAA/J,MAAA;YAAA;cAIA4J,QAAA,GAAAvR,IAAA,CAAAiT,KAAA,CAAAjZ,IAAA;cAAA,IACAuX,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAA/J,MAAA;YAAA;cAAA,MAKA4J,QAAA,CAAAC,MAAA,cAAAD,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cAAA,OAAA+P,UAAA,CAAA/J,MAAA;YAAA;cAIA;cACA6J,MAAA,GAAAT,aAAA,CAAAQ,QAAA,CAAAC,MAAA,GAEA;cAAA,KACAA,MAAA,CAAAhP,QAAA;gBAAAkP,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cACAmP,YAAA;cAAA,OAAAY,UAAA,CAAA/J,MAAA;YAAA;cAAA,KAKA6J,MAAA,CAAAhP,QAAA;gBAAAkP,UAAA,CAAA/P,IAAA;gBAAA;cAAA;cACAmP,YAAA;cAAA,OAAAY,UAAA,CAAA/J,MAAA;YAAA;cAIA;cACA,KAAAmJ,YAAA,IAAAU,MAAA;gBACAZ,MAAA,IAAAY,MAAA;gBACAR,aAAA,CAAAJ,MAAA;cACA;cAAAc,UAAA,CAAA/P,IAAA;cAAA;YAAA;cAAA+P,UAAA,CAAAhQ,IAAA;cAAAgQ,UAAA,CAAA8B,EAAA,GAAA9B,UAAA;cAEA5R,OAAA,CAAAqT,IAAA;gBACA7B,IAAA,EAAAA,IAAA;gBACAzR,KAAA,EAAA6R,UAAA,CAAA8B,EAAA,CAAA9S,OAAA;gBACAmQ,aAAA,EAAAA;cACA;cAAA,OAAAa,UAAA,CAAA/J,MAAA;YAAA;cAAA+J,UAAA,CAAA/P,IAAA;cAAA;YAAA;cAAA+P,UAAA,CAAA/P,IAAA;cAAA;YAAA;cAAA+P,UAAA,CAAA/P,IAAA;cAAA;YAAA;cAAA+P,UAAA,CAAAhQ,IAAA;cAAAgQ,UAAA,CAAA+B,EAAA,GAAA/B,UAAA;cAMA5R,OAAA,CAAAD,KAAA,YAAA6R,UAAA,CAAA+B,EAAA;cACAxD,OAAA,CAAAlQ,QAAA,CAAAF,KAAA,CAAA6R,UAAA,CAAA+B,EAAA,CAAA/S,OAAA;cACA,IAAAuP,OAAA,CAAA9S,YAAA;gBACA8S,OAAA,CAAA9S,YAAA,IAAA+U,OAAA;cACA;YAAA;cAAAR,UAAA,CAAAhQ,IAAA;cAEAuO,OAAA,CAAA7R,aAAA;cACA,IAAA6R,OAAA,CAAA/S,eAAA;gBACA+S,OAAA,CAAA7S,UAAA;gBACA6S,OAAA,CAAA/R,YAAA;cACA;cAAA,OAAAwT,UAAA,CAAArK,MAAA;YAAA;YAAA;cAAA,OAAAqK,UAAA,CAAAhP,IAAA;UAAA;QAAA,GAAAwN,SAAA;MAAA;IAEA;IACA;IACAwD,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxS,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAuS,UAAA;QAAA,IAAAC,sBAAA,EAAAzD,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAuD,UAAA,EAAArD,MAAA,EAAA1E,QAAA,EAAA2E,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAmD,cAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAjD,aAAA,EAAAkD,aAAA;QAAA,WAAA9S,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA4S,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1S,IAAA,GAAA0S,UAAA,CAAAzS,IAAA;YAAA;cAAA,MACAgS,OAAA,CAAAtY,GAAA,CAAAqI,MAAA;gBAAA0Q,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cAAA,OAAAyS,UAAA,CAAAzM,MAAA,WACAgM,OAAA,CAAA5T,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIAgT,OAAA,CAAAzV,YAAA;gBAAAkW,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cACAgS,OAAA,CAAAxV,SAAA;cAAA,KACAwV,OAAA,CAAAvV,aAAA;gBAAAgW,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cAAAyS,UAAA,CAAA1S,IAAA;cAAA0S,UAAA,CAAAzS,IAAA;cAAA,OAEAgS,OAAA,CAAAvV,aAAA,CAAAuT,MAAA;YAAA;cAAAyC,UAAA,CAAAzS,IAAA;cAAA;YAAA;cAAAyS,UAAA,CAAA1S,IAAA;cAAA0S,UAAA,CAAA3R,EAAA,GAAA2R,UAAA;cAEAtU,OAAA,CAAA8R,GAAA,qOAAAwC,UAAA,CAAA3R,EAAA;YAAA;cAAA2R,UAAA,CAAAzS,IAAA;cAAA,OAIA,IAAAM,OAAA,WAAA4P,OAAA;gBAAA,OAAAjO,UAAA,CAAAiO,OAAA;cAAA;YAAA;cAGA8B,OAAA,CAAAzV,YAAA;cACAyV,OAAA,CAAAxV,SAAA;cACAwV,OAAA,CAAAzW,eAAA;cACAyW,OAAA,CAAAxW,YAAA;cACAwW,OAAA,CAAAvW,UAAA;cAAAgX,UAAA,CAAA1S,IAAA;cAGA;cACA0O,gBAAA,GAAAuD,OAAA,CAAAxY,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACA8U,OAAA,CAAAtY,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAiW,MAAA,GAAAD,gBAAA,CACA/J,GAAA,WAAAxH,OAAA;gBAAA,gBAAA0H,MAAA,CAAA1H,OAAA,CAAA2J,OAAA,IAAA3J,OAAA,CAAA6J,KAAA;cAAA,GACA3C,IAAA,QAEA;cAAAqO,UAAA,CAAAzS,IAAA;cAAA,OACA,IAAAmQ,4BAAA,EAAA6B,OAAA,CAAAtY,GAAA,CAAA0K,IAAA;YAAA;cAAAuK,gBAAA,GAAA8D,UAAA,CAAAjO,IAAA;cAAA,KAAA0N,sBAAA,GACAvD,gBAAA,CAAAtW,IAAA,cAAA6Z,sBAAA,eAAAA,sBAAA,CAAAnQ,MAAA;gBAAA0Q,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAtW,IAAA,CACAqM,GAAA,WAAAxH,OAAA,EAAAyH,KAAA;gBAAA,IAAA+N,sBAAA,EAAAC,sBAAA;gBACA,IAAA5L,KAAA,GACA,EAAA2L,sBAAA,GAAAjE,gBAAA,CAAA9J,KAAA,eAAA+N,sBAAA,uBAAAA,sBAAA,CAAA7L,OAAA,OAAA8L,sBAAA,GACAlE,gBAAA,CAAA9J,KAAA,eAAAgO,sBAAA,uBAAAA,sBAAA,CAAA5L,KAAA,KACA;gBACA,IAAAwJ,OAAA,GAAArT,OAAA,CAAAqT,OAAA;gBACA,uBAAA3L,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAA2L,OAAA;cACA,GACAnM,IAAA,yDAEA;cACA4N,OAAA,CAAAxW,YAAA,CAAAkH,IAAA;gBACA8N,IAAA;gBACAD,OAAA,qDAAA3L,MAAA,CAAAoN,OAAA,CAAAtY,GAAA,CAAAqI,MAAA,gCAAA6C,MAAA,CAAA8J,MAAA;cACA;;cAEA;cACAG,UAAA;gBACA2B,IAAA;gBACAD,OAAA;cACA;cACAyB,OAAA,CAAAxW,YAAA,CAAAkH,IAAA,CAAAmM,UAAA;;cAEA;cACAC,MAAA,GACAkD,OAAA,CAAArV,eAAA,CACA2P,OAAA,kBAAA0F,OAAA,CAAAtY,GAAA,CAAAqI,MAAA,EACAuK,OAAA,yFAAA1H,MAAA,CACAgK,eAAA,GAEA;cAAA6D,UAAA,CAAAzS,IAAA;cAAA,OACA,IAAA4S,cAAA,EAAA9D,MAAA;YAAA;cAAA1E,QAAA,GAAAqI,UAAA,CAAAjO,IAAA;cAAA,IACA4F,QAAA,CAAAsG,EAAA;gBAAA+B,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA3E,QAAA,CAAAuG,IAAA,CAAAC,SAAA;cACAoB,OAAA,CAAAvV,aAAA,GAAAsS,MAAA;cACAC,OAAA,OAAA6B,WAAA;cACA5B,MAAA;cACAmD,cAAA,GAAAvN,IAAA,CAAAC,GAAA;cACAuN,cAAA;cACAC,UAAA,OAEA;cACAjD,aAAA,YAAAA,cAAA4B,UAAA;gBACA,IAAA4B,WAAA,GAAAhO,IAAA,CAAAC,GAAA;gBACA;gBACA,IAAA+N,WAAA,GAAAT,cAAA;kBACAvD,UAAA,CAAA0B,OAAA,GAAAU,UAAA;kBACAmB,cAAA,GAAAS,WAAA;kBACA;kBACAb,OAAA,CAAAzQ,SAAA;oBACA,IAAA/F,YAAA,GAAAwW,OAAA,CAAArP,KAAA,CAAAnH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAA4V,YAAA;oBACA;kBACA;gBACA;cACA,GAEA;cACAmB,aAAA;gBAAA,IAAAO,KAAA,OAAAtT,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAqT,UAAA;kBAAA,IAAAC,mBAAA,EAAAhF,IAAA,EAAAnF,KAAA,EAAA4G,KAAA,EAAAwD,KAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAxD,IAAA,EAAAC,QAAA,EAAAwD,SAAA,EAAAC,eAAA,EAAAC,aAAA;kBAAA,WAAA7T,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA2T,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAzT,IAAA,GAAAyT,UAAA,CAAAxT,IAAA;sBAAA;wBAAAwT,UAAA,CAAAzT,IAAA;sBAAA;wBAAA,KAEA;0BAAAyT,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA,KAEAgS,OAAA,CAAAxV,SAAA;0BAAAgX,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAoQ,KAAA;sBAAA;wBAAAoD,UAAA,CAAAxT,IAAA;wBAAA,OAGA+O,MAAA,CAAAsC,IAAA;sBAAA;wBAAA2B,mBAAA,GAAAQ,UAAA,CAAAhP,IAAA;wBAAAwJ,IAAA,GAAAgF,mBAAA,CAAAhF,IAAA;wBAAAnF,KAAA,GAAAmK,mBAAA,CAAAnK,KAAA;wBAAA,KACAmF,IAAA;0BAAAwF,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBACA,IAAAiP,MAAA,CAAAlN,MAAA;0BACAsN,aAAA,CAAAJ,MAAA;wBACA;wBAAA,OAAAuE,UAAA,CAAAxN,MAAA;sBAAA;wBAIAyJ,KAAA,GAAAT,OAAA,CAAAyC,MAAA,CAAA5I,KAAA;wBACAoK,KAAA,GAAAxD,KAAA,CAAA1C,KAAA,OAAAtM,MAAA,WAAAkP,IAAA;0BAAA,OAAAA,IAAA,CAAA3I,IAAA;wBAAA;wBAAAkM,SAAA,OAAAO,2BAAA,CAAArU,OAAA,EAEA6T,KAAA;wBAAAO,UAAA,CAAAzT,IAAA;wBAAAmT,SAAA,CAAAQ,CAAA;sBAAA;wBAAA,KAAAP,KAAA,GAAAD,SAAA,CAAAS,CAAA,IAAA3F,IAAA;0BAAAwF,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA2P,IAAA,GAAAwD,KAAA,CAAAtK,KAAA;wBAAA2K,UAAA,CAAAzT,IAAA;wBAEA6P,QAAA,GAAAvR,IAAA,CAAAiT,KAAA,CAAA3B,IAAA;wBAAA,IACAC,QAAA,CAAAxF,QAAA;0BAAAoJ,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA,OAAAwT,UAAA,CAAAxN,MAAA;sBAAA;wBAEAoE,SAAA,GAAAwF,QAAA,CAAAxF,QAAA,EAEA;wBAAA,MACAA,SAAA,cAAAA,SAAA;0BAAAoJ,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA,OAAAwT,UAAA,CAAAxN,MAAA;sBAAA;wBAIAsM,UAAA,IAAAlI,SAAA;;wBAEA;sBAAA;wBAAA,KACA;0BAAAoJ,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBACAqT,eAAA,GAAAf,UAAA,CAAAZ,OAAA;wBACA4B,aAAA,GAAAhB,UAAA,CAAAZ,OAAA;wBAAA,MAEA2B,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBACA;wBACA,KAAAqS,cAAA;0BACApD,MAAA,IAAAqD,UAAA;0BACA;0BACAjD,aAAA,KAAA8B,cAAA,EAAAlC,MAAA,EAAA+C,OAAA,CAAApW,eAAA;wBACA;wBACA0W,UAAA;wBAAA,OAAAkB,UAAA,CAAAxN,MAAA;sBAAA;wBAAA,MAEAqN,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBACA;wBACAqS,cAAA;wBACA,IAAAgB,eAAA;0BACApE,MAAA,IAAAqD,UAAA,CAAApN,SAAA,IAAAmO,eAAA;0BACA;0BACAhE,aAAA,KAAA8B,cAAA,EAAAlC,MAAA,EAAA+C,OAAA,CAAApW,eAAA;wBACA;wBACA0W,UAAA,GAAAA,UAAA,CAAApN,SAAA,CAAAmO,eAAA;wBAAA,OAAAG,UAAA,CAAAxN,MAAA;sBAAA;wBAAA,MAEAqN,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBACA;wBACAqS,cAAA;wBACAC,UAAA,GAAAA,UAAA,CAAApN,SAAA,CAAAoO,aAAA;wBAAA,OAAAE,UAAA,CAAAxN,MAAA;sBAAA;wBAGA;wBACA,IAAAqN,eAAA;0BACApE,MAAA,IAAAqD,UAAA,CAAApN,SAAA,IAAAmO,eAAA;0BACA;0BACAhE,aAAA,KAAA8B,cAAA,EAAAlC,MAAA,EAAA+C,OAAA,CAAApW,eAAA;wBACA;wBACA0W,UAAA,GAAAA,UAAA,CAAApN,SAAA,CAAAoO,aAAA;wBACAjB,cAAA;wBAAA,OAAAmB,UAAA,CAAAxN,MAAA;sBAAA;wBAAAwN,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAzT,IAAA;wBAAAyT,UAAA,CAAA1S,EAAA,GAAA0S,UAAA;wBAKArV,OAAA,CAAAqT,IAAA;0BACA7B,IAAA,EAAAA,IAAA;0BACAzR,KAAA,EAAAsV,UAAA,CAAA1S,EAAA,CAAA/B;wBACA;sBAAA;wBAAAyU,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAzT,IAAA;wBAAAyT,UAAA,CAAA3B,EAAA,GAAA2B,UAAA;wBAAAN,SAAA,CAAA3B,CAAA,CAAAiC,UAAA,CAAA3B,EAAA;sBAAA;wBAAA2B,UAAA,CAAAzT,IAAA;wBAAAmT,SAAA,CAAAU,CAAA;wBAAA,OAAAJ,UAAA,CAAA9N,MAAA;sBAAA;wBAAA8N,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAxT,IAAA;wBAAA;sBAAA;wBAAAwT,UAAA,CAAAzT,IAAA;wBAAAyT,UAAA,CAAA1B,EAAA,GAAA0B,UAAA;wBAAA,MAKAA,UAAA,CAAA1B,EAAA,CAAA/S,OAAA;0BAAAyU,UAAA,CAAAxT,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAoQ,KAAA;sBAAA;wBAEAjS,OAAA,CAAAD,KAAA,eAAAsV,UAAA,CAAA1B,EAAA;wBAAA,MAAA0B,UAAA,CAAA1B,EAAA;sBAAA;sBAAA;wBAAA,OAAA0B,UAAA,CAAAzS,IAAA;oBAAA;kBAAA,GAAAgS,SAAA;gBAAA,CAGA;gBAAA,gBAzFAR,cAAA;kBAAA,OAAAO,KAAA,CAAAe,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAArB,UAAA,CAAAzS,IAAA;cAAA,OA2FAuS,aAAA;YAAA;cAAAE,UAAA,CAAAzS,IAAA;cAAA;YAAA;cAAAyS,UAAA,CAAA1S,IAAA;cAAA0S,UAAA,CAAAZ,EAAA,GAAAY,UAAA;cAAA,MAGAA,UAAA,CAAAZ,EAAA,CAAA9S,OAAA;gBAAA0T,UAAA,CAAAzS,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAA8R,GAAA;cAAA,OAAAwC,UAAA,CAAAzM,MAAA;YAAA;cAGA7H,OAAA,CAAAD,KAAA,YAAAuU,UAAA,CAAAZ,EAAA;cACAG,OAAA,CAAA5T,QAAA,CAAAF,KAAA,CAAAuU,UAAA,CAAAZ,EAAA,CAAA9S,OAAA;cACA,IAAAiT,OAAA,CAAAxW,YAAA;gBACAwW,OAAA,CAAAxW,YAAA,IAAA+U,OAAA;cACA;YAAA;cAAAkC,UAAA,CAAA1S,IAAA;cAEAiS,OAAA,CAAAvV,aAAA;cACA;cACA,IAAAuV,OAAA,CAAAzW,eAAA;gBACAyW,OAAA,CAAAvW,UAAA;gBACAuW,OAAA,CAAAzV,YAAA;cACA;cAAA,OAAAkW,UAAA,CAAA/M,MAAA;YAAA;YAAA;cAAA,OAAA+M,UAAA,CAAA1R,IAAA;UAAA;QAAA,GAAAkR,SAAA;MAAA;IAEA;IACA;IACA8B,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxU,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAuU,UAAA;QAAA,IAAAxF,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAsF,WAAA,EAAApF,MAAA,EAAA1E,QAAA,EAAA2E,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAkF,eAAA,EAAA9E,aAAA,EAAA+E,mBAAA,EAAApG,IAAA,EAAAnF,KAAA,EAAA4G,KAAA,EAAAwD,KAAA,EAAAoB,UAAA,EAAAC,MAAA,EAAA3E,IAAA,EAAAtX,IAAA,EAAAkc,iBAAA,EAAA3E,QAAA,EAAAW,OAAA;QAAA,WAAA9Q,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAA4U,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1U,IAAA,GAAA0U,UAAA,CAAAzU,IAAA;YAAA;cAAA,MACAgU,OAAA,CAAAta,GAAA,CAAAqI,MAAA;gBAAA0S,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,OAAAyU,UAAA,CAAAzO,MAAA,WACAgO,OAAA,CAAA5V,QAAA;gBACAW,OAAA;gBACAC,IAAA;cACA;YAAA;cAAA,KAIAgV,OAAA,CAAAzX,YAAA;gBAAAkY,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cACAgU,OAAA,CAAAxX,SAAA;cAAA,KACAwX,OAAA,CAAAvX,aAAA;gBAAAgY,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAAzU,IAAA;cAAA,OAEAgU,OAAA,CAAAvX,aAAA,CAAAuT,MAAA;YAAA;cAAAyE,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAA3T,EAAA,GAAA2T,UAAA;cAEAtW,OAAA,CAAA8R,GAAA,qOAAAwE,UAAA,CAAA3T,EAAA;YAAA;cAAA2T,UAAA,CAAAzU,IAAA;cAAA,OAIA,IAAAM,OAAA,WAAA4P,OAAA;gBAAA,OAAAjO,UAAA,CAAAiO,OAAA;cAAA;YAAA;cAGA8D,OAAA,CAAAzX,YAAA;cACAyX,OAAA,CAAAxX,SAAA;cACAwX,OAAA,CAAAzY,eAAA;cACAyY,OAAA,CAAAxY,YAAA;cACAwY,OAAA,CAAAvY,UAAA;cAEAgT,gBAAA,GAAAuF,OAAA,CAAAxa,WAAA,CAAAiH,MAAA,WAAAvD,OAAA;gBAAA,OACA8W,OAAA,CAAAta,GAAA,CAAAmH,QAAA,CAAA3D,OAAA,CAAAzE,EAAA;cAAA,CACA;cACAiW,MAAA,GAAAD,gBAAA,CACA/J,GAAA,WAAAxH,OAAA;gBAAA,gBAAA0H,MAAA,CAAA1H,OAAA,CAAA2J,OAAA,IAAA3J,OAAA,CAAA6J,KAAA;cAAA,GACA3C,IAAA;cAAAqQ,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAAzU,IAAA;cAAA,OAGA,IAAAmQ,4BAAA,EAAA6D,OAAA,CAAAta,GAAA,CAAA0K,IAAA;YAAA;cAAAuK,gBAAA,GAAA8F,UAAA,CAAAjQ,IAAA;cAAA,MACA,CAAAmK,gBAAA,CAAAtW,IAAA,KAAAsW,gBAAA,CAAAtW,IAAA,CAAA0J,MAAA;gBAAA0S,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGAxB,eAAA,GAAAD,gBAAA,CAAAtW,IAAA,CACAqM,GAAA,WAAAxH,OAAA,EAAAyH,KAAA;gBAAA,IAAA+P,sBAAA,EAAAC,sBAAA;gBACA,IAAA5N,KAAA,GACA,EAAA2N,sBAAA,GAAAjG,gBAAA,CAAA9J,KAAA,eAAA+P,sBAAA,uBAAAA,sBAAA,CAAA7N,OAAA,OAAA8N,sBAAA,GACAlG,gBAAA,CAAA9J,KAAA,eAAAgQ,sBAAA,uBAAAA,sBAAA,CAAA5N,KAAA,KACA;gBACA,IAAAwJ,OAAA,GAAArT,OAAA,CAAAqT,OAAA;gBACA,uBAAA3L,MAAA,CAAAD,KAAA,yCAAAC,MAAA,CAAAmC,KAAA,gBAAAnC,MAAA,CAAA2L,OAAA;cACA,GACAnM,IAAA,yDAEA;cACA4P,OAAA,CAAAxY,YAAA,CAAAkH,IAAA;gBACA8N,IAAA;gBACAD,OAAA,qDAAA3L,MAAA,CAAAoP,OAAA,CAAAta,GAAA,CAAAqI,MAAA,gCAAA6C,MAAA,CAAA8J,MAAA;cACA;;cAEA;cACAG,WAAA;gBACA2B,IAAA;gBACAD,OAAA;cACA;cACAyD,OAAA,CAAAxY,YAAA,CAAAkH,IAAA,CAAAmM,WAAA;cACAmF,OAAA,CAAAvY,UAAA;cAEAqT,MAAA,GACAkF,OAAA,CAAArX,eAAA,CACA2P,OAAA,kBAAA0H,OAAA,CAAAta,GAAA,CAAAqI,MAAA,EACAuK,OAAA,6FAAA1H,MAAA,CACAgK,eAAA;cAAA6F,UAAA,CAAAzU,IAAA;cAAA,OAEA,IAAA4U,gBAAA,EAAA9F,MAAA;YAAA;cAAA1E,QAAA,GAAAqK,UAAA,CAAAjQ,IAAA;cAAA,KAEA4F,QAAA,CAAAsG,EAAA;gBAAA+D,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cACA+O,MAAA,GAAA3E,QAAA,CAAAuG,IAAA,CAAAC,SAAA;cACAoD,OAAA,CAAAvX,aAAA,GAAAsS,MAAA;cACAC,OAAA,OAAA6B,WAAA;cACA5B,MAAA;cACAmD,eAAA,GAAAvN,IAAA,CAAAC,GAAA;cAEAuK,aAAA,YAAAA,cAAA4B,UAAA;gBACA,IAAA4B,WAAA,GAAAhO,IAAA,CAAAC,GAAA;gBACA,IAAA+N,WAAA,GAAAT,eAAA;kBACAvD,WAAA,CAAA0B,OAAA,GAAAU,UAAA;kBACAmB,eAAA,GAAAS,WAAA;kBACAmB,OAAA,CAAAzS,SAAA;oBACA,IAAA/F,YAAA,GAAAwY,OAAA,CAAArR,KAAA,CAAAnH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAA4V,YAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA;gBAAAqD,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,KAEAgU,OAAA,CAAAxX,SAAA;gBAAAiY,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAAAqE,UAAA,CAAAzU,IAAA;cAAA,OAGA+O,MAAA,CAAAsC,IAAA;YAAA;cAAA+C,mBAAA,GAAAK,UAAA,CAAAjQ,IAAA;cAAAwJ,IAAA,GAAAoG,mBAAA,CAAApG,IAAA;cAAAnF,KAAA,GAAAuL,mBAAA,CAAAvL,KAAA;cAAA,KACAmF,IAAA;gBAAAyG,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cACA,IAAAiP,MAAA,CAAAlN,MAAA;gBACAsN,aAAA,CAAAJ,MAAA;cACA;cAAA,OAAAwF,UAAA,CAAAzO,MAAA;YAAA;cAIAyJ,KAAA,GAAAT,OAAA,CAAAyC,MAAA,CAAA5I,KAAA;cAAA4L,UAAA,CAAA1U,IAAA;cAEAkT,KAAA,GAAAxD,KAAA,CAAA1C,KAAA;cAAAsH,UAAA,OAAAZ,2BAAA,CAAArU,OAAA,EAEA6T,KAAA;cAAAwB,UAAA,CAAA1U,IAAA;cAAAsU,UAAA,CAAAX,CAAA;YAAA;cAAA,KAAAY,MAAA,GAAAD,UAAA,CAAAV,CAAA,IAAA3F,IAAA;gBAAAyG,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA2P,IAAA,GAAA2E,MAAA,CAAAzL,KAAA;cAAA,MACA,CAAA8G,IAAA,CAAA3I,IAAA,OAAA2I,IAAA,CAAAiC,UAAA;gBAAA6C,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,OAAAyU,UAAA,CAAAzO,MAAA;YAAA;cAEA3N,IAAA,GAAAsX,IAAA,CAAAgC,KAAA;cAAA,MACAtZ,IAAA;gBAAAoc,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,OAAAyU,UAAA,CAAAzO,MAAA;YAAA;cAAAyO,UAAA,CAAA1U,IAAA;cAGA6P,QAAA,GAAAvR,IAAA,CAAAiT,KAAA,CAAAjZ,IAAA;cAAA,OAAAkc,iBAAA,GACA3E,QAAA,CAAAiF,OAAA,cAAAN,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,iBAAAA,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAAO,KAAA,cAAAP,iBAAA,eAAAA,iBAAA,CAAAhE,OAAA;gBAAAkE,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cACAuQ,OAAA,GAAAX,QAAA,CAAAiF,OAAA,IAAAC,KAAA,CAAAvE,OAAA,EAEA;cAAA,MACAA,OAAA,cAAAA,OAAA;gBAAAkE,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cAAA,OAAAyU,UAAA,CAAAzO,MAAA;YAAA;cAIAiJ,MAAA,IAAAsB,OAAA;cACAlB,aAAA,CAAAJ,MAAA;YAAA;cAAAwF,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAA5C,EAAA,GAAA4C,UAAA;cAGAtW,OAAA,CAAAD,KAAA,wBAAAuW,UAAA,CAAA5C,EAAA;YAAA;cAAA4C,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAA3C,EAAA,GAAA2C,UAAA;cAAAJ,UAAA,CAAA9C,CAAA,CAAAkD,UAAA,CAAA3C,EAAA;YAAA;cAAA2C,UAAA,CAAA1U,IAAA;cAAAsU,UAAA,CAAAT,CAAA;cAAA,OAAAa,UAAA,CAAA/O,MAAA;YAAA;cAAA+O,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAIAtW,OAAA,CAAAD,KAAA,4BAAAuW,UAAA,CAAAM,EAAA;YAAA;cAAAN,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAA,MAIA,IAAAoQ,KAAA;YAAA;cAAAqE,UAAA,CAAAzU,IAAA;cAAA;YAAA;cAAAyU,UAAA,CAAA1U,IAAA;cAAA0U,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAAA,MAIAA,UAAA,CAAAO,EAAA,CAAAjW,OAAA;gBAAA0V,UAAA,CAAAzU,IAAA;gBAAA;cAAA;cACA7B,OAAA,CAAA8R,GAAA;cAAA,OAAAwE,UAAA,CAAAzO,MAAA;YAAA;cAGA7H,OAAA,CAAAD,KAAA,mBAAAuW,UAAA,CAAAO,EAAA;cACAhB,OAAA,CAAA5V,QAAA,CAAAF,KAAA;cACA,IAAA8V,OAAA,CAAAxY,YAAA;gBACAwY,OAAA,CAAAxY,YAAA,IAAA+U,OAAA;cACA;YAAA;cAAAkE,UAAA,CAAA1U,IAAA;cAEAiU,OAAA,CAAAvX,aAAA;cACA;cACA,IAAAuX,OAAA,CAAAzY,eAAA;gBACAyY,OAAA,CAAAvY,UAAA;gBACAuY,OAAA,CAAAzX,YAAA;cACA;cAAA,OAAAkY,UAAA,CAAA/O,MAAA;YAAA;YAAA;cAAA,OAAA+O,UAAA,CAAA1T,IAAA;UAAA;QAAA,GAAAkT,SAAA;MAAA;IAEA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAAzY,SAAA;MACA,SAAAC,aAAA;QACA,KAAAA,aAAA,CAAAuT,MAAA;MACA;MACA,KAAAzU,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,UAAA;MACA,KAAAc,YAAA;MACA,KAAAE,aAAA;IACA;IACAyY,aAAA,WAAAA,cAAA;MACA,SAAAxY,UAAA;QACA,KAAA2R,UAAA;MACA,gBAAA3R,UAAA;QACA,KAAAqV,YAAA;MACA,gBAAArV,UAAA;QACA,KAAAqX,cAAA;MACA;IACA;IACAoB,WAAA,WAAAA,YAAA;MACA,SAAAzY,UAAA;QACA,KAAA0Y,eAAA;MACA,gBAAA1Y,UAAA;QACA,KAAA2Y,mBAAA;MACA;IACA;IACA;IACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAE,OAAA;MAAA,WAAA9V,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAA6V,UAAA;QAAA,IAAAC,aAAA,EAAAC,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAvC,eAAA,EAAAC,aAAA,EAAAuC,SAAA;QAAA,WAAApW,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAkW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhW,IAAA,GAAAgW,UAAA,CAAA/V,IAAA;YAAA;cAAA,MAEAsV,OAAA,CAAA5b,GAAA,CAAAqI,MAAA;gBAAAgU,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACAsV,OAAA,CAAAlX,QAAA,CAAAkP,OAAA;cAAA,OAAAyI,UAAA,CAAA/P,MAAA;YAAA;cAAA,MAIAsP,OAAA,CAAA5b,GAAA,CAAAqI,MAAA;gBAAAgU,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cACAsV,OAAA,CAAAlX,QAAA,CAAAkP,OAAA;cAAA,OAAAyI,UAAA,CAAA/P,MAAA;YAAA;cAIA;cACAsP,OAAA,CAAAzY,kBAAA;cACAyY,OAAA,CAAAvY,YAAA;;cAEA;cACA,IAAAuY,OAAA,CAAA3S,KAAA,CAAAqT,YAAA;gBACAV,OAAA,CAAA3S,KAAA,CAAAqT,YAAA,CAAAC,SAAA;cACA;cAAAF,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAA/V,IAAA;cAAA,OAIAkK,cAAA,CAAAgM,QAAA,CAAAZ,OAAA,CAAA5b,GAAA;YAAA;cAAA8b,aAAA,GAAAO,UAAA,CAAAvR,IAAA;cAAA,MACA,CAAAgR,aAAA,CAAAnd,IAAA,KAAAmd,aAAA,CAAAnd,IAAA,CAAAkY,OAAA;gBAAAwF,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAAA2F,UAAA,CAAA/V,IAAA;cAAA,OAIA,IAAAyQ,YAAA,EACA+E,aAAA,CAAAnd,IAAA,CAAAkY,OAAA,EACA,YACA,mBACA;YAAA;cAJAkF,QAAA,GAAAM,UAAA,CAAAvR,IAAA;cAAA,IAMAiR,QAAA,CAAA/E,EAAA;gBAAAqF,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAAA2F,UAAA,CAAA/V,IAAA;cAAA,OAGAyV,QAAA,CAAAU,IAAA;YAAA;cAAAT,MAAA,GAAAK,UAAA,CAAAvR,IAAA;cAAA,MACA,CAAAkR,MAAA,KAAAA,MAAA,CAAA7F,MAAA;gBAAAkG,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACAuF,QAAA;cAEA;gBACA;gBACAC,UAAA,GAAAvX,IAAA,CAAAiT,KAAA,CAAAoE,MAAA,CAAA7F,MAAA;gBACA8F,QAAA,GACAC,UAAA,CAAA/F,MAAA,IACA+F,UAAA,CAAAQ,IAAA,IACAR,UAAA,CAAArF,OAAA,IACAmF,MAAA,CAAA7F,MAAA;cACA,SAAA0B,CAAA;gBACA;gBACAoE,QAAA,GAAAD,MAAA,CAAA7F,MAAA;cACA;;cAEA;cACAwD,eAAA,GAAAsC,QAAA,CAAAjE,OAAA;cACA4B,aAAA,GAAAqC,QAAA,CAAAjE,OAAA,cAEA;cACA,IAAA2B,eAAA,WAAAC,aAAA;gBACA;gBACAqC,QAAA,GAAAA,QAAA,CAAAzQ,SAAA,CAAAoO,aAAA,MAAAtM,IAAA;cACA;;cAEA;cACA2O,QAAA,GAAAA;cACA;cAAA,CACArJ,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAtF,IAAA;;cAEA;cAAA,MACA,CAAA2O,QAAA,IAAAA,QAAA,CAAA5T,MAAA;gBAAAgU,UAAA,CAAA/V,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACA;cACAyF,SAAA,GAAAF,QAAA,EAEA;cACA;cACAE,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,iDACA,WACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,qDACA,aACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,qDACA,aACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,+DACA,WACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,8FACA,WACA;cAEA,KAAAuJ,SAAA,CAAAhV,QAAA,kBAAAgV,SAAA,CAAAhV,QAAA;gBACA;gBACAgV,SAAA,GACA,oBACA,WACA,WACA,6BACA,6EACA,yCACA,YACA,WACA,OACAF,QAAA,GACA,YACA;cACA,WACA,CAAAE,SAAA,CAAAhV,QAAA,eACAgV,SAAA,CAAAhV,QAAA,aACA;gBACA;gBACAgV,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,UACA,iDACA;cACA;;cAEA;cACAgJ,OAAA,CAAA/T,SAAA;gBACA,IAAA+T,OAAA,CAAA3S,KAAA,CAAAqT,YAAA;kBACA;kBACA,IAAAV,OAAA,CAAAtY,kBAAA;oBACA;sBACAsY,OAAA,CAAAtY,kBAAA,CAAAqZ,MAAA;sBACAf,OAAA,CAAAtY,kBAAA,CAAAsZ,OAAA;oBACA,SAAA/E,CAAA;sBACApT,OAAA,CAAAD,KAAA,mBAAAqT,CAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAgF,MAAA,GAAAhT,QAAA,CAAAiT,aAAA;kBACAD,MAAA,CAAAE,KAAA,CAAAC,KAAA;kBACAH,MAAA,CAAAE,KAAA,CAAAE,MAAA;kBACAJ,MAAA,CAAAE,KAAA,CAAAG,MAAA;kBACAL,MAAA,CAAAE,KAAA,CAAAI,OAAA;kBACAN,MAAA,CAAAE,KAAA,CAAAK,QAAA;;kBAEA;kBACAxB,OAAA,CAAAtY,kBAAA,GAAAuZ,MAAA;;kBAEA;kBACAjB,OAAA,CAAA3S,KAAA,CAAAqT,YAAA,CAAAC,SAAA;kBACAX,OAAA,CAAA3S,KAAA,CAAAqT,YAAA,CAAAe,WAAA,CAAAR,MAAA;;kBAEA;kBACAA,MAAA,CAAAF,MAAA;oBACA;sBACA;sBACA,IACA,CAAAE,MAAA,CAAAS,aAAA,CAAAC,KAAA,IACA,CAAAV,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAAC,aAAA,CACA,2BACA,GACA;wBACA;wBACA,IAAA0T,WAAA,GACAX,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAAiT,aAAA;wBACAU,WAAA,CAAAC,GAAA;wBACAZ,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAA6T,IAAA,CAAAL,WAAA,CAAAG,WAAA;;wBAEA;wBACAA,WAAA,CAAAb,MAAA;0BACAf,OAAA,CAAA+B,oBAAA,CAAAd,MAAA;wBACA;;wBAEA;wBACAW,WAAA,CAAAZ,OAAA;0BACAnY,OAAA,CAAAD,KAAA;0BACAoX,OAAA,CAAAvY,YAAA;wBACA;sBACA;wBACA;wBACAuY,OAAA,CAAA+B,oBAAA,CAAAd,MAAA;sBACA;oBACA,SAAAhF,CAAA;sBACApT,OAAA,CAAAD,KAAA,YAAAqT,CAAA;sBACA+D,OAAA,CAAAvY,YAAA;oBACA;kBACA;;kBAEA;kBACAwZ,MAAA,CAAAD,OAAA;oBACAnY,OAAA,CAAAD,KAAA;oBACAoX,OAAA,CAAAvY,YAAA;kBACA;;kBAEA;kBACA,IAAAua,GAAA,GAAAf,MAAA,CAAAS,aAAA,CAAAzT,QAAA;kBACA+T,GAAA,CAAArL,IAAA;kBACAqL,GAAA,CAAAC,KAAA,CAAA1B,SAAA;kBACAyB,GAAA,CAAAE,KAAA;gBACA;cACA;cAAAzB,UAAA,CAAA/V,IAAA;cAAA;YAAA;cAAA+V,UAAA,CAAAhW,IAAA;cAAAgW,UAAA,CAAAjV,EAAA,GAAAiV,UAAA;cAEA5X,OAAA,CAAAD,KAAA,YAAA6X,UAAA,CAAAjV,EAAA;cACAwU,OAAA,CAAAlX,QAAA,CAAAF,KAAA,CAAA6X,UAAA,CAAAjV,EAAA,CAAA/B,OAAA;cACAuW,OAAA,CAAAmC,gBAAA;YAAA;YAAA;cAAA,OAAA1B,UAAA,CAAAhV,IAAA;UAAA;QAAA,GAAAwU,SAAA;MAAA;IAEA;IACA;IACAF,mBAAA,WAAAA,oBAAA;MAAA,IAAAqC,OAAA;MAAA,WAAAlY,kBAAA,CAAAJ,OAAA,mBAAAK,oBAAA,CAAAL,OAAA,IAAAM,IAAA,UAAAiY,UAAA;QAAA,IAAAnC,aAAA,EAAA1G,MAAA,EAAA2G,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAE,SAAA;QAAA,WAAApW,oBAAA,CAAAL,OAAA,IAAAQ,IAAA,UAAAgY,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9X,IAAA,GAAA8X,UAAA,CAAA7X,IAAA;YAAA;cAAA,MAEA0X,OAAA,CAAAhe,GAAA,CAAAqI,MAAA;gBAAA8V,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cACA0X,OAAA,CAAAtZ,QAAA,CAAAkP,OAAA;cAAA,OAAAuK,UAAA,CAAA7R,MAAA;YAAA;cAAA,MAIA0R,OAAA,CAAAhe,GAAA,CAAAqI,MAAA;gBAAA8V,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cACA0X,OAAA,CAAAtZ,QAAA,CAAAkP,OAAA;cAAA,OAAAuK,UAAA,CAAA7R,MAAA;YAAA;cAIA;cACA0R,OAAA,CAAA7a,kBAAA;cACA6a,OAAA,CAAA3a,YAAA;;cAEA;cACA,IAAA2a,OAAA,CAAA/U,KAAA,CAAAqT,YAAA;gBACA0B,OAAA,CAAA/U,KAAA,CAAAqT,YAAA,CAAAC,SAAA;cACA;cAAA4B,UAAA,CAAA9X,IAAA;cAAA8X,UAAA,CAAA7X,IAAA;cAAA,OAIAkK,cAAA,CAAAgM,QAAA,CAAAwB,OAAA,CAAAhe,GAAA;YAAA;cAAA8b,aAAA,GAAAqC,UAAA,CAAArT,IAAA;cAAA,MACA,CAAAgR,aAAA,CAAAnd,IAAA,KAAAmd,aAAA,CAAAnd,IAAA,CAAAkY,OAAA;gBAAAsH,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGAtB,MAAA,GAAA4I,OAAA,CAAAta,WAAA,UAAAwH,MAAA,CAAA4Q,aAAA,CAAAnd,IAAA,CAAAkY,OAAA,GAEA;cAAAsH,UAAA,CAAA7X,IAAA;cAAA,OACA,IAAA4U,gBAAA,EAAA9F,MAAA;YAAA;cAAA2G,QAAA,GAAAoC,UAAA,CAAArT,IAAA;cAAA,IAEAiR,QAAA,CAAA/E,EAAA;gBAAAmH,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAAAyH,UAAA,CAAA7X,IAAA;cAAA,OAGAyV,QAAA,CAAAU,IAAA;YAAA;cAAAT,MAAA,GAAAmC,UAAA,CAAArT,IAAA;cAAA,MACA,CAAAkR,MAAA,KAAAA,MAAA,CAAAb,OAAA;gBAAAgD,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACAuF,QAAA,GAAAD,MAAA,CAAAb,OAAA,IAAA9V,OAAA,CAAAwR,OAAA,EAEA;cACAoF,QAAA,GAAAA;cACA;cAAA,CACArJ,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAA,OAAA;cACA;cAAA,CACAtF,IAAA;;cAEA;cAAA,MACA,CAAA2O,QAAA,IAAAA,QAAA,CAAA5T,MAAA;gBAAA8V,UAAA,CAAA7X,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAoQ,KAAA;YAAA;cAGA;cACA;cACAyF,SAAA,GAAAF,QAAA,EAEA;cACA;cACAE,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,iDACA,WACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,qDACA,aACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,qDACA,aACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,+DACA,WACA;cACA;cACAuJ,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,8FACA,WACA;cAEA,KAAAuJ,SAAA,CAAAhV,QAAA,kBAAAgV,SAAA,CAAAhV,QAAA;gBACA;gBACAgV,SAAA,GACA,oBACA,WACA,WACA,6BACA,6EACA,yCACA,YACA,WACA,OACAF,QAAA,GACA,YACA;cACA,WACA,CAAAE,SAAA,CAAAhV,QAAA,eACAgV,SAAA,CAAAhV,QAAA,aACA;gBACA;gBACAgV,SAAA,GAAAA,SAAA,CAAAvJ,OAAA,CACA,UACA,iDACA;cACA;;cAEA;cACAoL,OAAA,CAAAnW,SAAA;gBACA,IAAAmW,OAAA,CAAA/U,KAAA,CAAAqT,YAAA;kBACA;kBACA,IAAA0B,OAAA,CAAA1a,kBAAA;oBACA;sBACA0a,OAAA,CAAA1a,kBAAA,CAAAqZ,MAAA;sBACAqB,OAAA,CAAA1a,kBAAA,CAAAsZ,OAAA;oBACA,SAAA/E,CAAA;sBACApT,OAAA,CAAAD,KAAA,mBAAAqT,CAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAgF,MAAA,GAAAhT,QAAA,CAAAiT,aAAA;kBACAD,MAAA,CAAAE,KAAA,CAAAC,KAAA;kBACAH,MAAA,CAAAE,KAAA,CAAAE,MAAA;kBACAJ,MAAA,CAAAE,KAAA,CAAAG,MAAA;kBACAL,MAAA,CAAAE,KAAA,CAAAI,OAAA;kBACAN,MAAA,CAAAE,KAAA,CAAAK,QAAA;;kBAEA;kBACAY,OAAA,CAAA1a,kBAAA,GAAAuZ,MAAA;;kBAEA;kBACAmB,OAAA,CAAA/U,KAAA,CAAAqT,YAAA,CAAAC,SAAA;kBACAyB,OAAA,CAAA/U,KAAA,CAAAqT,YAAA,CAAAe,WAAA,CAAAR,MAAA;;kBAEA;kBACAA,MAAA,CAAAF,MAAA;oBACA;sBACA;sBACA,IACA,CAAAE,MAAA,CAAAS,aAAA,CAAAC,KAAA,IACA,CAAAV,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAAC,aAAA,CACA,2BACA,GACA;wBACA;wBACA,IAAA0T,WAAA,GACAX,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAAiT,aAAA;wBACAU,WAAA,CAAAC,GAAA;wBACAZ,MAAA,CAAAS,aAAA,CAAAzT,QAAA,CAAA6T,IAAA,CAAAL,WAAA,CAAAG,WAAA;;wBAEA;wBACAA,WAAA,CAAAb,MAAA;0BACAqB,OAAA,CAAAL,oBAAA,CAAAd,MAAA;wBACA;;wBAEA;wBACAW,WAAA,CAAAZ,OAAA;0BACAnY,OAAA,CAAAD,KAAA;0BACAwZ,OAAA,CAAA3a,YAAA;wBACA;sBACA;wBACA;wBACA2a,OAAA,CAAAL,oBAAA,CAAAd,MAAA;sBACA;oBACA,SAAAhF,CAAA;sBACApT,OAAA,CAAAD,KAAA,YAAAqT,CAAA;sBACAmG,OAAA,CAAA3a,YAAA;oBACA;kBACA;;kBAEA;kBACAwZ,MAAA,CAAAD,OAAA;oBACAnY,OAAA,CAAAD,KAAA;oBACAwZ,OAAA,CAAA3a,YAAA;kBACA;;kBAEA;kBACA,IAAAua,GAAA,GAAAf,MAAA,CAAAS,aAAA,CAAAzT,QAAA;kBACA+T,GAAA,CAAArL,IAAA;kBACAqL,GAAA,CAAAC,KAAA,CAAA1B,SAAA;kBACAyB,GAAA,CAAAE,KAAA;gBACA;cACA;cAAAK,UAAA,CAAA7X,IAAA;cAAA;YAAA;cAAA6X,UAAA,CAAA9X,IAAA;cAAA8X,UAAA,CAAA/W,EAAA,GAAA+W,UAAA;cAEA1Z,OAAA,CAAAD,KAAA,YAAA2Z,UAAA,CAAA/W,EAAA;cACA4W,OAAA,CAAAtZ,QAAA,CAAAF,KAAA,CAAA2Z,UAAA,CAAA/W,EAAA,CAAA/B,OAAA;cACA2Y,OAAA,CAAAD,gBAAA;YAAA;YAAA;cAAA,OAAAI,UAAA,CAAA9W,IAAA;UAAA;QAAA,GAAA4W,SAAA;MAAA;IAEA;IACA;IACAF,gBAAA,WAAAA,iBAAA;MACA,KAAAjb,SAAA;MACA,KAAAK,kBAAA;MACA,KAAAC,SAAA;MACA,KAAAC,YAAA;MACA,KAAAR,YAAA;;MAEA;MACA,SAAAS,kBAAA,SAAAA,kBAAA,CAAAga,aAAA;QACA;UACA;UACA,SAAAha,kBAAA,CAAAga,aAAA,CAAAC,KAAA;YACA,IAAAa,SAAA,GACA,KAAA9a,kBAAA,CAAAga,aAAA,CAAAC,KAAA,CAAAa,SAAA;YACA,IAAAA,SAAA;cACAC,MAAA,CAAAC,MAAA,CAAAF,SAAA,EAAA1V,OAAA,WAAA6V,QAAA;gBACA,IAAAA,QAAA,WAAAA,QAAA,CAAAC,OAAA;kBACAD,QAAA,CAAAC,OAAA;gBACA;cACA;YACA;UACA;QACA,SAAA3G,CAAA;UACApT,OAAA,CAAAD,KAAA,iBAAAqT,CAAA;QACA;MACA;;MAEA;MACA,SAAA5O,KAAA,CAAAqT,YAAA;QACA,KAAArT,KAAA,CAAAqT,YAAA,CAAAC,SAAA;MACA;;MAEA;MACA,SAAAjZ,kBAAA;QACA;UACA,KAAAA,kBAAA,CAAAqZ,MAAA;UACA,KAAArZ,kBAAA,CAAAsZ,OAAA;UACA,KAAAtZ,kBAAA;QACA,SAAAuU,CAAA;UACApT,OAAA,CAAAD,KAAA,gBAAAqT,CAAA;QACA;MACA;IACA;IACA;IACA8F,oBAAA,WAAAA,qBAAAd,MAAA;MAAA,IAAA4B,OAAA;MACA;MACAha,OAAA,CAAA8R,GAAA;;MAEA;MACAhO,UAAA;QACAkW,OAAA,CAAApb,YAAA;MACA;IACA;IAEA;IACAkK,mBAAA,WAAAA,oBAAAmR,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAArW,MAAA;QACA,OAAAqW,QAAA;MACA;MAEA,IAAAC,QAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;;MAEA;MACAH,QAAA,CAAAhW,OAAA,WAAAlF,OAAA;QACA;QACA,IAAAsb,UAAA,GAAAtb,OAAA,CAAA6J,KAAA,GACA7J,OAAA,CAAA6J,KAAA,CAAAuF,OAAA,iBAAAA,OAAA,eACA;QAEA,IAAA+L,QAAA,CAAArQ,GAAA,CAAAwQ,UAAA;UACAH,QAAA,CAAAI,GAAA,CAAAD,UAAA,EAAApT,KAAA;QACA;UACAiT,QAAA,CAAAK,GAAA,CAAAF,UAAA;YACAtb,OAAA,MAAAiC,cAAA,CAAAC,OAAA,MAAAlC,OAAA;YACAkI,KAAA;YACAuT,aAAA,EAAAzb,OAAA,CAAA6J,KAAA;UACA;QACA;MACA;;MAEA;MACAsR,QAAA,CAAAjW,OAAA,WAAAwW,KAAA;QAAA,IAAA1b,OAAA,GAAA0b,KAAA,CAAA1b,OAAA;UAAAkI,KAAA,GAAAwT,KAAA,CAAAxT,KAAA;UAAAuT,aAAA,GAAAC,KAAA,CAAAD,aAAA;QACA,IAAAvT,KAAA;UACA;UACA;UACAlI,OAAA,CAAA6J,KAAA,MAAAnC,MAAA,CAAA+T,aAAA,kBAAA/T,MAAA,CAAAQ,KAAA;QACA;QACAmT,MAAA,CAAA7V,IAAA,CAAAxF,OAAA;MACA;MAEA,OAAAqb,MAAA;IACA;EACA;AACA", "ignoreList": []}]}