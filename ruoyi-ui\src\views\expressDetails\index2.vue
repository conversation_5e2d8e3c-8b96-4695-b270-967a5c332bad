<template>
  <div class="drawer_box" v-loading="loading">
    <div
      class="drawer_Style"
      :style="{ marginBottom: type ? '40px' : '100px' }"
    >
      <div>
        <!--p class="title">
          {{ drawerInfo.cnTitle || drawerInfo.title }}
        </p-->
        <p class="title">
          {{ drawerInfo.cnTitle }}
        </p>
        <p class="title" v-if="titleShow">
          {{ drawerInfo.title }}
        </p>
        <p>
          <span class="source">{{ drawerInfo.sourceName }}</span>
          <span class="time">{{ drawerInfo.publishTime }}</span>
          <span class="author">{{ drawerInfo.author }}</span>
        </p>
        <el-divider></el-divider>
        <div class="summary" v-if="drawerInfo.cnSummary || drawerInfo.summary">
          <div class="summary-item1">
            小信导读：<span style="color: #838484">【内容由大模型生成】</span>
          </div>
          <div class="summary-item2">
            <text-ellipsis
              :text="drawerInfo.cnSummary || drawerInfo.summary"
              :max-lines="10"
              more-text="展开"
              less-text="收起"
            ></text-ellipsis>
          </div>
        </div>
        <!-- 内容区域 -->
        <div v-if="!contentReady && isWeixinArticle" class="content-loading">
          <el-alert
            title="正在处理微信图片，请稍等..."
            type="info"
            center
            :closable="false"
            show-icon
          ></el-alert>
          <div v-if="totalImages > 0" class="loading-progress">
            <div class="loading-text">
              图片加载进度: {{ loadedImages }}/{{ totalImages }}
            </div>
            <el-progress
              :percentage="Math.floor((loadedImages / totalImages) * 100)"
              :show-text="false"
              status="success"
            ></el-progress>
          </div>
        </div>
        <div
          v-else
          style="line-height: 30px; white-space: normal; word-break: break-word"
          v-html="htmlJson"
        ></div>
        <el-empty description="当前文章暂无数据" v-if="!htmlJson"></el-empty>
      </div>
      <div class="liuyanBox" :style="{ height: type ? '50px' : '110px' }">
        <div class="morenzhuangtai" v-if="type">
          <div class="uesr">
            <img :src="avatar" class="avatar" />
            <div class="name">{{ name }}</div>
          </div>
          <div class="button">
            <el-tooltip
              class="item"
              effect="dark"
              :content="drawerInfo.recommend ? '取消推荐' : '推荐'"
              placement="top"
            >
              <el-button
                :icon="
                  drawerInfo.recommend
                    ? 'el-icon-message-solid'
                    : 'el-icon-bell'
                "
                :type="drawerInfo.recommend ? 'primary' : ''"
                circle
                @click="handleRecommend(drawerInfo)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="drawerInfo.collection ? '取消收藏' : '收藏'"
              placement="top"
            >
              <el-button
                :icon="
                  drawerInfo.collection ? 'el-icon-star-on' : 'el-icon-star-off'
                "
                :type="drawerInfo.collection ? 'primary' : ''"
                circle
                @click="handleCollections(drawerInfo)"
              ></el-button>
            </el-tooltip>
            <el-button type="text" icon="el-icon-edit" @click="type = false"
              >写留言</el-button
            >
          </div>
        </div>
        <div class="shuruzhuangtai" v-else>
          <div class="top">
            <div class="uesr">
              <img :src="avatar" class="avatar" />
              <div class="name">{{ name }}</div>
            </div>
            <div class="button">
              <el-button size="mini" type="primary" @click="submit"
                >确定</el-button
              >
              <el-button size="mini" type="info" @click="close">取消</el-button>
            </div>
          </div>
          <div class="bottom">
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="textarea"
              maxlength="300"
              show-word-limit
              resize="none"
            ></el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="sisterDetails">
        <div class="right_title">媒体详情</div>
        <el-divider class="el-divider-right"></el-divider>
        <div class="media-info">
          <div class="media-info-item">
            <div class="name">
              媒体名称:<span class="value">{{ drawerInfo.sourceName }}</span>
            </div>
            <div class="name">媒体等级:<span class="value"></span></div>
          </div>
          <div class="media-info-item">
            <div class="name">媒体地域:<span class="value"></span></div>
            <div class="name">媒体行业:<span class="value"></span></div>
          </div>
        </div>
      </div>
      <div
        class="markdown"
        v-if="keyPointsFormatted || entitiesFormatted"
        :style="{
          height: keyPointsFormatted && entitiesFormatted ? '810px' : '430px',
        }"
      >
        <div class="right_title">
          <span style="color: #971231">小信解读</span>
          <span style="color: #838484">【内容由大模型生成】</span>
        </div>
        <el-divider class="el-divider-markdown"></el-divider>
        <div
          class="markdown-content"
          style="margin-top: 10px"
          v-if="keyPointsFormatted"
        >
          <div class="markdown-content-title">
            内容要点
            <span class="view-markmap" @click="openMarkmap('keyPoints')"
              >点击放大查看</span
            >
          </div>
          <div
            class="markdown-content-text"
            v-html="keyPointsFormatted"
            style="background-color: #e8d9cc"
          ></div>
        </div>
        <div
          class="markdown-content"
          :style="{ marginTop: keyPointsFormatted ? '20px' : '10px' }"
          v-if="entitiesFormatted"
        >
          <div class="markdown-content-title">
            人员/机构/技术/产品
            <span class="view-markmap" @click="openMarkmap('entities')"
              >点击放大查看</span
            >
          </div>
          <div
            class="markdown-content-text"
            v-html="entitiesFormatted"
            style="background-color: #dce4d4"
          ></div>
        </div>
      </div>
      <div class="recommendedArticle">
        <div class="right_title">推荐文章</div>
        <el-divider class="el-divider-right"></el-divider>
        <div class="articleBox" v-for="item in articleList" :key="item.id">
          <div class="article" @click="openNewView(item)">
            {{ item.cnTitle }}
          </div>
          <div class="bottom">
            <div class="time">{{ item.publishTime }}</div>
            <div class="sourceName">{{ item.sourceName }}</div>
            <span class="count">{{ `推荐数量：${item.count}` }}</span>
          </div>
        </div>
      </div>
      <!-- <div class="keyword">
        <div class="right_title">关键词</div>
        <el-divider class="el-divider-right"></el-divider>
        <div class="chartBox">
          <el-image style="width: 100%; height: 290px" :src="url">
          </el-image>
        </div>
      </div> -->
      <!-- <div class="articleEntity">
        <div class="right_title">文章实体</div>
        <el-divider class="el-divider-right"></el-divider>
        <div class="chartBox">
          <el-radio-group v-model="tabPosition" style="margin-bottom: 30px; position: absolute;   z-index: 3;">
            <el-radio-button label="1">通用</el-radio-button>
            <el-radio-button label="2">人物</el-radio-button>
            <el-radio-button label="3">地域</el-radio-button>
            <el-radio-button label="4">机构</el-radio-button>
          </el-radio-group>
          <el-image style="width: 100%; height: 290px" :src="url">
          </el-image>
        </div>
      </div> -->
    </div>
    <div class="tabs-all">
      <div class="tabs" v-if="translationBtnShow">
        <span @click="viewOriginal()">{{
          this.originalArticleShow ? "查看原文" : "查看中文"
        }}</span>
      </div>
      <div class="tabs">
        <span @click="viewOriginalArticle(drawerInfo)">原文链接</span>
      </div>
      <div
        class="tabs"
        v-hasPermi="['article:articleList:monitoringAndSpecial']"
        v-if="translationBtnShow"
      >
        <span @click="translateEvent(drawerInfo)">机器翻译</span>
      </div>
      <!-- <div class="tabs" v-if="translationBtnShow">
        <span @click="">人工翻译</span>
      </div> -->

      <div class="tabs">
        <span @click="backToTop()">返回顶部</span>
      </div>
    </div>
    <markmap-dialog
      :visible.sync="markmapVisible"
      :content="markmapContent"
      :title="markmapTitle"
      :loading="markmapLoading"
      @close="handleMarkmapClose"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import API from "@/api/ScienceApi/index.js";
import { feedbackAdd } from "@/api/article/leaveMessage";
import {
  containsHtmlTags,
  extractHtmlTags,
  hasValidHtmlStructure,
} from "@/utils/htmlUtils";
import { formatMarkdown } from "@/utils/markdownUtils";
import MarkmapDialog from "./MarkmapDialog.vue";
import TextEllipsis from "@/components/TextEllipsis/index.vue";

export default {
  dicts: [],
  components: {
    MarkmapDialog,
    TextEllipsis,
  },
  data() {
    return {
      loading: false,
      contentReady: false,
      translationBtnShow: true,
      titleShow: true,
      drawerInfo: {},
      htmlJson: "",
      processedHtml: "",
      originalArticleShow: true,
      articleList: [],
      type: true,
      textarea: "",
      url: "",
      tabPosition: 1,
      totalImages: 0,
      loadedImages: 0,
      imageLoadTimeout: null,
      markdownContent: {
        keyPoints: "",
        entities: "",
      },
      markmapVisible: false,
      markmapContent: "",
      markmapTitle: "",
      markmapLoading: false,
    };
  },
  computed: {
    ...mapGetters(["roles", "name", "avatar"]),
    isWeixinArticle() {
      return (
        this.drawerInfo.originalUrl &&
        this.drawerInfo.originalUrl.includes("https://mp.weixin.qq.com")
      );
    },
    keyPointsFormatted() {
      return formatMarkdown(this.markdownContent.keyPoints);
    },
    entitiesFormatted() {
      return formatMarkdown(this.markdownContent.entities);
    },
  },
  mounted() {
    this.loading = true;
    this.translationBtnShow = true;

    // 添加全局meta标签禁用referrer
    this.addNoReferrerMeta();

    this.details();
    this.getIndexData();
    // this.hanldeBrowseAdd();
  },
  watch: {
    tabPosition(newVal, oldVal) {
      console.log(`Tab position changed from ${oldVal} to ${newVal}`);
    },
  },
  methods: {
    // 添加全局meta标签来禁用所有引用头
    addNoReferrerMeta() {
      // 检查是否已经存在meta标签
      if (
        document.querySelector('meta[name="referrer"][content="no-referrer"]')
      ) {
        return;
      }

      // 创建并添加meta标签
      const meta = document.createElement("meta");
      meta.name = "referrer";
      meta.content = "no-referrer";
      document.head.appendChild(meta);
      console.log("已添加no-referrer meta标签");
    },

    // 辅助方法：修复图片的referrer设置
    fixImageReferrer(content) {
      // 确保每个微信图片标签都有referrerpolicy="no-referrer"属性
      if (!content) return content;

      // 替换所有微信图片标签，确保它们有referrerpolicy属性
      return content.replace(
        /<img([^>]*?src=["']https?:\/\/mmbiz\.q(?:logo|pic)\.cn\/[^"']+["'][^>]*?)>/gi,
        (match, attrPart) => {
          // 如果已经有referrerpolicy属性，不再添加
          if (attrPart.includes("referrerpolicy")) {
            return match;
          }
          // 添加referrerpolicy属性
          return `<img${attrPart} referrerpolicy="no-referrer">`;
        }
      );
    },

    // 使用canvas或图片代理增强微信图片的方法（不替换DOM节点）
    replaceAllWechatImages() {
      // 如果不是微信文章，直接返回
      if (!this.isWeixinArticle) {
        return Promise.resolve();
      }

      return new Promise((resolve) => {
        try {
          // 找到所有微信域名的图片
          const wechatImages = document.querySelectorAll(
            'img[src*="mmbiz.qpic.cn"], img[src*="mmbiz.qlogo.cn"], img[data-src*="mmbiz"], img[src*="mmsns.qpic.cn"]'
          );

          if (wechatImages.length === 0) {
            resolve();
            return;
          }

          console.log(`开始增强${wechatImages.length}张微信图片，保留现有图片`);
          let processedCount = 0;

          // 处理每一张图片
          wechatImages.forEach((img, index) => {
            // 如果图片已经被替换过且非空白，则跳过
            if (
              img.hasAttribute("data-wx-replaced") &&
              img.complete &&
              img.naturalWidth > 0
            ) {
              processedCount++;
              if (processedCount >= wechatImages.length) {
                resolve();
              }
              return;
            }

            // 记录原始尺寸和样式
            const originalWidth = img.style.width || img.width || "auto";
            const originalHeight = img.style.height || img.height || "auto";

            // 只有当图片无法显示时才进行处理
            if (!img.complete || img.naturalWidth === 0) {
              // 获取图片源
              const originalSrc =
                img.getAttribute("data-original-src") ||
                img.getAttribute("src");
              if (!originalSrc) {
                // 无法获取源，跳过
                processedCount++;
                if (processedCount >= wechatImages.length) {
                  resolve();
                }
                return;
              }

              // 添加必要的属性
              img.setAttribute("referrerpolicy", "no-referrer");
              img.classList.add("wx-img");
              img.style.maxWidth = "100%";
              img.style.height = "auto";

              // 设置标识
              img.setAttribute("data-wx-replaced", "true");

              // 尝试使用HTTPS加载
              if (originalSrc.startsWith("http:")) {
                const httpsUrl = originalSrc.replace(/^http:/, "https:");
                console.log(`尝试使用HTTPS协议: ${httpsUrl}`);
                img.src = httpsUrl;
              }
              // 尝试添加格式参数
              else if (!originalSrc.includes("wx_fmt=")) {
                const separator = originalSrc.includes("?") ? "&" : "?";
                const srcWithFormat = `${originalSrc}${separator}wx_fmt=jpeg`;
                console.log(`尝试添加格式参数: ${srcWithFormat}`);
                img.src = srcWithFormat;
              }
            }

            // 无论是否处理，都计数
            processedCount++;
            if (processedCount >= wechatImages.length) {
              resolve();
            }
          });

          // 设置超时保障
          setTimeout(() => {
            resolve();
          }, 3000);
        } catch (e) {
          console.error("增强微信图片出错:", e);
          resolve();
        }
      });
    },

    // 强制重新加载内容区域，避免清空导致图片丢失
    forceReloadContent() {
      // 如果没有内容或不是微信文章，直接返回
      if (!this.htmlJson || !this.isWeixinArticle) {
        return;
      }

      console.log("开始增强图片显示处理，保留现有DOM结构");

      // 不再清空内容，直接在现有DOM上处理图片
      this.$nextTick(() => {
        // 先进行基础修复
        this.fixWechatImages();

        // 延迟执行彻底替换
        setTimeout(() => {
          this.replaceAllWechatImages().then(() => {
            console.log("完成彻底替换微信图片");
            // 最后强制更新一次视图
            this.$forceUpdate();
          });
        }, 100);
      });
    },

    // 预处理微信图片
    preloadWechatImages(content) {
      return new Promise((resolve) => {
        try {
          // 先应用全局referrer策略
          this.addNoReferrerMeta();

          // 先修复内容中包含转义字符的图片标签
          content = this.fixEscapedImageTags(content);

          // 修复内容中的图片标签
          content = this.handleWechatImages(content);

          // 处理相对路径图片
          content = this.handleRelativeImagePaths(content);

          // 标记内容准备好了
          this.contentReady = true;
          resolve(content);

          // 在内容加载后，下一个事件循环再应用修复
          setTimeout(() => {
            this.fixWechatImages();
          }, 0);
        } catch (e) {
          console.error("预处理微信图片出错:", e);
          this.contentReady = true;
          resolve(content);
        }
      });
    },

    // 修复包含转义字符的图片标签 - 更彻底的处理
    fixEscapedImageTags(content) {
      try {
        if (!content) return content;

        // 先处理全局的转义字符，简化后续处理
        content = content
          .replace(/\\"/g, '"')
          .replace(/\\'/g, "'")
          .replace(/\\\\/g, "\\")
          .replace(/\\&quot;/g, "&quot;")
          .replace(/&amp;/g, "&");

        // 扩展匹配模式，捕获所有可能的问题格式
        const escapedTagRegex =
          /<img[^>]*?(?:src|data-src)=["']?(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")([^"']+?)(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")["']?[^>]*?>/gi;
        const badStyleRegex =
          /<img[^>]*?style=["'][^"']*?(?:16px|white-space)[^"']*?["'][^>]*?>/gi;
        const brokenPathRegex =
          /<img[^>]*?src=["'][^"']*?mmbiz[^"']*?["'][^>]*?>/gi;

        // 合并所有匹配结果
        const escapedTags = content.match(escapedTagRegex) || [];
        const badStyleTags = content.match(badStyleRegex) || [];
        const brokenPathTags = content.match(brokenPathRegex) || [];

        // 去重 - 转换为Set然后再转回数组
        const allTags = [
          ...new Set([...escapedTags, ...badStyleTags, ...brokenPathTags]),
        ];

        if (allTags.length === 0) {
          return content; // 没有找到需要修复的标签
        }

        console.log(`找到${allTags.length}个可能有问题的图片标签`);

        // 处理每个问题标签
        for (const tag of allTags) {
          // 提取图片URL - 尝试多种模式
          let imgUrl = "";

          // 尝试匹配各种可能的src格式
          const patterns = [
            /src=["']?(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?([^"'<>\s]+?mmbiz[^"'<>\s]+)(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?["']?/i,
            /data-src=["']?(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?([^"'<>\s]+?mmbiz[^"'<>\s]+)(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?["']?/i,
            /original-src=["']?(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?([^"'<>\s]+?mmbiz[^"'<>\s]+)(?:\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\")?["']?/i,
          ];

          // 尝试所有模式直到找到匹配项
          for (const pattern of patterns) {
            const match = tag.match(pattern);
            if (match && match[1]) {
              imgUrl = match[1];
              break;
            }
          }

          if (!imgUrl) {
            // 如果仍然无法提取URL，跳过此标签
            continue;
          }

          // 清理URL并添加必要的参数
          imgUrl = imgUrl.replace(
            /\\&quot;|\\"|&quot;|%22|\\\\&quot;|\\\\"/g,
            ""
          );
          if (!imgUrl.includes("wx_fmt=") && imgUrl.includes("mmbiz")) {
            const separator = imgUrl.includes("?") ? "&" : "?";
            imgUrl += `${separator}wx_fmt=jpeg`;
          }

          // 处理可能存在的协议问题
          if (!imgUrl.startsWith("http")) {
            if (imgUrl.startsWith("//")) {
              imgUrl = "https:" + imgUrl;
            } else {
              imgUrl = "https://" + imgUrl;
            }
          }

          // 创建干净的替换标签 - 更简洁但完整
          const newTag = `<img referrerpolicy="no-referrer" class="wx-img" src="${imgUrl}" data-src="${imgUrl}" data-original-src="${imgUrl}" style="max-width:100%;height:auto!important;" />`;

          // 替换原始标签
          content = content.replace(tag, newTag);
        }

        return content;
      } catch (e) {
        console.error("修复图片标签出错:", e);
        return content; // 出错时返回原始内容
      }
    },

    // 动态修复图片方法 - 增强版，实现DOM重新渲染
    fixWechatImages() {
      try {
        // 添加全局样式，确保所有微信图片都有no-referrer
        const style = document.createElement("style");
        style.textContent = `
          img[src*="mmbiz.qpic.cn"], img[src*="mmbiz.qlogo.cn"] {
            max-width: 100% !important;
            height: auto !important;
            display: block !important;
            margin: 10px auto !important;
            object-fit: contain !important;
            -webkit-referrer: no-referrer !important;
            referrerpolicy: no-referrer !important;
          }
        `;
        document.head.appendChild(style);

        // 找到所有微信域名的图片
        const wechatImages = document.querySelectorAll(
          'img[src*="mmbiz.qpic.cn"], img[src*="mmbiz.qlogo.cn"], img[src*="mmsns.qpic.cn"]'
        );

        if (wechatImages.length > 0) {
          console.log(
            `页面中找到${wechatImages.length}张微信图片，应用全局修复`
          );

          wechatImages.forEach((img) => {
            // 添加必要的属性
            img.setAttribute("referrerpolicy", "no-referrer");
            img.classList.add("wx-img");

            // 如果图片尚未进行错误处理，添加错误处理
            if (!img.hasAttribute("data-error-handled")) {
              img.setAttribute("data-error-handled", "true");

              // 添加错误处理
              img.onerror = function () {
                console.log("图片加载失败，应用占位样式");
                this.style.border = "1px dashed #ccc";
                this.style.padding = "10px";
                this.style.width = "auto";
                this.style.height = "auto";
                this.style.minHeight = "100px";
                this.alt = "图片加载失败";
              };
            }
          });
        }
      } catch (e) {
        console.error("修复微信图片出错:", e);
      }

      // 返回一个Promise，方便外部检测完成状态
      return Promise.resolve();
    },

    // 处理相对路径图片的方法
    handleRelativeImagePaths(content) {
      if (!content || !this.drawerInfo?.originalUrl) {
        return content;
      }

      // 从原文链接中提取基础URL
      const originalUrl = this.drawerInfo.originalUrl;
      const urlParts = originalUrl.split('/');
      urlParts.pop(); // 移除文件名
      const baseUrl = urlParts.join('/') + '/';

      // 处理所有相对路径图片
      return content.replace(/<img([^>]*?)src\s*=\s*["']([^"']+)["']([^>]*?)>/gi, (match, before, src, after) => {
        // 跳过绝对路径
        if (src.startsWith('http') || src.startsWith('//')) {
          return match;
        }

        // 跳过非图片文件
        if (!/\.(png|jpg|jpeg|gif|webp|bmp|svg)(\?.*)?$/i.test(src)) {
          return match;
        }

        // 转换相对路径
        let newSrc = '';
        if (src.startsWith('./')) {
          newSrc = baseUrl + src.substring(2);
        } else if (src.startsWith('/')) {
          const urlObj = new URL(originalUrl);
          newSrc = urlObj.protocol + '//' + urlObj.host + src;
        } else {
          newSrc = baseUrl + src;
        }

        // 构建新的img标签，移除所有old*属性
        let newTag = `<img${before} src="${newSrc}"${after}>`;
        newTag = newTag.replace(/\s*(oldsrc|oldSrc|old-src)\s*=\s*["'][^"']*["']/gi, '');

        return newTag;
      });
    },

    // 处理微信图片的公共方法
    handleWechatImages(content) {
      if (!content) return content;

      try {
        // 先处理转义字符问题
        content = content
          .replace(/\\"/g, '"')
          .replace(/\\'/g, "'")
          .replace(/\\\\/g, "\\");

        // 将所有微信图片URL进行提取和替换
        const regex =
          /<img[^>]*?src=["'](https?:\/\/mmbiz\.q(?:logo|pic)\.cn\/[^"']+)["'][^>]*?>/gi;

        // 简单直接的替换，确保每个微信图片都有正确属性
        let newContent = content.replace(
          regex,
          '<img src="$1" referrerpolicy="no-referrer" class="wx-img" style="max-width:100%;height:auto;" />'
        );

        // 还需要处理已被转义的图片URL
        const escapedRegex =
          /<img[^>]*?src=["']?(\\?&quot;|\\?"|&quot;|%22)(https?:\/\/mmbiz[^"'&]+)(\\?&quot;|\\?"|&quot;|%22)["']?[^>]*?>/gi;
        newContent = newContent.replace(
          escapedRegex,
          '<img src="$2" referrerpolicy="no-referrer" class="wx-img" style="max-width:100%;height:auto;" />'
        );

        return newContent;
      } catch (e) {
        console.error("处理微信图片HTML出错:", e);
        return content;
      }
    },

    async getIndexData() {
      await API.recommendHot().then((response) => {
        if (response.code == 200) {
          this.articleList = response.data.slice(0, 5).map((item) => {
            item.cnTitle = item.title;
            item.id = item.articleId;
            return item;
          });
        }
      });
    },

    async details() {
      let params;
      if (this.$route.query.id) {
        params = {
          id: this.$route.query.id,
        };
      } else {
        params = { articleSn: this.$route.query.articleSn };
      }
      // await API.AreaInfo(this.$route.query.id || this.$route.query.articleSn).then(async (res) => {
      await API.articleDetail(params).then(async (res) => {
        if (res.code == 200) {
          this.hanldeBrowseAdd(res.data.id);
          this.drawerInfo = res.data;
          this.drawerInfo.sourceType != "1" && this.drawerInfo.sourceType != "3"
            ? (this.translationBtnShow = true)
            : (this.translationBtnShow = false);

          // 如果是微信文章，先显示加载状态
          if (
            this.drawerInfo.originalUrl &&
            this.drawerInfo.originalUrl.includes("https://mp.weixin.qq.com")
          ) {
            this.contentReady = false;
          } else {
            this.contentReady = true;
          }

          // 预处理内容
          const rawContent =
            this.drawerInfo.cnContent || this.drawerInfo.content;
          let processedContent = this.formattingJson(rawContent);

          // 处理swdt数据
          let keyPoints = "";
          let entities = "";

          if (this.drawerInfo.swdt && Array.isArray(this.drawerInfo.swdt)) {
            this.drawerInfo.swdt.forEach((item) => {
              if (item.swdtTaskid === "1" && item.swdtContent) {
                // 内容要点
                keyPoints = item.swdtContent;
              } else if (item.swdtTaskid === "2" && item.swdtContent) {
                // 人员/机构/技术/产品
                entities = item.swdtContent;
              }
            });
          }

          // 预处理 markdown 内容，确保换行符正确处理
          const preprocessMarkdown = (text) => {
            if (!text) return "";
            // 处理各种转义字符和格式问题
            return text
              .replace(/```markdown\s*|```\s*/g, "") // 移除 markdown 代码块标记
              .replace(/```[a-zA-Z]*\s*/g, "") // 移除带有语言标记的代码块标记
              .replace(/\\n/g, "\n") // 处理双反斜杠换行符
              .replace(/\\\n/g, "\n") // 处理三反斜杠换行符
              .replace(/\r\n/g, "\n") // 处理 Windows 风格换行符
              .replace(/\$\{[^}]+\}/g, ""); // 移除模板字符串占位符
          };

          // 设置 markdown 内容，使用预处理函数
          this.markdownContent.keyPoints = preprocessMarkdown(keyPoints);
          this.markdownContent.entities = preprocessMarkdown(entities);

          // 如果是微信公众号文章，进行预加载处理
          if (
            this.drawerInfo.originalUrl &&
            this.drawerInfo.originalUrl.includes("https://mp.weixin.qq.com")
          ) {
            try {
              // 预加载微信图片 - 不会造成闪烁
              processedContent = await this.preloadWechatImages(
                processedContent
              );
              console.log("微信图片预处理完成");
            } catch (e) {
              console.error("微信图片预处理失败:", e);
              // 出错时，显示内容
              this.contentReady = true;
            }
          }

          // 设置处理后的内容
          this.$set(this, "htmlJson", processedContent);

          if (this.drawerInfo.title == this.drawerInfo.cnTitle) {
            this.titleShow = false;
          }

          document.title = this.drawerInfo.cnTitle || this.drawerInfo.title;
          this.loading = false;

          // 内容显示后，应用全局修复
          if (this.isWeixinArticle) {
            this.$nextTick(() => {
              // 应用图片修复
              this.fixWechatImages();

              // 2秒后再检查一次
              setTimeout(() => {
                this.fixWechatImages();
              }, 2000);
            });
          }
        }
      });
    },
    formattingJson(content) {
      if (content) {
        if (containsHtmlTags(content)) {
          content = content.replace(/<br>/g, "");
          content = content.replace(/\n/g, "");
          content = content.replace(/\\n/g, "");
          content = content.replace(/\\\n/g, "");
          content = content.replace("|xa0", "");
          content = content.replace("opacity: 0", "");

          // 处理微信公众号图片防盗链问题
          if (
            this.drawerInfo.originalUrl &&
            this.drawerInfo.originalUrl.includes("https://mp.weixin.qq.com")
          ) {
            content = this.handleWechatImages(content);
          }

          // 处理相对路径图片
          content = this.handleRelativeImagePaths(content);

          console.log("包含的HTML标签", extractHtmlTags(content));
          console.log("HTML是否结构正确", hasValidHtmlStructure(content));
        } else {
          content = content.replace(/\n/g, "<br>");
          content = content.replace(/\\n/g, "<br>");
          content = content.replace(/\\\n/g, "<br>");
          content = content.replace(/\${[^}]+}/g, "<br>");
          content = content.replace("|xa0", "");
          content = content.replace("opacity: 0", "");

          // 处理微信公众号图片防盗链问题
          if (
            this.drawerInfo.originalUrl &&
            this.drawerInfo.originalUrl.includes("https://mp.weixin.qq.com")
          ) {
            content = this.handleWechatImages(content);
          }

          // 处理相对路径图片
          content = this.handleRelativeImagePaths(content);
        }
      }
      return content;
    },
    // 翻译文章
    translateEvent(row) {
      const fun = () => {
        const loading = this.$loading({
          lock: true,
          text: "Loading",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        API.translationTitle({
          originalText: row.content,
          docId: this.$route.query.id,
          id: row.id,
          translationField: "content",
          translationType: 1,
        })
          .then((res) => {
            this.drawerInfo.cnContent = res.data;
            this.$set(
              this,
              "htmlJson",
              this.formattingJson(
                this.drawerInfo.cnContent || this.drawerInfo.content
              )
            );
            this.originalArticleShow = true;
            loading.close();
          })
          .catch((err) => {
            loading.close();
          });
      };

      if (row.cnContent) {
        // 提示是否确认再次翻译
        this.$confirm("是否确认再次翻译?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            fun();
          })
          .catch(() => {
            this.$set(
              this,
              "htmlJson",
              this.formattingJson(
                this.drawerInfo.cnContent || this.drawerInfo.content
              )
            );
            this.originalArticleShow = true;
          });
      } else {
        fun();
      }
    },
    viewOriginal() {
      if (this.originalArticleShow) {
        this.$set(
          this,
          "htmlJson",
          this.formattingJson(this.drawerInfo.content)
        );
      } else {
        this.$set(
          this,
          "htmlJson",
          this.formattingJson(
            this.drawerInfo.cnContent || this.drawerInfo.content
          )
        );
      }
      this.originalArticleShow = !this.originalArticleShow;
    },
    backToTop() {
      window.scrollTo({ top: 0, behavior: "smooth" });
    },
    close() {
      this.type = true;
      this.textarea = "";
    },
    submit() {
      feedbackAdd({
        articleId: this.$route.query.id,
        docId: this.$route.query.id,
        cnTitle: this.drawerInfo.cnTitle || this.drawerInfo.title,
        content: this.textarea,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "留言成功",
            type: "success",
          });
          this.close();
        }
      });
    },
    openNewView(item) {
      window.open(`/expressDetails?id=${item.id}&docId=${item.id}`, "_blank");
    },
    handleCollections(item) {
      if (!item.collection) {
        API.collectApi([item.id]).then((res) => {
          if (res.code == 200) {
            this.$message({
              message: "收藏成功,请前往个人中心查看",
              type: "success",
            });
            this.$set(this.drawerInfo, "collection", !item.collection);
          } else {
            this.$message({ message: "收藏失败", type: "info" });
          }
        });
      } else {
        API.cocelCollect([item.id]).then((res) => {
          if (res.code == 200) {
            this.$message({ message: "已取消收藏", type: "success" });
            this.$set(this.drawerInfo, "collection", !item.collection);
          } else {
            this.$message({ message: "取消收藏失败", type: "info" });
          }
        });
      }
    },
    handleRecommend(item) {
      let query = new FormData();
      query.append("articleId", item.id);
      if (!item.recommend) {
        API.recommendAdd(query).then((res) => {
          if (res.code == 200) {
            this.$message({
              message: "推荐成功,请前往个人中心查看",
              type: "success",
            });
            this.$set(this.drawerInfo, "recommend", !item.recommend);
          } else {
            this.$message({ message: "推荐失败", type: "info" });
          }
        });
      } else {
        API.recommendCancel(query).then((res) => {
          if (res.code == 200) {
            this.$message({ message: "已取消推荐", type: "success" });
            this.$set(this.drawerInfo, "recommend", !item.recommend);
          } else {
            this.$message({ message: "取消推荐失败", type: "info" });
          }
        });
      }
    },
    hanldeBrowseAdd(id) {
      let query = new FormData();
      query.append("articleId", id);
      API.browseAdd(query);
    },
    viewOriginalArticle(item) {
      window.open(item.originalUrl);
    },
    // 打开思维导图弹窗
    openMarkmap(type) {
      if (!this.markdownContent[type]) {
        this.$message.warning("暂无思维导图数据");
        return;
      }

      // 设置标题和内容
      let title = "";
      if (type === "keyPoints") {
        title = "内容要点思维导图";
      } else if (type === "entities") {
        title = "人员/机构/技术/产品思维导图";
      }

      // 设置内容
      const content = this.markdownContent[type];

      // 使用localStorage存储大型内容，而不是通过URL传递
      const storageKey = `markmap_data_${Date.now()}`;
      localStorage.setItem(storageKey, content);

      // 构建URL参数 - 只传递标题和存储键，不传递大型内容
      const params = new URLSearchParams();
      params.append("title", title);
      params.append(
        "articleTitle",
        this.drawerInfo.cnTitle || this.drawerInfo.title
      );
      params.append("storageKey", storageKey);

      // 获取屏幕尺寸，制定合适的窗口大小
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;

      // 窗口宽高为屏幕的80%，并居中显示
      const width = Math.round(screenWidth * 0.8);
      const height = Math.round(screenHeight * 0.8);
      const left = Math.round((screenWidth - width) / 2);
      const top = Math.round((screenHeight - height) / 2);

      // 尝试打开新窗口
      const windowFeatures = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;
      const newWindow = window.open(
        `/markmap?${params.toString()}`,
        "_blank",
        windowFeatures
      );

      // 检查是否成功打开窗口，如果被浏览器阻止则打开新标签页
      if (
        !newWindow ||
        newWindow.closed ||
        typeof newWindow.closed === "undefined"
      ) {
        // 浏览器可能阻止了弹窗，使用新标签页
        window.open(`/markmap?${params.toString()}`, "_blank");
        this.$message.info("新窗口被浏览器阻止，已在新标签页打开");
      }
    },
    // 关闭思维导图弹窗 - 保留以防需要
    handleMarkmapClose() {
      this.markmapVisible = false;
      this.markmapContent = "";
    },
  },
};
</script>

<style scoped lang="scss">
/* 全局样式 */
::v-deep img[src*="mmbiz"] {
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain !important;
  margin: 10px auto !important;
  display: block !important;
  -webkit-referrer: no-referrer !important;
  referrerpolicy: no-referrer !important;
}

.drawer_box {
  display: flex;
  user-select: text !important;
  background: #f5f7fa;
  min-height: 100vh;
  justify-content: center;

  .drawer_Style {
    position: relative;
    z-index: 2;
    margin: 0px 20px 0px;
    width: 800px;
    background: #ffffff;
    padding: 10px 30px;

    .title {
      font-size: 22px;
      font-weight: 500px;
      // text-align: center;
    }

    .source {
      color: #0798f8;
      // text-align: center;
      font-size: 14px;
    }

    .time {
      font-size: 14px;
      // text-align: center;
      margin-left: 10px;
      color: #9b9b9b;
    }

    .author {
      color: #1a0997;
      // text-align: center;
      margin-left: 10px;
      font-size: 14px;
    }

    .summary {
      background-color: #f5f7fa;
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .summary .summary-item1 {
      color: #971231;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .summary .summary-item2 {
      line-height: 30px;
      word-break: break-word;
    }

    /* 文本展开折叠组件样式调整 */
    .summary ::v-deep .text-ellipsis {
      width: 100%;
    }

    .summary ::v-deep .text-content {
      line-height: 30px;
      font-size: 16px;
      word-break: break-word;
    }

    .summary ::v-deep .text-limited {
      -webkit-line-clamp: 10 !important;
    }

    .summary ::v-deep .ellipsis-actions {
      text-align: right;
      // margin-top: 8px;
    }

    .summary ::v-deep .show-more,
    .summary ::v-deep .show-less {
      color: #0798f8;
      font-size: 14px;
      transition: color 0.3s ease;
    }

    .summary ::v-deep .show-more:hover,
    .summary ::v-deep .show-less:hover {
      color: #0071ce;
    }

    .summary ::v-deep .show-more i,
    .summary ::v-deep .show-less i {
      margin-left: 3px;
      font-size: 12px;
    }
  }
}

.right {
  width: 500px;

  .sisterDetails {
    background-color: #ffffff;
    padding: 10px 20px;
    width: 100%;
    color: #000000;

    .media-info {
      display: flex;
      justify-content: space-between;
      .media-info-item {
        flex: 1;
      }
    }

    .name {
      color: #9b9b9b;
      line-height: 1.8;
      font-size: 14px;

      .value {
        margin-left: 15px;
        color: #000000;
      }
    }
  }

  .markdown {
    margin-top: 20px;
    background-color: #f2e3c5;
    padding: 10px 20px;
    width: 100%;
    color: #000000;
    height: 810px;

    ::v-deep .el-divider {
      background-color: #838484;
      margin: 8px 0;
    }

    .markdown-content {
      margin-bottom: 10px;
      .markdown-content-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .markdown-content-text {
        height: 325px;
        overflow-y: auto;
        padding: 10px;
        line-height: 1.6;
        font-size: 16px;
        color: #333333;
        border-radius: 10px;
      }
    }
  }

  .recommendedArticle {
    margin-top: 20px;
    background-color: #ffffff;
    padding: 10px 20px;
    width: 100%;
    color: #000000;

    .articleBox {
      font-size: 14px;
      margin: 10px 0;

      .article {
        margin-bottom: 5px;
        cursor: pointer;
        position: relative;
        // width: calc(100% - 75px);
        line-height: 1.8;

        &:before {
          content: "•";
          color: red;
          font-weight: bold;
        }
      }

      .bottom {
        display: flex;
        color: #9b9b9b;
        align-items: baseline;

        .sourceName {
          margin-left: 10px;
        }

        .count {
          margin-left: 10px;
        }
      }
    }
  }

  .keyword {
    margin-top: 20px;
    background-color: #ffffff;
    padding: 10px 20px;
    width: 100%;
    color: #000000;
    height: 350px;

    .chartBox {
      height: 300px;
    }
  }

  .articleEntity {
    margin-top: 20px;
    background-color: #ffffff;
    padding: 10px 20px;
    width: 100%;
    color: #000000;
    height: 350px;

    .chartBox {
      height: 300px;
      position: relative;
    }
  }
}

.right_title {
  font-size: 20px;
}

.el-divider-right {
  margin: 8px 0;
}

.liuyanBox {
  position: fixed;
  bottom: 0;
  left: calc(50vw - 653px);
  width: 800px;
  z-index: 99;
  background-color: #e3e3ef;
  padding: 5px 10px;
  border-radius: 5px;

  .morenzhuangtai {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .shuruzhuangtai {
    height: 90px;

    .top {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .bottom {
      height: 50px;
    }
  }
}

.uesr {
  display: flex;
  align-items: center;
  height: 30px;

  .avatar {
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }

  .name {
    margin-left: 10px;
  }
}

.tabs-all {
  position: fixed;
  left: calc(50vw - 693px);
  top: 0px;
  z-index: 99;
  height: 800px;
  width: 40px;

  @media screen and (max-width: 1400px) {
    left: 0;
  }

  .tabs {
    writing-mode: vertical-rl;
    /* 文字从上到下，从右到左 */
    height: 120px;
    width: 40px;
    font-weight: 800;
    font-size: 16px;
    color: #ffffff;
    line-height: 35px;
    text-align: center;
    font-style: normal;
    background: url("../../assets/bigScreenTwo/tab-active.png") no-repeat 0px
      0px !important;
    background-size: 100% 100% !important;
    letter-spacing: 2px;
    margin-bottom: 10px;
    cursor: pointer;
  }
}

.content-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  width: 100%;
  margin: 20px 0;
}

.loading-progress {
  width: 80%;
  max-width: 500px;
  margin-top: 20px;
}

.loading-text {
  text-align: center;
  margin-bottom: 10px;
  color: #409eff;
  font-size: 14px;
}

.preloading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

::v-deep img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

::v-deep video {
  width: 100%;
}

/* 添加微信图片特殊样式 */
::v-deep .wx-img {
  max-width: 100%;
  height: auto !important;
  display: block;
  margin: 10px auto;
}

/* 替换后的图片样式 */
::v-deep .wx-img-replaced {
  max-width: 100%;
  height: auto !important;
  display: block;
  margin: 10px auto;
  border: 1px solid transparent;
}

/* Canvas创建的图片样式 */
::v-deep .wx-img-canvas {
  max-width: 100%;
  height: auto !important;
  display: block;
  margin: 10px auto;
  border: 1px solid transparent;
}

/* 占位图样式 */
::v-deep .wx-img-placeholder {
  max-width: 100%;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px auto;
  border: 1px dashed #ccc;
  padding: 20px;
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* markdown显示样式 */
::v-deep .md-h1,
::v-deep h1.md-h1 {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0 8px 0;
  color: #333333;
}

::v-deep .md-h2,
::v-deep h2.md-h2 {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 6px 0;
  color: #333333;
}

::v-deep .md-h3,
::v-deep h3.md-h3 {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0 6px 0;
  color: #333333;
}

::v-deep .md-li {
  margin: 5px 0;
  padding-left: 10px;
  display: flex;
  align-items: flex-start;
}

::v-deep .md-bullet,
::v-deep .md-number {
  margin-right: 8px;
  color: #333333;
  font-weight: bold;
  flex-shrink: 0;
}

::v-deep .md-blockquote,
::v-deep blockquote.md-blockquote {
  border-left: 4px solid #d0d0d0;
  padding: 5px 10px;
  margin: 10px 0;
  background-color: #f9f9f9;
  font-style: italic;
  color: #555;
}

::v-deep .markdown-content-text strong {
  font-weight: bold;
  color: #333333;
}

::v-deep .markdown-content-text em {
  font-style: italic;
  color: #333333;
}

::v-deep .md-code-block,
::v-deep pre.md-code-block {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 10px;
  margin: 10px 0;
  font-family: monospace;
  overflow-x: auto;
  white-space: pre;
  font-size: 13px;
}

::v-deep .md-code-inline,
::v-deep code.md-code-inline {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 2px 4px;
  font-family: monospace;
  font-size: 13px;
}

/* 添加列表和段落样式 */
::v-deep .md-paragraph {
  margin: 5px 0;
  line-height: 1.6;
}

::v-deep .md-paragraph-space {
  height: 6px; /* 仅在需要的情况下调整为更小的值 */
}

::v-deep .md-ul,
::v-deep .md-ol {
  margin: 10px 0;
  padding-left: 25px;
}

::v-deep .md-ul {
  list-style-type: disc;
}

::v-deep .md-ul li {
  margin: 5px 0;
  color: #333333;
}

::v-deep .md-ul li::marker {
  color: #333333;
}

::v-deep .md-ol {
  list-style-type: decimal;
}

::v-deep .md-ol li {
  margin: 5px 0;
  color: #333333;
}

::v-deep .md-ol li::marker {
  color: #333333;
  font-weight: bold;
}

/* 添加滚动条样式： */
/* 自定义滚动条样式 */
::v-deep .markdown-content-text::-webkit-scrollbar {
  width: 6px;
}

::v-deep .markdown-content-text::-webkit-scrollbar-track {
  background: transparent; /* 去掉滚动条背景色 */
}

::v-deep .markdown-content-text::-webkit-scrollbar-thumb {
  background-color: #ffffff; /* 中等灰色 */
  border-radius: 3px;
}

::v-deep .markdown-content-text::-webkit-scrollbar-thumb:hover {
  background-color: #f7f7f7; /* 悬停时的颜色 */
}

/* 点击看大图按钮样式 */
.view-markmap {
  display: inline-block;
  margin-left: 10px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.view-markmap:hover {
  background-color: rgba(24, 144, 255, 0.2);
  text-decoration: underline;
}

.markdown-content-title {
  display: flex;
  align-items: center;
}

::v-deep pre {
  white-space: normal;
}
</style>
