{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=style&index=0&id=794a9327&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753944647587}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudHJlZUJveCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAxNzZweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi50cmVlLXBhZ2luYXRpb24gew0KICBwYWRkaW5nOiAxMHB4Ow0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KDQogIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIHsNCiAgICAuZWwtcGFnaW5hdGlvbl9fc2l6ZXMgew0KICAgICAgbWFyZ2luLXRvcDogLTJweDsNCiAgICB9DQogIH0NCn0NCg0KLnRyZWVNYWluIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoudHJlZVF1ZXJ5IHsNCiAgOjp2LWRlZXAgLmVsLWlucHV0LS1taW5pIC5lbC1pbnB1dF9faW5uZXIgew0KICAgIGhlaWdodDogMjRweDsNCiAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICBwYWRkaW5nOiAwIDRweDsNCiAgfQ0KDQogIDo6di1kZWVwIC5lbC1pbnB1dF9fc3VmZml4IHsNCiAgICAvLyBoZWlnaHQ6IDIwcHg7DQogICAgcmlnaHQ6IC0ycHg7DQogICAgLy8gdG9wOiA1cHg7DQogIH0NCn0NCg0KLnRvb2xCb3ggew0KICBtaW4taGVpZ2h0OiAxMzBweDsNCiAgaGVpZ2h0OiBhdXRvOw0KICBwYWRkaW5nLWJvdHRvbTogMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSwgMjU1LCAyNTUpOw0KICAvLyBib3gtc2hhZG93OiAtMXB4IDJweCAxNXB4ICNjZWNkY2Q7DQogIGJvcmRlci1sZWZ0OiBzb2xpZCAxcHggcmdiKDIyMSwgMjE5LCAyMTkpOw0KDQogIC50aXRsZSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgaGVpZ2h0OiA3MHB4Ow0KICAgIHBhZGRpbmc6IDAgMzBweDsNCiAgICBmb250LXNpemU6IDE5cHg7DQogIH0NCg0KICAubWFpblRvb2wgew0KICAgIHBhZGRpbmc6IDAgMjhweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgY29sb3I6IHJnYig1OCwgNTgsIDU4KTsNCiAgfQ0KDQogIC5tYWluVG9vbE9uZSB7DQogICAgbWFyZ2luLXRvcDogMTVweDsNCiAgICBoZWlnaHQ6IGF1dG87DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgICAvLyBhbGlnbi1pdGVtczogY2VudGVyOw0KICB9DQoNCiAgLm1haW5Ub29sVHdvIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgaGVpZ2h0OiA0MHB4Ow0KDQogICAgcCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGdhcDogMTBweDsNCiAgICB9DQogIH0NCg0KICAuYnRuIHsNCiAgICBtYXJnaW46IDE1cHggMCAwIDI1cHg7DQogIH0NCn0NCg0KLmtleXdvcmQgew0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KDQogIC5oaXN0b3J5IHsNCiAgICB3aWR0aDogNDMwcHg7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgei1pbmRleDogOTk5OTsNCiAgICBsZWZ0OiA2NXB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYigyMjEsIDIxOSwgMjE5KTsNCg0KICAgIC5oaXN0b3J5SXRlbSB7DQogICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQoNCiAgICAgIC5oaXN0b3J5VGV4dCB7DQogICAgICAgIHdpZHRoOiA0NTBweDsNCiAgICAgICAgaGVpZ2h0OiAzNHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMzRweDsNCiAgICAgIH0NCg0KICAgICAgJjpudGgtbGFzdC1vZi10eXBlKDEpIHsNCiAgICAgICAgcGFkZGluZy1sZWZ0OiAwOw0KDQogICAgICAgIDo6di1kZWVwIC5lbC1idXR0b24tLXRleHQgew0KICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQoua2V5d29yZC10aXAgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjOTk5Ow0KICBtYXJnaW4tbGVmdDogNjVweDsNCiAgbGluZS1oZWlnaHQ6IDE7DQp9DQoNCi5oaXN0b3J5IHsNCiAgd2lkdGg6IDUzMHB4Ow0KDQogIC5oaXN0b3J5SXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgcGFkZGluZzogMCAxMHB4Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgICAuaGlzdG9yeVRleHQgew0KICAgICAgd2lkdGg6IDM1MHB4Ow0KICAgICAgaGVpZ2h0OiAzNHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDM0cHg7DQogICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["keMonitor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkwCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "keMonitor.vue", "sourceRoot": "src/views/KeMonitor", "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}