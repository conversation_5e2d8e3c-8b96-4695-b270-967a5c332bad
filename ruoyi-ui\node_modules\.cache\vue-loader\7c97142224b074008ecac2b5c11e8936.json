{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=template&id=692f5a28&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1753944503958}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}