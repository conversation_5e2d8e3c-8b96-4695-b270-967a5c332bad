{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753944647587}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJGOi9wcm9qZWN0L3N6cy1kcHgvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJGOi9wcm9qZWN0L3N6cy1kcHgvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMi5qcyIpKTsKdmFyIF90b0NvbnN1bWFibGVBcnJheTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkY6L3Byb2plY3Qvc3pzLWRweC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90b0NvbnN1bWFibGVBcnJheS5qcyIpKTsKdmFyIF9yZWdlbmVyYXRvclJ1bnRpbWUyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJGOi9wcm9qZWN0L3N6cy1kcHgvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3JSdW50aW1lLmpzIikpOwp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJGOi9wcm9qZWN0L3N6cy1kcHgvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvci5qcyIpKTsKdmFyIF9pbmRleCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9hcGkvU2NpZW5jZUFwaS9pbmRleC5qcyIpKTsKdmFyIF90b3BTZWFjaCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9jb21wb25lbnRzL3RvcFNlYWNoLnZ1ZSIpKTsKdmFyIF9NYWluQXJ0aWNsZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29tcG9uZW50cy9NYWluQXJ0aWNsZS52dWUiKSk7CnZhciBfYXJ0aWNsZUhpc3RvcnkgPSByZXF1aXJlKCJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5Iik7CnZhciBfc3BsaXRwYW5lcyA9IHJlcXVpcmUoInNwbGl0cGFuZXMiKTsKcmVxdWlyZSgic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIik7CnZhciBfaW5kZXgyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2NvbXBvbmVudHMvVHJlZVRhYmxlL2luZGV4LnZ1ZSIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGNvbXBvbmVudHM6IHsKICAgIHRvcFNlYWNoOiBfdG9wU2VhY2guZGVmYXVsdCwKICAgIE1haW5BcnRpY2xlOiBfTWFpbkFydGljbGUuZGVmYXVsdCwKICAgIFNwbGl0cGFuZXM6IF9zcGxpdHBhbmVzLlNwbGl0cGFuZXMsCiAgICBQYW5lOiBfc3BsaXRwYW5lcy5QYW5lLAogICAgVHJlZVRhYmxlOiBfaW5kZXgyLmRlZmF1bHQKICB9LAogIGRpY3RzOiBbImlzX3RlY2hub2xvZ3kiXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgd2lkdGg6ICIyNTgiLAogICAgICBpc1JlU2l6ZTogZmFsc2UsCiAgICAgIC8qIOaWh+eroOS4u+S9k+e7hOS7tuaVsOaNriAqLwogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgcGFnZVNpemU6IDUwLAogICAgICB0b3RhbDogMCwKICAgICAgQXJ0aWNsZUxpc3Q6IFtdLAogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwKICAgICAgY2hlY2tMaXN0OiBbXSwKICAgICAgLyog5qCR5b2i5YiG6aG15pWw5o2uICovCiAgICAgIHRyZWVDdXJyZW50UGFnZTogMSwKICAgICAgdHJlZVBhZ2VTaXplOiAxMDAsCiAgICAgIHRyZWVUb3RhbDogMCwKICAgICAgLyog5pCc57Si57uE5Lu25pWw5o2uICovCiAgICAgIFNlYWNoRGF0YTogewogICAgICAgIG1ldGFNb2RlOiAiIiAvKiDljLnphY3mqKHlvI8gKi8sCiAgICAgICAga2V5d29yZDogIiIgLyog5YWz6ZSu6K+NICovLAogICAgICAgIHRpbWVSYW5nZTogNCAvKiDml7bpl7TojIPlm7QgKi8sCiAgICAgICAgY3VzdG9tRGF5OiBbXSAvKiDoh6rlrprkuYnlpKkgKi8sCiAgICAgICAgY29sbGVjdGlvbkRhdGVUeXBlOiBudWxsIC8qIOaXtumXtOiMg+WbtCAqLywKICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLAogICAgICAgIGlzVGVjaG5vbG9neTogIjEiLAogICAgICAgIHNvcnRNb2RlOiAiNCIsCiAgICAgICAgZW1vdGlvbjogIjAiLAogICAgICAgIGhhc0NhY2hlOiAiMCIKICAgICAgfSAvKiDmkJzntKLmnaHku7YgKi8sCgogICAgICAvKiDmjpLluo/mqKHlvI8gLSDljZXni6zmj5Dlj5bvvIzpgb/lhY3op6blj5FTZWFjaERhdGHnmoTmt7Hluqbnm5HlkKwgKi8KICAgICAgYnV0dG9uRGlzYWJsZWQ6IGZhbHNlIC8qIOaMiemSrumYsuaKliAqLywKICAgICAgQWN0aXZlRGF0YToge30sCiAgICAgIHNlbmlvclNlcmNoRmxhZzogZmFsc2UgLyog5pmu6YCa5qOA57Si5oiW6auY57qn5qOA57SiICovLAogICAgICBhcmVhTGlzdDogW10gLyog5Zu95YaF5Zyw5Yy6ICovLAogICAgICBjb3VudHJ5TGlzdDogW10gLyog5Zu95a625oiW5Zyw5Yy6ICovLAogICAgICBLZUxpc3Q6IFtdLAogICAgICBmdW5Fc1NlYWNoOiBudWxsLAogICAgICB0cmVlUXVlcnk6IHsKICAgICAgICBmaWx0ZXJ3b3JkczogIiIgLy8g5re75Yqg5qCR5pCc57Si5YWz6ZSu5a2XCiAgICAgIH0sCiAgICAgIGRvbWFpbkxpc3Q6IFtdLAogICAgICBpbmR1c3RyeUxpc3Q6IFtdLAogICAgICBzaG93SGlzdG9yeTogZmFsc2UsCiAgICAgIGhpc3RvcnlMaXN0OiBbXSwKICAgICAgaGlzdG9yeVRpbWVvdXQ6IG51bGwsCiAgICAgIGRpYWxvZ1Zpc2libGUxOiBmYWxzZSwKICAgICAgaGlzdG9yeUxvYWRpbmc6IGZhbHNlLAogICAgICBxdWVyeVBhcmFtczE6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICB0b3RhbDE6IDAsCiAgICAgIGhpc3RvcnlMaXN0MTogW10sCiAgICAgIGluaXRpYWxpemF0aW9uQ29tcGxldGVkOiBmYWxzZSwKICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5piv5ZCm5a6M5oiQCiAgICAgIC8vIOS7jldlY2hhdC52dWXlkIzmraXnmoTlsZ7mgKcKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOagkee7hOS7tmxvYWRpbmfnirbmgIEKICAgICAgdGFibGVMb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6KGo5qC8bG9hZGluZ+eKtuaAgQogICAgICBpc1F1ZXJ5aW5nOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i6Ziy5oqWCiAgICAgIHF1ZXJ5RGVib3VuY2VUaW1lcjogbnVsbCwKICAgICAgLy8g5p+l6K+i6Ziy5oqW5a6a5pe25ZmoCiAgICAgIGlzUmlnaHRGaWx0ZXI6IGZhbHNlLAogICAgICAvLyDmoIforrDlj7PkvqfnrZvpgInmnaHku7bmmK/lkKblj5HnlJ/lj5jljJYKICAgICAgaXNMZWZ0UmVzZXQ6IGZhbHNlLAogICAgICAvLyDmoIforrDlt6bkvqfmoJHmmK/lkKbph43nva4KICAgICAgc2VsZWN0ZWRDbGFzc2lmeTogIjUiLAogICAgICAvLyDpgInkuK3nmoTmlbDmja7mupDliIbnsbsKICAgICAgc2VsZWN0ZWRDb3VudHJ5OiBudWxsLAogICAgICAvLyDpgInkuK3nmoTlm73lrrYKICAgICAgLyog5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yI5rC45LmF5L+d5a2Y77yM5Y+q5pyJ54m55a6a5pON5L2c5omN5pu05paw77yJICovCiAgICAgIHNhdmVkQ2hlY2tib3hEYXRhOiBbXSwKICAgICAgZ2xvYmFsTG9hZGluZzogZmFsc2UKICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDA7CiAgICAgICAgICAgIC8vIOWIneWni+WMlmZ1bkVzU2VhY2jmlrnms5UKICAgICAgICAgICAgX3RoaXMuZnVuRXNTZWFjaCA9IF90aGlzLkVzU2VhY2g7CgogICAgICAgICAgICAvLyDlhYjliqDovb3ln7rnoYDmlbDmja4KICAgICAgICAgICAgX3RoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsKCiAgICAgICAgICAgIC8vIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5tZW51VHlwZSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5tZW51VHlwZSA9PT0gIjgiKSB7CiAgICAgICAgICAgIC8vICAgdGhpcy5TZWFjaERhdGEudGltZVJhbmdlID0gNzsKICAgICAgICAgICAgLy8gfQoKICAgICAgICAgICAgLy8g5Yqg6L295qCR5pWw5o2u5ZKM5YaF5a655pWw5o2uCiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA1OwogICAgICAgICAgICByZXR1cm4gX3RoaXMuaW5pdGlhbGl6ZURhdGEoKTsKICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5a6M5oiQ77yM6L+Z5qC3d2F0Y2jnm5HlkKzlmajmiY3kvJrlvIDlp4vlt6XkvZwKICAgICAgICAgICAgX3RoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgPSB0cnVlOwogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gODsKICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgY29uc29sZS5lcnJvcigi57uE5Lu25Yid5aeL5YyW5aSx6LSlOiIsIF9jb250ZXh0LnQwKTsKICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIneWni+WMluWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlSIpOwogICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDhdXSk7CiAgICB9KSkoKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICAvLyBUcmVlVGFibGUg57uE5Lu25LiN6ZyA6KaB54m55q6K55qE54q25oCB5qOA5p+lCiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs562b6YCJ5p2h5Lu25Y+Y5YyWCiAgICAiU2VhY2hEYXRhLnRpbWVSYW5nZSI6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOwogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7CiAgICAgIH0KICAgIH0sCiAgICAiU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSI6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOwogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7CiAgICAgIH0KICAgIH0sCiAgICAiU2VhY2hEYXRhLmlzVGVjaG5vbG9neSI6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOwogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7CiAgICAgIH0KICAgIH0sCiAgICAiU2VhY2hEYXRhLmVtb3Rpb24iOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsKICAgICAgICB0aGlzLmhhbmRsZUZpbHRlckNoYW5nZSgpOwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJbmlbDmja4KICAgIGluaXRpYWxpemVEYXRhOiBmdW5jdGlvbiBpbml0aWFsaXplRGF0YSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMDsKICAgICAgICAgICAgICAvLyDliqDovb3mlofnq6DliJfooajvvIjlhoXpg6jlt7Lnu4/lpITnkIbkuoYgdGFibGVMb2FkaW5n77yJCiAgICAgICAgICAgICAgX3RoaXMyLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsKICAgICAgICAgICAgICAvLyDliqDovb3moJHmlbDmja4KICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5xdWVyeVRyZWVEYXRhKCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDY7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMi4kbmV4dFRpY2soKTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDg7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnQwID0gX2NvbnRleHQyWyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluaVsOaNruWksei0pToiLCBfY29udGV4dDIudDApOwogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7CiAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMCwgOF1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8gVHJlZVRhYmxlIOe7hOS7tuS6i+S7tuWkhOeQhuaWueazlQogICAgLy8g5aSE55CG6YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3RlZERhdGEsIG9wZXJhdGlvblR5cGUpIHsKICAgICAgaWYgKG9wZXJhdGlvblR5cGUgPT09ICJyb3ctY2xpY2siIHx8IG9wZXJhdGlvblR5cGUgPT09ICJjbGVhci1hbGwiKSB7CiAgICAgICAgLy8g54K55Ye76KGM77yI5Y2V6YCJ77yJ5oiW5Y+W5raI5omA5pyJ6YCJ5Lit77ya55u05o6l5pu/5o2i77yM5LiN6ZyA6KaB6L+95Yqg5Y676YeNCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShzZWxlY3RlZERhdGEpOwogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShzZWxlY3RlZERhdGEpOwogICAgICB9IGVsc2UgaWYgKG9wZXJhdGlvblR5cGUgPT09ICJjaGVja2JveC1jaGFuZ2UiIHx8IG9wZXJhdGlvblR5cGUgPT09ICJzZWxlY3QtYWxsIikgewogICAgICAgIC8vIOeCueWHu+WLvumAieahhu+8iOWkmumAie+8ieaIluWFqOmAie+8mumcgOimgeato+ehruWkhOeQhumAieS4reWSjOWPlua2iOmAieS4rQogICAgICAgIC8vIOWFiOS7juS/neWtmOeahOaVsOaNruS4reenu+mZpOW9k+WJjemhtemdoueahOaJgOacieaVsOaNrgogICAgICAgIHZhciBjdXJyZW50UGFnZUlkcyA9IHRoaXMudHJlZURhdGFUcmFuc2Zlci5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLnNvdXJjZVNuOwogICAgICAgIH0pOwogICAgICAgIHZhciBmaWx0ZXJlZENoZWNrTGlzdCA9IHRoaXMuY2hlY2tMaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuICFjdXJyZW50UGFnZUlkcy5pbmNsdWRlcyhpdGVtLnNvdXJjZVNuKTsKICAgICAgICB9KTsKICAgICAgICB2YXIgZmlsdGVyZWRTYXZlZERhdGEgPSB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuICFjdXJyZW50UGFnZUlkcy5pbmNsdWRlcyhpdGVtLnNvdXJjZVNuKTsKICAgICAgICB9KTsKCiAgICAgICAgLy8g54S25ZCO5re75Yqg5b2T5YmN6aG16Z2i5paw6YCJ5Lit55qE5pWw5o2uCiAgICAgICAgdmFyIGNvbWJpbmVkQ2hlY2tMaXN0ID0gW10uY29uY2F0KCgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKGZpbHRlcmVkQ2hlY2tMaXN0KSwgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoc2VsZWN0ZWREYXRhKSk7CiAgICAgICAgdmFyIGNvbWJpbmVkU2F2ZWREYXRhID0gW10uY29uY2F0KCgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKGZpbHRlcmVkU2F2ZWREYXRhKSwgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoc2VsZWN0ZWREYXRhKSk7CgogICAgICAgIC8vIOWvueWQiOW5tuWQjueahOaVsOaNrui/m+ihjOWOu+mHjeWkhOeQhgogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gdGhpcy5kZWR1cGxpY2F0ZUJ5U291cmNlU24oY29tYmluZWRDaGVja0xpc3QpOwogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSB0aGlzLmRlZHVwbGljYXRlQnlTb3VyY2VTbihjb21iaW5lZFNhdmVkRGF0YSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g6buY6K6k5oOF5Ya177ya55u05o6l5pu/5o2i77yI5YW85a655oCn5aSE55CG77yJCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShzZWxlY3RlZERhdGEpOwogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShzZWxlY3RlZERhdGEpOwogICAgICB9CgogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LlhoXlrrkKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOwogICAgICBpZiAoIXRoaXMuaXNSaWdodEZpbHRlcikgewogICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgic291cmNlSXRlbUNoYW5nZWQiKTsKICAgICAgfQogICAgfSwKICAgIC8vIOagueaNrnNvdXJjZVNu5Y676YeN55qE6L6F5Yqp5pa55rOVCiAgICBkZWR1cGxpY2F0ZUJ5U291cmNlU246IGZ1bmN0aW9uIGRlZHVwbGljYXRlQnlTb3VyY2VTbihkYXRhQXJyYXkpIHsKICAgICAgdmFyIHNlZW4gPSBuZXcgU2V0KCk7CiAgICAgIHJldHVybiBkYXRhQXJyYXkuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKHNlZW4uaGFzKGl0ZW0uc291cmNlU24pKSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICAgIHNlZW4uYWRkKGl0ZW0uc291cmNlU24pOwogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlpITnkIbph43nva4KICAgIGhhbmRsZVJlc2V0OiBmdW5jdGlvbiBoYW5kbGVSZXNldCgpIHsKICAgICAgLy8g5YWI5riF56m66L+H5ruk5YWz6ZSu5a2X77yM6YG/5YWN6Kem5Y+RIGhhbmRsZUZpbHRlclNlYXJjaAogICAgICB0aGlzLnRyZWVRdWVyeS5maWx0ZXJ3b3JkcyA9ICIiOwogICAgICB0aGlzLnNlbGVjdGVkQ2xhc3NpZnkgPSBudWxsOwogICAgICB0aGlzLnNlbGVjdGVkQ291bnRyeSA9IG51bGw7CgogICAgICAvLyDnhLblkI7orr7nva7ph43nva7moIforrAKICAgICAgdGhpcy5pc0xlZnRSZXNldCA9IHRydWU7CgogICAgICAvLyDmuIXnqbrpgInkuK3nirbmgIEKICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsKCiAgICAgIC8vIOa4heepuuS/neWtmOeahOWLvumAieaVsOaNru+8iOawuOS5heS/neWtmO+8iQogICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gW107CgogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LliJfooajmlbDmja4KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7CiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOwoKICAgICAgLy8g6YeN5paw5p+l6K+i5qCR5ZKM5YiX6KGoCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOwogICAgfSwKICAgIC8vIOWkhOeQhuetm+mAieadoeS7tuWPmOWMliAtIOadpeiHquWPs+S+p+etm+mAieadoeS7tueahOWPmOWMlgogICAgaGFuZGxlRmlsdGVyQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVGaWx0ZXJDaGFuZ2UoKSB7CiAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IHRydWU7IC8vIOagh+iusOWPs+S+p+etm+mAieadoeS7tuWPkeeUn+WPmOWMlgoKICAgICAgLy8g5LiN5YaN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uCiAgICAgIC8vIOawuOS5heS/neWtmOeahOWLvumAieaVsOaNruS8muWcqOafpeivouWQjuiHquWKqOaBouWkjQoKICAgICAgLy8g6YeN572u5YiG6aG15Yiw56ys5LiA6aG1CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOwogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuU2VhY2hEYXRhLmhhc0NhY2hlID0gIjAiOwoKICAgICAgLy8g5rua5Yqo5Yiw6aG26YOoCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOwoKICAgICAgLy8g5ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGoCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOwogICAgfSwKICAgIC8vIOWQjOaXtuafpeivouagkeWSjOWIl+ihqCAtIOebtOaOpeS7jldlY2hhdC52dWXlpI3liLYKICAgIHF1ZXJ5VHJlZUFuZExpc3Q6IGZ1bmN0aW9uIHF1ZXJ5VHJlZUFuZExpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgc2F2ZWREYXRhOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gMDsKICAgICAgICAgICAgICAvLyDkv53lrZjlvZPliY3nmoTmsLjkuYXli77pgInmlbDmja7vvIzpgb/lhY3lnKjmn6Xor6Lov4fnqIvkuK3kuKLlpLEKICAgICAgICAgICAgICBzYXZlZERhdGEgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShfdGhpczMuc2F2ZWRDaGVja2JveERhdGEpOyAvLyDlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzlhYjkuLTml7bmgaLlpI0gY2hlY2tMaXN0IOS7peS+v+afpeivouaXtuW4puS4iuWPguaVsAogICAgICAgICAgICAgIGlmIChzYXZlZERhdGEgJiYgc2F2ZWREYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgIF90aGlzMy5jaGVja0xpc3QgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShzYXZlZERhdGEpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmsqHmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzmuIXnqbrpgInkuK3nirbmgIEKICAgICAgICAgICAgICAgIF90aGlzMy5jaGVja0xpc3QgPSBbXTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOWQjOaXtuafpeivouagkeaVsOaNruWSjOWPs+S+p+WIl+ihqO+8iOS/neaMgeaAp+iDveS8mOWKv++8iQogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gNTsKICAgICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5hbGwoW190aGlzMy5xdWVyeVRyZWVEYXRhKCksIF90aGlzMy5xdWVyeUFydGljbGVMaXN0KCkgLy8gcXVlcnlBcnRpY2xlTGlzdCDlhoXpg6jlt7Lnu4/lpITnkIbkuoYgdGFibGVMb2FkaW5nCiAgICAgICAgICAgICAgXSk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICAvLyDnoa7kv53msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7kuI3kvJrkuKLlpLEKICAgICAgICAgICAgICBfdGhpczMuc2F2ZWRDaGVja2JveERhdGEgPSBzYXZlZERhdGE7CgogICAgICAgICAgICAgIC8vIOafpeivouWujOaIkOWQju+8jOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOmdmem7mOaBouWkjeeVjOmdoumAieS4reeKtuaAgQogICAgICAgICAgICAgIGlmIChfdGhpczMuc2F2ZWRDaGVja2JveERhdGEgJiYgX3RoaXMzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgIF90aGlzMy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAvLyDmn6Xor6LlrozmiJDlkI7ph43nva7lj7PkvqfnrZvpgInmoIforrAKICAgICAgICAgICAgICBfdGhpczMuaXNSaWdodEZpbHRlciA9IGZhbHNlOwogICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgX3RoaXMzLmlzTGVmdFJlc2V0ID0gZmFsc2U7CiAgICAgICAgICAgICAgfSwgMzAwKTsKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDE3OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gMTE7CiAgICAgICAgICAgICAgX2NvbnRleHQzLnQwID0gX2NvbnRleHQzWyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWQjOaXtuafpeivouagkeWSjOWIl+ihqOWksei0pToiLCBfY29udGV4dDMudDApOwogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcigi5p+l6K+i5aSx6LSl77yM6K+36YeN6K+VIik7CiAgICAgICAgICAgICAgLy8g5Y2z5L2/5Ye66ZSZ5Lmf6KaB6YeN572u5qCH6K6wCiAgICAgICAgICAgICAgX3RoaXMzLmlzUmlnaHRGaWx0ZXIgPSBmYWxzZTsKICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzMy5pc0xlZnRSZXNldCA9IGZhbHNlOwogICAgICAgICAgICAgIH0sIDMwMCk7CiAgICAgICAgICAgIGNhc2UgMTc6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMsIG51bGwsIFtbMCwgMTFdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOaBouWkjemAieS4reaVsOaNrua6kOeahOaWueazleW3suWIoOmZpO+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrgogICAgLy8g5LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB77yI5LuF5aSE55CG55WM6Z2i6YCJ5Lit54q25oCB77yJCiAgICByZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhOiBmdW5jdGlvbiByZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgaWYgKCF0aGlzLnNhdmVkQ2hlY2tib3hEYXRhIHx8IHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDlnKjlvZPliY3moJHmlbDmja7kuK3mn6Xmib7ljLnphY3nmoTpobkKICAgICAgdmFyIG1hdGNoZWRJdGVtcyA9IFtdOwogICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmZvckVhY2goZnVuY3Rpb24gKHNhdmVkSXRlbSkgewogICAgICAgIHZhciBmb3VuZEl0ZW0gPSBfdGhpczQudHJlZURhdGFUcmFuc2Zlci5maW5kKGZ1bmN0aW9uICh0cmVlSXRlbSkgewogICAgICAgICAgcmV0dXJuIHRyZWVJdGVtLnNvdXJjZVNuID09PSBzYXZlZEl0ZW0uc291cmNlU247CiAgICAgICAgfSk7CiAgICAgICAgaWYgKGZvdW5kSXRlbSkgewogICAgICAgICAgbWF0Y2hlZEl0ZW1zLnB1c2goZm91bmRJdGVtKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICBpZiAobWF0Y2hlZEl0ZW1zLmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDmm7TmlrDpgInkuK3liJfooajvvIjmraTml7YgY2hlY2tMaXN0IOW3sue7j+WcqOafpeivouWJjeaBouWkjei/h+S6hu+8iQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gbWF0Y2hlZEl0ZW1zOwogICAgICAgIC8vIOmAmuefpSBUcmVlVGFibGUg57uE5Lu25oGi5aSN55WM6Z2i6YCJ5Lit54q25oCB77yI5LiN6Kem5Y+R5LqL5Lu277yJCiAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgaWYgKF90aGlzNC4kcmVmcy50cmVlVGFibGUpIHsKICAgICAgICAgICAgX3RoaXM0LiRyZWZzLnRyZWVUYWJsZS5yZXN0b3JlU2VsZWN0aW9uU2lsZW50bHkobWF0Y2hlZEl0ZW1zKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpzmsqHmnInljLnphY3pobnvvIzmuIXnqbrpgInkuK3nirbmgIEKICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOwogICAgICB9CiAgICB9LAogICAgLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB55qE5pa55rOV5bey5Yig6Zmk77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uCiAgICAvLyDmn6Xor6LmoJHmlbDmja7lubbku47msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7mgaLlpI3pgInkuK3nirbmgIHvvIjnlKjkuo7lhbPplK7lrZfov4fmu6TvvIkKICAgIHF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZDogZnVuY3Rpb24gcXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSAwOwogICAgICAgICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOWFiOS4tOaXtuaBouWkjSBjaGVja0xpc3Qg5Lul5L6/5p+l6K+i5pe25bim5LiK5Y+C5pWwCiAgICAgICAgICAgICAgaWYgKF90aGlzNS5zYXZlZENoZWNrYm94RGF0YSAmJiBfdGhpczUuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgX3RoaXM1LmNoZWNrTGlzdCA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKF90aGlzNS5zYXZlZENoZWNrYm94RGF0YSk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzNS5jaGVja0xpc3QgPSBbXTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOafpeivouagkeaVsOaNrgogICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM1LnF1ZXJ5VHJlZURhdGEoKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIC8vIOafpeivouWujOaIkOWQju+8jOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOmdmem7mOaBouWkjeeVjOmdoumAieS4reeKtuaAgQogICAgICAgICAgICAgIGlmIChfdGhpczUuc2F2ZWRDaGVja2JveERhdGEgJiYgX3RoaXM1LnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgIF90aGlzNS5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfY29udGV4dDQucHJldiA9IDc7CiAgICAgICAgICAgICAgX2NvbnRleHQ0LnQwID0gX2NvbnRleHQ0WyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuafpeivouagkeaVsOaNruW5tuS7juawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaBouWkjemAieS4reeKtuaAgeWksei0pToiLCBfY29udGV4dDQudDApOwogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0LCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOWIhumhteWkhOeQhgogICAgaGFuZGxlQ3VycmVudENoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ3VycmVudENoYW5nZShjdXJyZW50KSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSBjdXJyZW50OwogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsKICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7CiAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOwogICAgfSwKICAgIC8vIOafpeivouagkeaVsOaNrgogICAgcXVlcnlUcmVlRGF0YTogZnVuY3Rpb24gcXVlcnlUcmVlRGF0YSgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU1KCkgewogICAgICAgIHZhciBwYXJhbXMsIHJlcywgZGF0YUxpc3QsIHRvdGFsLCBtYXBEYXRhOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU1JChfY29udGV4dDUpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NS5wcmV2ID0gX2NvbnRleHQ1Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzNi5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDE7CiAgICAgICAgICAgICAgcGFyYW1zID0gewogICAgICAgICAgICAgICAgcGFnZU51bTogX3RoaXM2LnRyZWVDdXJyZW50UGFnZSwKICAgICAgICAgICAgICAgIHBhZ2VTaXplOiBfdGhpczYudHJlZVBhZ2VTaXplLAogICAgICAgICAgICAgICAgcGxhdGZvcm1UeXBlOiAxLAogICAgICAgICAgICAgICAgaWQ6IF90aGlzNi4kcm91dGUucXVlcnkubWVudVR5cGUgPyAiMSIgOiBfdGhpczYuJHJvdXRlLnF1ZXJ5LmlkLAogICAgICAgICAgICAgICAgbTogMSwKICAgICAgICAgICAgICAgIGRhdGVUeXBlOiBfdGhpczYuU2VhY2hEYXRhLnRpbWVSYW5nZSAhPSA2ID8gX3RoaXM2LlNlYWNoRGF0YS50aW1lUmFuZ2UgOiAiIiwKICAgICAgICAgICAgICAgIHN0YXJ0VGltZTogX3RoaXM2LlNlYWNoRGF0YS5jdXN0b21EYXkgPyBfdGhpczYuU2VhY2hEYXRhLmN1c3RvbURheVswXSA6ICIiLAogICAgICAgICAgICAgICAgZW5kVGltZTogX3RoaXM2LlNlYWNoRGF0YS5jdXN0b21EYXkgPyBfdGhpczYuU2VhY2hEYXRhLmN1c3RvbURheVsxXSA6ICIiLAogICAgICAgICAgICAgICAgY29sbGVjdGlvbkRhdGVUeXBlOiBfdGhpczYuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSAhPSA2ID8gX3RoaXM2LlNlYWNoRGF0YS5jb2xsZWN0aW9uRGF0ZVR5cGUgOiAiIiwKICAgICAgICAgICAgICAgIGNvbGxlY3Rpb25TdGFydFRpbWU6IF90aGlzNi5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUgPyBfdGhpczYuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lWzBdIDogIiIsCiAgICAgICAgICAgICAgICBjb2xsZWN0aW9uRW5kVGltZTogX3RoaXM2LlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZSA/IF90aGlzNi5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMV0gOiAiIiwKICAgICAgICAgICAgICAgIGtleXdvcmRzOiBfdGhpczYuU2VhY2hEYXRhLmtleXdvcmQsCiAgICAgICAgICAgICAgICBpc1RlY2hub2xvZ3k6IF90aGlzNi5TZWFjaERhdGEuaXNUZWNobm9sb2d5LAogICAgICAgICAgICAgICAgZW1vdGlvbjogX3RoaXM2LlNlYWNoRGF0YS5lbW90aW9uLAogICAgICAgICAgICAgICAgLy8g5re75Yqg5YWz6ZSu5a2X6L+H5ruk5Y+C5pWwCiAgICAgICAgICAgICAgICBmaWx0ZXJ3b3JkczogX3RoaXM2LnRyZWVRdWVyeS5maWx0ZXJ3b3JkcyB8fCAiIiwKICAgICAgICAgICAgICAgIC8vIOa3u+WKoOaVsOaNrua6kOWIhuexu+WPguaVsAogICAgICAgICAgICAgICAgdGhpbmtUYW5rQ2xhc3NpZmljYXRpb246IF90aGlzNi5zZWxlY3RlZENsYXNzaWZ5LAogICAgICAgICAgICAgICAgLy8g5re75Yqg5Zu95a62562b6YCJ5Y+C5pWwCiAgICAgICAgICAgICAgICBjb3VudHJ5T2ZPcmlnaW46IF90aGlzNi5zZWxlY3RlZENvdW50cnksCiAgICAgICAgICAgICAgICBoYXNDYWNoZTogX3RoaXM2LlNlYWNoRGF0YS5oYXNDYWNoZQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgaWYgKF90aGlzNi4kcm91dGUucXVlcnkubWVudVR5cGUpIHsKICAgICAgICAgICAgICAgIHBhcmFtcy5tZW51VHlwZSA9IF90aGlzNi4kcm91dGUucXVlcnkubWVudVR5cGU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gNjsKICAgICAgICAgICAgICByZXR1cm4gX2luZGV4LmRlZmF1bHQubW9uaXRvcmluZ01lZGl1bShwYXJhbXMpOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ1LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICAgIGRhdGFMaXN0ID0gcmVzLnJvd3MgfHwgW107CiAgICAgICAgICAgICAgICB0b3RhbCA9IHJlcy50b3RhbCB8fCAwOwogICAgICAgICAgICAgICAgbWFwRGF0YSA9IGZ1bmN0aW9uIG1hcERhdGEoZGF0YSkgewogICAgICAgICAgICAgICAgICByZXR1cm4gZGF0YS5tYXAoZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAgIGlkOiAiIi5jb25jYXQoaXRlbS5zb3VyY2VTbiB8fCAidW5rbm93biIsICJfIikuY29uY2F0KGluZGV4LCAiXyIpLmNvbmNhdChEYXRlLm5vdygpLCAiXyIpLmNvbmNhdChNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTEpKSwKICAgICAgICAgICAgICAgICAgICAgIC8vIOehruS/nee7neWvueWUr+S4gOaApwogICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uY25OYW1lLAogICAgICAgICAgICAgICAgICAgICAgY291bnQ6IGl0ZW0uYXJ0aWNsZUNvdW50IHx8IDAsCiAgICAgICAgICAgICAgICAgICAgICBvcmRlck51bTogaXRlbS5vcmRlck51bSwKICAgICAgICAgICAgICAgICAgICAgIGNvdW50cnk6IGl0ZW0uY291bnRyeU9mT3JpZ2luIHx8IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICBzb3VyY2VTbjogaXRlbS5zb3VyY2VTbiwKICAgICAgICAgICAgICAgICAgICAgIHVybDogaXRlbS51cmwgfHwgbnVsbAogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIF90aGlzNi50cmVlRGF0YVRyYW5zZmVyID0gbWFwRGF0YShkYXRhTGlzdCk7CiAgICAgICAgICAgICAgICBfdGhpczYudHJlZVRvdGFsID0gdG90YWw7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMTQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnByZXYgPSAxMDsKICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oMSk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5p+l6K+i5qCR5pWw5o2u5aSx6LSlOiIsIF9jb250ZXh0NS50MCk7CiAgICAgICAgICAgICAgX3RoaXM2LiRtZXNzYWdlLmVycm9yKCLojrflj5bmlbDmja7mupDlpLHotKUiKTsKICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDE0OwogICAgICAgICAgICAgIF90aGlzNi5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5maW5pc2goMTQpOwogICAgICAgICAgICBjYXNlIDE3OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU1LCBudWxsLCBbWzEsIDEwLCAxNCwgMTddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOafpeivouaWh+eroOWIl+ihqO+8iOW4pumYsuaKlu+8iQogICAgcXVlcnlBcnRpY2xlTGlzdDogZnVuY3Rpb24gcXVlcnlBcnRpY2xlTGlzdChmbGFnKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNygpIHsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoIV90aGlzNy5pc1F1ZXJ5aW5nKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDcubmV4dCA9IDI7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgaWYgKCFmbGFnKSB7CiAgICAgICAgICAgICAgICBfdGhpczcudGFibGVMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOmYsuaKluWumuaXtuWZqAogICAgICAgICAgICAgIGlmIChfdGhpczcucXVlcnlEZWJvdW5jZVRpbWVyKSB7CiAgICAgICAgICAgICAgICBjbGVhclRpbWVvdXQoX3RoaXM3LnF1ZXJ5RGVib3VuY2VUaW1lcik7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAvLyDorr7nva7pmLLmipbvvIwzMDBtc+WQjuaJp+ihjOafpeivogogICAgICAgICAgICAgIF90aGlzNy5xdWVyeURlYm91bmNlVGltZXIgPSBzZXRUaW1lb3V0KC8qI19fUFVSRV9fKi8oMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU2KCkgewogICAgICAgICAgICAgICAgdmFyIHBhcmFtcywgZGF0YSwgc291cmNlU24sIHF5a2pkdFBhcmFtcywgcmVzLCBhcnRpY2xlTGlzdDsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU2JChfY29udGV4dDYpIHsKICAgICAgICAgICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ2LnByZXYgPSBfY29udGV4dDYubmV4dCkgewogICAgICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5wcmV2ID0gMDsKICAgICAgICAgICAgICAgICAgICAgIGlmIChmbGFnID09PSAic291cmNlSXRlbUNoYW5nZWQiKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5nbG9iYWxMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5pc1F1ZXJ5aW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICAgIHBhcmFtcyA9IHsKICAgICAgICAgICAgICAgICAgICAgICAgbTogMSwKICAgICAgICAgICAgICAgICAgICAgICAgcGFnZU51bTogX3RoaXM3LmN1cnJlbnRQYWdlLAogICAgICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZTogX3RoaXM3LnBhZ2VTaXplLAogICAgICAgICAgICAgICAgICAgICAgICBpZDogX3RoaXM3LiRyb3V0ZS5xdWVyeS5tZW51VHlwZSA/ICIxIiA6IF90aGlzNy4kcm91dGUucXVlcnkuaWQsCiAgICAgICAgICAgICAgICAgICAgICAgIGlzU29ydDogX3RoaXM3LlNlYWNoRGF0YS5zb3J0TW9kZSwKICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZVR5cGU6IF90aGlzNy5TZWFjaERhdGEudGltZVJhbmdlICE9IDYgPyBfdGhpczcuU2VhY2hEYXRhLnRpbWVSYW5nZSA6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICBzdGFydFRpbWU6IF90aGlzNy5TZWFjaERhdGEuY3VzdG9tRGF5ID8gX3RoaXM3LlNlYWNoRGF0YS5jdXN0b21EYXlbMF0gOiAiIiwKICAgICAgICAgICAgICAgICAgICAgICAgZW5kVGltZTogX3RoaXM3LlNlYWNoRGF0YS5jdXN0b21EYXkgPyBfdGhpczcuU2VhY2hEYXRhLmN1c3RvbURheVsxXSA6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICBjb2xsZWN0aW9uRGF0ZVR5cGU6IF90aGlzNy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlICE9IDYgPyBfdGhpczcuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSA6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICBjb2xsZWN0aW9uU3RhcnRUaW1lOiBfdGhpczcuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lID8gX3RoaXM3LlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZVswXSA6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICBjb2xsZWN0aW9uRW5kVGltZTogX3RoaXM3LlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZSA/IF90aGlzNy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMV0gOiAiIiwKICAgICAgICAgICAgICAgICAgICAgICAga2V5d29yZHM6IF90aGlzNy5TZWFjaERhdGEua2V5d29yZCwKICAgICAgICAgICAgICAgICAgICAgICAgaXNUZWNobm9sb2d5OiBfdGhpczcuU2VhY2hEYXRhLmlzVGVjaG5vbG9neSwKICAgICAgICAgICAgICAgICAgICAgICAgZW1vdGlvbjogX3RoaXM3LlNlYWNoRGF0YS5lbW90aW9uLAogICAgICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDEKICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICBpZiAoX3RoaXM3LiRyb3V0ZS5xdWVyeS5tZW51VHlwZSkgewogICAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMubWVudVR5cGUgPSBfdGhpczcuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlOwogICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaehOW7uuafpeivouWPguaVsAogICAgICAgICAgICAgICAgICAgICAgaWYgKF90aGlzNy5zYXZlZENoZWNrYm94RGF0YSAmJiBfdGhpczcuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gX3RoaXM3LnNhdmVkQ2hlY2tib3hEYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLmxhYmVsOwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgc291cmNlU24gPSBfdGhpczcuc2F2ZWRDaGVja2JveERhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uc291cmNlU247CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMud2VDaGF0TmFtZSA9IFN0cmluZyhkYXRhKTsKICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zLnNvdXJjZVNuID0gU3RyaW5nKHNvdXJjZVNuKTsKICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAvLyDorrDlvZXlhbPplK7or43ljoblj7IKICAgICAgICAgICAgICAgICAgICAgIGlmIChwYXJhbXMua2V5d29yZHMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgKDAsIF9hcnRpY2xlSGlzdG9yeS5hZGRBcnRpY2xlSGlzdG9yeSkoewogICAgICAgICAgICAgICAgICAgICAgICAgIGtleXdvcmQ6IHBhcmFtcy5rZXl3b3JkcywKICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAyCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5nZXRBcnRpY2xlSGlzdG9yeSgpOwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczcuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgewogICAgICAgICAgICAgICAgICAgICAgICBxeWtqZHRQYXJhbXMgPSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnZU51bTogX3RoaXM3LmN1cnJlbnRQYWdlLAogICAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplOiBfdGhpczcucGFnZVNpemUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgc291cmNlU246IF90aGlzNy4kcm91dGUucXVlcnkuZG9tYWluLAogICAgICAgICAgICAgICAgICAgICAgICAgIGlzU29ydDogX3RoaXM3LlNlYWNoRGF0YS5zb3J0TW9kZQogICAgICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgaWYgKCFfdGhpczcuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgewogICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE0OwogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2luZGV4LmRlZmF1bHQucXlramR0QXJ0aWNsZUxpc3QoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBxeWtqZHRQYXJhbXMpKTsKICAgICAgICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQ2LnQwID0gX2NvbnRleHQ2LnNlbnQ7CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE3OwogICAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTY7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2luZGV4LmRlZmF1bHQuS2VJbnRlZ3JhdGlvbigoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHBhcmFtcykpOwogICAgICAgICAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYudDAgPSBfY29udGV4dDYuc2VudDsKICAgICAgICAgICAgICAgICAgICBjYXNlIDE3OgogICAgICAgICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ2LnQwOwogICAgICAgICAgICAgICAgICAgICAgaWYgKCEocmVzLmNvZGUgPT0gMjAwKSkgewogICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDMxOwogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczcuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgewogICAgICAgICAgICAgICAgICAgICAgICBhcnRpY2xlTGlzdCA9IHJlcy5yb3dzIHx8IFtdOwogICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgYXJ0aWNsZUxpc3QgPSByZXMuZGF0YS5saXN0IHx8IFtdOwogICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgIC8vIOWOu+mHjemAu+i+ke+8muWPquacieWcqOayoeacieWFs+mUruivjeaQnOe0ouaXtuaJjei/m+ihjOWOu+mHjQogICAgICAgICAgICAgICAgICAgICAgaWYgKCFfdGhpczcuU2VhY2hEYXRhLmtleXdvcmQgfHwgX3RoaXM3LlNlYWNoRGF0YS5rZXl3b3JkLnRyaW0oKSA9PT0gIiIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgYXJ0aWNsZUxpc3QgPSBfdGhpczcuZGVkdXBsaWNhdGVBcnRpY2xlcyhhcnRpY2xlTGlzdCk7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczcuQXJ0aWNsZUxpc3QgPSBhcnRpY2xlTGlzdDsKICAgICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczcuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgewogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczcudG90YWwgPSByZXMudG90YWwgfHwgMDsKICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7CiAgICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5oGi5aSN6YCJ5Lit54q25oCB77yI6Z2Z6buY5oGi5aSN77yM5LiN6Kem5Y+R5Y+z5L6n5p+l6K+i77yJCiAgICAgICAgICAgICAgICAgICAgICBpZiAoX3RoaXM3LnNhdmVkQ2hlY2tib3hEYXRhICYmIF90aGlzNy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7CiAgICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgICAgLy8g5aSE55CG5YiG6aG15Li656m655qE5oOF5Ya1CiAgICAgICAgICAgICAgICAgICAgICBpZiAoIShfdGhpczcuQXJ0aWNsZUxpc3QubGVuZ3RoID09IDAgJiYgX3RoaXM3LnBhZ2VTaXplICogKF90aGlzNy5jdXJyZW50UGFnZSAtIDEpID49IF90aGlzNy50b3RhbCAmJiBfdGhpczcudG90YWwgIT0gMCkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAyOTsKICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczcuY3VycmVudFBhZ2UgPSBNYXRoLm1heCgxLCBNYXRoLmNlaWwoX3RoaXM3LnRvdGFsIC8gX3RoaXM3LnBhZ2VTaXplKSk7CiAgICAgICAgICAgICAgICAgICAgICAvLyDph43mlrDmn6Xor6IKICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMjg7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM3LnF1ZXJ5QXJ0aWNsZUxpc3QoKTsKICAgICAgICAgICAgICAgICAgICBjYXNlIDI4OgogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICAgICAgICAgIGNhc2UgMjk6CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDMyOwogICAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICAgICAgY2FzZSAzMToKICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICLojrflj5bmlbDmja7lpLHotKUiKTsKICAgICAgICAgICAgICAgICAgICBjYXNlIDMyOgogICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAzODsKICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgIGNhc2UgMzQ6CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYucHJldiA9IDM0OwogICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQ2LnQxID0gX2NvbnRleHQ2WyJjYXRjaCJdKDApOwogICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5p+l6K+i5paH56ug5YiX6KGo5aSx6LSlOiIsIF9jb250ZXh0Ni50MSk7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOwogICAgICAgICAgICAgICAgICAgIGNhc2UgMzg6CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYucHJldiA9IDM4OwogICAgICAgICAgICAgICAgICAgICAgX3RoaXM3LmlzUXVlcnlpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5nbG9iYWxMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczcudGFibGVMb2FkaW5nID0gZmFsc2U7IC8vIOafpeivouWujOaIkOWQjuWFs+mXrWxvYWRpbmcKICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy5idXR0b25EaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5maW5pc2goMzgpOwogICAgICAgICAgICAgICAgICAgIGNhc2UgNDQ6CiAgICAgICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuc3RvcCgpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9LCBfY2FsbGVlNiwgbnVsbCwgW1swLCAzNCwgMzgsIDQ0XV0pOwogICAgICAgICAgICAgIH0pKSwgMTAwMCk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOa7muWKqOWIsOmhtumDqAogICAgc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseTogZnVuY3Rpb24gc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDlsJ3or5XlpJrnp43mu5rliqjmlrnlvI/noa7kv53mu5rliqjmiJDlip8KICAgICAgICB2YXIgc2Nyb2xsQm94RWxlbWVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5zY29sbEJveCIpOwogICAgICAgIGlmIChzY3JvbGxCb3hFbGVtZW50KSB7CiAgICAgICAgICBzY3JvbGxCb3hFbGVtZW50LnNjcm9sbFRvcCA9IDA7CiAgICAgICAgfQoKICAgICAgICAvLyDlpoLmnpxNYWluQXJ0aWNsZee7hOS7tuaciXNjcm9sbOW8leeUqO+8jOS5n+Wwneivlea7muWKqOWugwogICAgICAgIGlmIChfdGhpczguJHJlZnMubWFpbkFydGljbGUgJiYgX3RoaXM4LiRyZWZzLm1haW5BcnRpY2xlLiRyZWZzICYmIF90aGlzOC4kcmVmcy5tYWluQXJ0aWNsZS4kcmVmcy5zY3JvbGwpIHsKICAgICAgICAgIF90aGlzOC4kcmVmcy5tYWluQXJ0aWNsZS4kcmVmcy5zY3JvbGwuc2Nyb2xsVG9wID0gMDsKICAgICAgICB9CgogICAgICAgIC8vIOa7muWKqOaVtOS4quWPs+S+p+WMuuWfnwogICAgICAgIHZhciByaWdodE1haW4gPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIucmlnaHRNYWluIik7CiAgICAgICAgaWYgKHJpZ2h0TWFpbikgewogICAgICAgICAgcmlnaHRNYWluLnNjcm9sbFRvcCA9IDA7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRBcmVhOiBmdW5jdGlvbiBnZXRBcmVhKCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTgoKSB7CiAgICAgICAgdmFyIFJlc3BvbnNlOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU4JChfY29udGV4dDgpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OC5wcmV2ID0gX2NvbnRleHQ4Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0OC5wcmV2ID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDgubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF9pbmRleC5kZWZhdWx0LmdldEFyZWFMaXN0KCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBSZXNwb25zZSA9IF9jb250ZXh0OC5zZW50OwogICAgICAgICAgICAgIGlmIChSZXNwb25zZSAmJiBSZXNwb25zZS5jb2RlID09IDIwMCAmJiBSZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICAgICAgICBfdGhpczkuYXJlYUxpc3QgPSBSZXNwb25zZS5kYXRhWzBdIHx8IFtdOwogICAgICAgICAgICAgICAgX3RoaXM5LmNvdW50cnlMaXN0ID0gUmVzcG9uc2UuZGF0YVsxXSB8fCBbXTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCLojrflj5blnLDljLrmlbDmja7lpLHotKXmiJbmlbDmja7kuLrnqboiKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSAxMTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0OC5wcmV2ID0gNzsKICAgICAgICAgICAgICBfY29udGV4dDgudDAgPSBfY29udGV4dDhbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5Yy65Z+f5pWw5o2u5aSx6LSlOiIsIF9jb250ZXh0OC50MCk7CiAgICAgICAgICAgICAgX3RoaXM5LiRtZXNzYWdlLmVycm9yKCLlnLDljLrmlbDmja7ojrflj5blpLHotKUiKTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOCwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBnZXRBcnRpY2xlSGlzdG9yeTogZnVuY3Rpb24gZ2V0QXJ0aWNsZUhpc3RvcnkoKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTkoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlOSQoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDkucHJldiA9IF9jb250ZXh0OS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDkucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ5Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2FydGljbGVIaXN0b3J5Lmxpc3RBcnRpY2xlSGlzdG9yeSkoewogICAgICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgICAgIHBhZ2VTaXplOiA1LAogICAgICAgICAgICAgICAgdHlwZTogMgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ5LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcyAmJiByZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczEwLmhpc3RvcnlMaXN0ID0gcmVzLnJvd3MgfHwgW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0OS5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfY29udGV4dDkucHJldiA9IDc7CiAgICAgICAgICAgICAgX2NvbnRleHQ5LnQwID0gX2NvbnRleHQ5WyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWOhuWPsuiusOW9leWksei0pToiLCBfY29udGV4dDkudDApOwogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU5LCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOagkeiKgueCuei/h+a7pOaWueazlQogICAgZmlsdGVyTm9kZTogZnVuY3Rpb24gZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgfSwKICAgIC8vIOa3u+WKoEVzU2VhY2jmlrnms5Xku6Xlhbzlrrnljp/mnInosIPnlKgKICAgIEVzU2VhY2g6IGZ1bmN0aW9uIEVzU2VhY2goZmxhZykgewogICAgICBpZiAoZmxhZyA9PT0gImZpbHRlciIpIHsKICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsKICAgICAgICAvLyDnrZvpgInlj5jljJbml7blkIzml7bmn6Xor6LmoJHlkozliJfooagKICAgICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhbbku5bmg4XlhrXlj6rmn6Xor6LliJfooagKICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsKICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsKICAgICAgfQogICAgfSwKICAgIC8vIOmHjee9ruaQnOe0ouadoeS7tiAtIOeugOWMlueJiOacrAogICAgcmVzZXR0aW5nOiBmdW5jdGlvbiByZXNldHRpbmcoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5TZWFjaERhdGEgPSB7CiAgICAgICAgICBtZXRhTW9kZTogIiIgLyog5Yy56YWN5qih5byPICovLAogICAgICAgICAga2V5d29yZDogIiIgLyog5YWz6ZSu6K+NICovLAogICAgICAgICAgdGltZVJhbmdlOiAiIiAvKiDml7bpl7TojIPlm7QgKi8sCiAgICAgICAgICBjdXN0b21EYXk6IFtdIC8qIOiHquWumuS5ieWkqSAqLywKICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZTogbnVsbCAvKiDml7bpl7TojIPlm7QgKi8sCiAgICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLAogICAgICAgICAgaXNUZWNobm9sb2d5OiAiMSIsCiAgICAgICAgICBzb3J0TW9kZTogIjQiLAogICAgICAgICAgZW1vdGlvbjogIjAiCiAgICAgICAgfTsKICAgICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsKICAgICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCLph43nva7mkJzntKLmnaHku7bml7blh7rplJk6IiwgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIumHjee9ruaQnOe0ouadoeS7tuWksei0pSIpOwogICAgICB9CiAgICB9LAogICAgcmVtb3ZlSGlzdG9yeTogZnVuY3Rpb24gcmVtb3ZlSGlzdG9yeShpdGVtLCB0eXBlKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTEwKCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMCQoX2NvbnRleHQxMCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxMC5wcmV2ID0gX2NvbnRleHQxMC5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDEwLnByZXYgPSAwOwogICAgICAgICAgICAgIGlmIChfdGhpczExLmhpc3RvcnlUaW1lb3V0KSB7CiAgICAgICAgICAgICAgICBjbGVhclRpbWVvdXQoX3RoaXMxMS5oaXN0b3J5VGltZW91dCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmICghKGl0ZW0gJiYgaXRlbS5pZCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDE1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDU7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYXJ0aWNsZUhpc3RvcnkuZGVsQXJ0aWNsZUhpc3RvcnkpKFtpdGVtLmlkXSk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBpZiAoISh0eXBlID09IDEpKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAxMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoX3RoaXMxMS4kcmVmc1sia2V5d29yZFJlZiJdKSB7CiAgICAgICAgICAgICAgICBfdGhpczExLiRyZWZzWyJrZXl3b3JkUmVmIl0uZm9jdXMoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gOTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMS5nZXRBcnRpY2xlSGlzdG9yeSgpOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMTU7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMTEuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAxNTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMS5nZXRBcnRpY2xlSGlzdG9yeTEoKTsKICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAyMTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgICBfY29udGV4dDEwLnByZXYgPSAxNzsKICAgICAgICAgICAgICBfY29udGV4dDEwLnQwID0gX2NvbnRleHQxMFsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLliKDpmaTljoblj7LorrDlvZXml7blh7rplJk6IiwgX2NvbnRleHQxMC50MCk7CiAgICAgICAgICAgICAgX3RoaXMxMS4kbWVzc2FnZS5lcnJvcigi5Yig6Zmk5Y6G5Y+y6K6w5b2V5aSx6LSlIik7CiAgICAgICAgICAgIGNhc2UgMjE6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTAuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxMCwgbnVsbCwgW1swLCAxN11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgc2hvd0hpc3RvcnlMaXN0OiBmdW5jdGlvbiBzaG93SGlzdG9yeUxpc3QoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5zaG93SGlzdG9yeSA9IHRydWU7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigi5pi+56S65Y6G5Y+y5YiX6KGo5pe25Ye66ZSZOiIsIGVycm9yKTsKICAgICAgfQogICAgfSwKICAgIGhpZGVIaXN0b3J5TGlzdDogZnVuY3Rpb24gaGlkZUhpc3RvcnlMaXN0KCkgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHRyeSB7CiAgICAgICAgaWYgKHRoaXMuaGlzdG9yeVRpbWVvdXQpIHsKICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsKICAgICAgICB9CiAgICAgICAgdGhpcy5oaXN0b3J5VGltZW91dCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMxMi5zaG93SGlzdG9yeSA9IGZhbHNlOwogICAgICAgIH0sIDUwMCk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigi6ZqQ6JeP5Y6G5Y+y5YiX6KGo5pe25Ye66ZSZOiIsIGVycm9yKTsKICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7IC8vIOehruS/neWcqOWHuumUmeaXtuS5n+iDvemakOiXj+WIl+ihqAogICAgICB9CiAgICB9LAogICAgLy8g5YWz6ZSu6K+N5Y6G5Y+y6YCJ5oupIC0g55u05o6l5LuOV2VjaGF0LnZ1ZeWkjeWItgogICAga2V5d29yZHNDaGFuZ2U6IGZ1bmN0aW9uIGtleXdvcmRzQ2hhbmdlKGl0ZW0pIHsKICAgICAgdGhpcy5TZWFjaERhdGEua2V5d29yZCA9IGl0ZW0ua2V5d29yZDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IGZhbHNlOwogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIC8vIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOwogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsKICAgIH0sCiAgICBjbGVhckhpc3Rvcnk6IGZ1bmN0aW9uIGNsZWFySGlzdG9yeSgpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTEoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTExJChfY29udGV4dDExKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDExLnByZXYgPSBfY29udGV4dDExLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0MTEucHJldiA9IDA7CiAgICAgICAgICAgICAgaWYgKF90aGlzMTMuaGlzdG9yeVRpbWVvdXQpIHsKICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dChfdGhpczEzLmhpc3RvcnlUaW1lb3V0KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKF90aGlzMTMuJHJlZnNbImtleXdvcmRSZWYiXSkgewogICAgICAgICAgICAgICAgX3RoaXMxMy4kcmVmc1sia2V5d29yZFJlZiJdLmZvY3VzKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0MTEubmV4dCA9IDU7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYXJ0aWNsZUhpc3RvcnkuY2xlYW5BcnRpY2xlSGlzdG9yeSkoMik7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBfY29udGV4dDExLm5leHQgPSA3OwogICAgICAgICAgICAgIHJldHVybiBfdGhpczEzLmdldEFydGljbGVIaXN0b3J5KCk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfY29udGV4dDExLm5leHQgPSAxMzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgIF9jb250ZXh0MTEucHJldiA9IDk7CiAgICAgICAgICAgICAgX2NvbnRleHQxMS50MCA9IF9jb250ZXh0MTFbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5riF6Zmk5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIF9jb250ZXh0MTEudDApOwogICAgICAgICAgICAgIF90aGlzMTMuJG1lc3NhZ2UuZXJyb3IoIua4hemZpOWOhuWPsuiusOW9leWksei0pSIpOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDExLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTEsIG51bGwsIFtbMCwgOV1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgbW9yZUhpc3Rvcnk6IGZ1bmN0aW9uIG1vcmVIaXN0b3J5KCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOwogICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7CiAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IHRydWU7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L295pu05aSa5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIGVycm9yKTsKICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBnZXRBcnRpY2xlSGlzdG9yeTE6IGZ1bmN0aW9uIGdldEFydGljbGVIaXN0b3J5MSgpIHsKICAgICAgdmFyIF90aGlzMTQgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTIoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMiQoX2NvbnRleHQxMikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxMi5wcmV2ID0gX2NvbnRleHQxMi5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDEyLnByZXYgPSAwOwogICAgICAgICAgICAgIF90aGlzMTQuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0MTIubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYXJ0aWNsZUhpc3RvcnkubGlzdEFydGljbGVIaXN0b3J5KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpczE0LnF1ZXJ5UGFyYW1zMSksIHt9LCB7CiAgICAgICAgICAgICAgICB0eXBlOiAyCiAgICAgICAgICAgICAgfSkpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDEyLnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgICBfdGhpczE0Lmhpc3RvcnlMaXN0MSA9IHJlc3BvbnNlLnJvd3MgfHwgW107CiAgICAgICAgICAgICAgICBfdGhpczE0LnRvdGFsMSA9IHJlc3BvbnNlLnRvdGFsIHx8IDA7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMTQuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICBfY29udGV4dDEyLm5leHQgPSAxNDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgIF9jb250ZXh0MTIucHJldiA9IDk7CiAgICAgICAgICAgICAgX2NvbnRleHQxMi50MCA9IF9jb250ZXh0MTJbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5paH56ug5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIF9jb250ZXh0MTIudDApOwogICAgICAgICAgICAgIF90aGlzMTQuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczE0LiRtZXNzYWdlLmVycm9yKCLojrflj5bmkJzntKLljoblj7LlpLHotKUiKTsKICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTEyLCBudWxsLCBbWzAsIDldXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIG9wZW5Vcmw6IGZ1bmN0aW9uIG9wZW5VcmwodXJsKSB7CiAgICAgIHdpbmRvdy5vcGVuKHVybCwgIl9ibGFuayIpOwogICAgfSwKICAgIC8vIOWkhOeQhui/h+a7pOaQnOe0ou+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJCiAgICBoYW5kbGVGaWx0ZXJTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZUZpbHRlclNlYXJjaChrZXl3b3JkKSB7CiAgICAgIGlmICh0aGlzLmlzTGVmdFJlc2V0KSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDkuI3kv53lrZjlvZPliY3pgInkuK3nirbmgIHvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4KCiAgICAgIC8vIOabtOaWsOafpeivouWPguaVsOS4reeahCBmaWx0ZXJ3b3JkcwogICAgICB0aGlzLnRyZWVRdWVyeS5maWx0ZXJ3b3JkcyA9IGtleXdvcmQgfHwgIiI7CgogICAgICAvLyDph43nva7liLDnrKzkuIDpobUKICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOwogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsKCiAgICAgIC8vIOiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8iOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8iQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOwogICAgfSwKICAgIC8vIOWkhOeQhuaVsOaNrua6kOWIhuexu+WPmOWMlu+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJCiAgICBoYW5kbGVDbGFzc2lmeUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ2xhc3NpZnlDaGFuZ2UoY2xhc3NpZnlWYWx1ZSkgewogICAgICAvLyDkuI3kv53lrZjlvZPliY3pgInkuK3nirbmgIHvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4KCiAgICAgIC8vIOabtOaWsOmAieS4reeahOWIhuexuwogICAgICB0aGlzLnNlbGVjdGVkQ2xhc3NpZnkgPSBjbGFzc2lmeVZhbHVlOwoKICAgICAgLy8g6YeN572u5Yiw56ys5LiA6aG1CiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7CgogICAgICAvLyDlj6rosIPnlKjmoJHmlbDmja7mn6Xor6LmjqXlj6PlubbmgaLlpI3pgInkuK3nirbmgIHvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsKICAgIH0sCiAgICAvLyDlpITnkIblm73lrrbnrZvpgInlj5jljJbvvIjmnaXoh6ogVHJlZVRhYmxlIOe7hOS7tu+8iQogICAgaGFuZGxlQ291bnRyeUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ291bnRyeUNoYW5nZShjb3VudHJ5VmFsdWUpIHsKICAgICAgLy8g5LiN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uCgogICAgICAvLyDmm7TmlrDpgInkuK3nmoTlm73lrrYKICAgICAgdGhpcy5zZWxlY3RlZENvdW50cnkgPSBjb3VudHJ5VmFsdWU7CgogICAgICAvLyDph43nva7liLDnrKzkuIDpobUKICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOwogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsKCiAgICAgIC8vIOWPquiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrgogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOwogICAgfSwKICAgIC8vIOWkhOeQhuagkeW9ouWIhumhtemhteeggeWPmOWMlgogICAgaGFuZGxlVHJlZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVRyZWVDdXJyZW50Q2hhbmdlKHBhZ2UpIHsKICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSBwYWdlOwogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsKICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsKICAgIH0sCiAgICAvLyDlpITnkIbmoJHlvaLliIbpobXmr4/pobXlpKflsI/lj5jljJYKICAgIGhhbmRsZVRyZWVQYWdlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlVHJlZVBhZ2VTaXplQ2hhbmdlKHNpemUpIHsKICAgICAgdGhpcy50cmVlUGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuU2VhY2hEYXRhLmhhc0NhY2hlID0gIjEiOwogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOwogICAgfSwKICAgIC8vIOa3u+WKoOe8uuWkseeahG9wZW5OZXdWaWV35pa55rOVCiAgICBvcGVuTmV3VmlldzogZnVuY3Rpb24gb3Blbk5ld1ZpZXcoaXRlbSkgewogICAgICB3aW5kb3cub3BlbigiL2V4cHJlc3NEZXRhaWxzP2lkPSIuY29uY2F0KGl0ZW0uaWQsICImZG9jSWQ9IikuY29uY2F0KGl0ZW0uZG9jSWQsICImc291cmNlVHlwZT0iKS5jb25jYXQoaXRlbS5zb3VyY2VUeXBlKSwgIl9ibGFuayIpOwogICAgfSwKICAgIC8vIOa3u+WKoOe8uuWkseeahGhhbmRsZUhpc3RvcnlQYWdpbmF0aW9u5pa55rOVCiAgICBoYW5kbGVIaXN0b3J5UGFnaW5hdGlvbjogZnVuY3Rpb24gaGFuZGxlSGlzdG9yeVBhZ2luYXRpb24oKSB7CiAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7CiAgICB9LAogICAgLy8g5paH56ug5Y676YeN5pa55rOVCiAgICBkZWR1cGxpY2F0ZUFydGljbGVzOiBmdW5jdGlvbiBkZWR1cGxpY2F0ZUFydGljbGVzKGFydGljbGVzKSB7CiAgICAgIGlmICghYXJ0aWNsZXMgfHwgYXJ0aWNsZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuIGFydGljbGVzOwogICAgICB9CiAgICAgIHZhciB0aXRsZU1hcCA9IG5ldyBNYXAoKTsKICAgICAgdmFyIHJlc3VsdCA9IFtdOwoKICAgICAgLy8g57uf6K6h55u45ZCM5qCH6aKY55qE5paH56ug5pWw6YePCiAgICAgIGFydGljbGVzLmZvckVhY2goZnVuY3Rpb24gKGFydGljbGUpIHsKICAgICAgICAvLyDljrvpmaRIVE1M5qCH562+5ZKM5omA5pyJ56m65qC85p2l5q+U6L6D5qCH6aKYCiAgICAgICAgdmFyIGNsZWFuVGl0bGUgPSBhcnRpY2xlLnRpdGxlID8gYXJ0aWNsZS50aXRsZS5yZXBsYWNlKC88W14+XSo+L2csICIiKS5yZXBsYWNlKC9ccysvZywgIiIpIDogIiI7CiAgICAgICAgaWYgKHRpdGxlTWFwLmhhcyhjbGVhblRpdGxlKSkgewogICAgICAgICAgdGl0bGVNYXAuZ2V0KGNsZWFuVGl0bGUpLmNvdW50Kys7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRpdGxlTWFwLnNldChjbGVhblRpdGxlLCB7CiAgICAgICAgICAgIGFydGljbGU6ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgYXJ0aWNsZSksCiAgICAgICAgICAgIGNvdW50OiAxLAogICAgICAgICAgICBvcmlnaW5hbFRpdGxlOiBhcnRpY2xlLnRpdGxlIC8vIOS/neWtmOWOn+Wni+agh+mimO+8iOWPr+iDveWMheWQq0hUTUzmoIfnrb7vvIkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICAvLyDnlJ/miJDljrvph43lkI7nmoTmlofnq6DliJfooagKICAgICAgdGl0bGVNYXAuZm9yRWFjaChmdW5jdGlvbiAoX3JlZjIpIHsKICAgICAgICB2YXIgYXJ0aWNsZSA9IF9yZWYyLmFydGljbGUsCiAgICAgICAgICBjb3VudCA9IF9yZWYyLmNvdW50LAogICAgICAgICAgb3JpZ2luYWxUaXRsZSA9IF9yZWYyLm9yaWdpbmFsVGl0bGU7CiAgICAgICAgaWYgKGNvdW50ID4gMSkgewogICAgICAgICAgLy8g5aaC5p6c5pyJ6YeN5aSN77yM5Zyo5qCH6aKY5ZCO6Z2i5Yqg5LiK5pWw6YeP5qCH6K6wCiAgICAgICAgICAvLyDkvb/nlKjljp/lp4vmoIfpopjvvIjkv53mjIFIVE1M5qC85byP77yJCiAgICAgICAgICBhcnRpY2xlLnRpdGxlID0gIiIuY29uY2F0KG9yaWdpbmFsVGl0bGUgfHwgIiIsICJcdUZGMDgiKS5jb25jYXQoY291bnQsICJcdUZGMDkiKTsKICAgICAgICB9CiAgICAgICAgcmVzdWx0LnB1c2goYXJ0aWNsZSk7CiAgICAgIH0pOwogICAgICByZXR1cm4gcmVzdWx0OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_topSeach", "_MainArticle", "_articleHistory", "_splitpanes", "_index2", "components", "topSeach", "MainArticle", "Splitpanes", "Pane", "TreeTable", "dicts", "data", "width", "isReSize", "currentPage", "pageSize", "total", "ArticleList", "treeDataTransfer", "checkList", "treeCurrentPage", "treePageSize", "treeTotal", "SeachData", "metaMode", "keyword", "timeRange", "customDay", "collectionDateType", "collectionTime", "isTechnology", "sortMode", "emotion", "<PERSON><PERSON><PERSON>", "buttonDisabled", "ActiveData", "seniorSerchFlag", "areaList", "countryList", "KeList", "funEsSeach", "tree<PERSON>uery", "filterwords", "domainList", "industryList", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "pageNum", "total1", "historyList1", "initializationCompleted", "loading", "tableLoading", "<PERSON><PERSON><PERSON><PERSON>", "queryDebounceTimer", "isRightFilter", "isLeftReset", "selectedClassify", "selectedCountry", "savedCheckboxData", "globalLoading", "created", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "EsSeach", "getArticleHistory", "initializeData", "t0", "console", "error", "$message", "stop", "mounted", "watch", "handler", "newVal", "oldVal", "handleFilterChange", "methods", "_this2", "_callee2", "_callee2$", "_context2", "queryArticleList", "queryTreeData", "$nextTick", "handleSelectionChange", "selectedData", "operationType", "_toConsumableArray2", "currentPageIds", "map", "item", "sourceSn", "filteredCheckList", "filter", "includes", "filteredSavedData", "combinedCheckList", "concat", "combinedSavedData", "deduplicateBySourceSn", "scrollToTopImmediately", "dataArray", "seen", "Set", "has", "add", "handleReset", "queryTreeAndList", "_this3", "_callee3", "savedData", "_callee3$", "_context3", "length", "Promise", "all", "restoreFromSavedCheckboxData", "setTimeout", "_this4", "matchedItems", "for<PERSON>ach", "savedItem", "foundItem", "find", "treeItem", "push", "$refs", "treeTable", "restoreSelectionSilently", "queryTreeDataWithRestoreFromSaved", "_this5", "_callee4", "_callee4$", "_context4", "handleCurrentChange", "current", "handleSizeChange", "size", "_this6", "_callee5", "params", "res", "dataList", "mapData", "_callee5$", "_context5", "platformType", "id", "$route", "query", "menuType", "m", "dateType", "startTime", "endTime", "collectionStartTime", "collectionEndTime", "keywords", "thinkTankClassification", "countryOf<PERSON><PERSON>in", "api", "monitoringMedium", "sent", "code", "rows", "index", "Date", "now", "Math", "random", "toString", "substring", "label", "cnName", "count", "articleCount", "orderNum", "country", "url", "finish", "flag", "_this7", "_callee7", "_callee7$", "_context7", "abrupt", "clearTimeout", "_callee6", "qykjdtParams", "articleList", "_callee6$", "_context6", "isSort", "weChatName", "String", "addArticleHistory", "type", "then", "domain", "qykjdtArticleList", "_objectSpread2", "KeIntegration", "list", "trim", "deduplicateArticles", "max", "ceil", "msg", "t1", "_this8", "scrollBoxElement", "document", "querySelector", "scrollTop", "mainArticle", "scroll", "rightMain", "getArea", "_this9", "_callee8", "Response", "_callee8$", "_context8", "getAreaList", "warn", "_this10", "_callee9", "_callee9$", "_context9", "listArticleHistory", "filterNode", "value", "indexOf", "resetting", "removeHistory", "_this11", "_callee10", "_callee10$", "_context10", "delArticleHistory", "focus", "getArticleHistory1", "showHistoryList", "hideHistoryList", "_this12", "keywordsChange", "clearHistory", "_this13", "_callee11", "_callee11$", "_context11", "cleanArticleHistory", "moreHistory", "_this14", "_callee12", "response", "_callee12$", "_context12", "openUrl", "window", "open", "handleFilterSearch", "handleClassifyChange", "classifyValue", "handleCountryChange", "countryValue", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "openNewView", "docId", "sourceType", "handleHistoryPagination", "articles", "titleMap", "Map", "result", "article", "cleanTitle", "title", "replace", "get", "set", "originalTitle", "_ref2"], "sources": ["src/views/KeMonitor/keMonitor.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AAMA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MAEA;MACAC,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MACA;MACAC,cAAA;MACAC,UAAA;MACAC,eAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACAC,OAAA;QACApC,QAAA;MACA;MACAqC,MAAA;MACAC,YAAA;MACAC,uBAAA;MAAA;MACA;MACAC,OAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,iBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAEA;YACAT,KAAA,CAAA1B,UAAA,GAAA0B,KAAA,CAAAW,OAAA;;YAEA;YACAX,KAAA,CAAAY,iBAAA;;YAEA;YACA;YACA;;YAEA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAa,cAAA;UAAA;YAEA;YACAb,KAAA,CAAAZ,uBAAA;YAAAoB,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;YAEAO,OAAA,CAAAC,KAAA,aAAAR,QAAA,CAAAM,EAAA;YACAd,KAAA,CAAAiB,QAAA,CAAAD,KAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EAEA;EAEAc,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,cAAA,WAAAA,eAAA;MAAA,IAAAa,MAAA;MAAA,WAAAzB,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuB,SAAA;QAAA,WAAAxB,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAEA;cACAiB,MAAA,CAAAI,gBAAA;cACA;cAAAD,SAAA,CAAAnB,IAAA;cAAA,OACAgB,MAAA,CAAAK,aAAA;YAAA;cAAAF,SAAA,CAAAnB,IAAA;cAAA,OAEAgB,MAAA,CAAAM,SAAA;YAAA;cAAAH,SAAA,CAAAnB,IAAA;cAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAAAoB,SAAA,CAAAf,EAAA,GAAAe,SAAA;cAEAd,OAAA,CAAAC,KAAA,aAAAa,SAAA,CAAAf,EAAA;cACAY,MAAA,CAAAT,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IAEA;IAEA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,YAAA,EAAAC,aAAA;MACA,IAAAA,aAAA,oBAAAA,aAAA;QACA;QACA,KAAAlF,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,KAAArC,iBAAA,OAAAuC,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;MACA,WACAC,aAAA,0BACAA,aAAA,mBACA;QACA;QACA;QACA,IAAAE,cAAA,QAAArF,gBAAA,CAAAsF,GAAA,CACA,UAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAC,iBAAA,QAAAxF,SAAA,CAAAyF,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAI,iBAAA,QAAA/C,iBAAA,CAAA6C,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;;QAEA;QACA,IAAAK,iBAAA,MAAAC,MAAA,KAAAV,mBAAA,CAAAlC,OAAA,EAAAuC,iBAAA,OAAAL,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,IAAAa,iBAAA,MAAAD,MAAA,KAAAV,mBAAA,CAAAlC,OAAA,EAAA0C,iBAAA,OAAAR,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;;QAEA;QACA,KAAAjF,SAAA,QAAA+F,qBAAA,CAAAH,iBAAA;QACA,KAAAhD,iBAAA,QAAAmD,qBAAA,CAAAD,iBAAA;MACA;QACA;QACA,KAAA9F,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,KAAArC,iBAAA,OAAAuC,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;MACA;;MAEA;MACA,KAAAtF,WAAA;MACA,KAAAqG,sBAAA;MACA,UAAAxD,aAAA;QACA,KAAAqC,gBAAA;MACA;IACA;IAEA;IACAkB,qBAAA,WAAAA,sBAAAE,SAAA;MACA,IAAAC,IAAA,OAAAC,GAAA;MACA,OAAAF,SAAA,CAAAR,MAAA,WAAAH,IAAA;QACA,IAAAY,IAAA,CAAAE,GAAA,CAAAd,IAAA,CAAAC,QAAA;UACA;QACA;QACAW,IAAA,CAAAG,GAAA,CAAAf,IAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA;IACAe,WAAA,WAAAA,YAAA;MACA;MACA,KAAAhF,SAAA,CAAAC,WAAA;MACA,KAAAmB,gBAAA;MACA,KAAAC,eAAA;;MAEA;MACA,KAAAF,WAAA;;MAEA;MACA,KAAAzC,SAAA;;MAEA;MACA,KAAA4C,iBAAA;;MAEA;MACA,KAAAjD,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAAkF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAhC,kBAAA,WAAAA,mBAAA;MACA,KAAA/B,aAAA;;MAEA;MACA;;MAEA;MACA,KAAA7C,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAkF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsD,SAAA;QAAA,IAAAC,SAAA;QAAA,WAAAxD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAEA;cACAkD,SAAA,OAAAvB,mBAAA,CAAAlC,OAAA,EAAAuD,MAAA,CAAA5D,iBAAA,GAEA;cACA,IAAA8D,SAAA,IAAAA,SAAA,CAAAG,MAAA;gBACAL,MAAA,CAAAxG,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAyD,SAAA;cACA;gBACA;gBACAF,MAAA,CAAAxG,SAAA;cACA;;cAEA;cAAA4G,SAAA,CAAAnD,IAAA;cAAA,OACAqD,OAAA,CAAAC,GAAA,EACAP,MAAA,CAAA1B,aAAA,IACA0B,MAAA,CAAA3B,gBAAA;cAAA,CACA;YAAA;cAEA;cACA2B,MAAA,CAAA5D,iBAAA,GAAA8D,SAAA;;cAEA;cACA,IAAAF,MAAA,CAAA5D,iBAAA,IAAA4D,MAAA,CAAA5D,iBAAA,CAAAiE,MAAA;gBACAL,MAAA,CAAAQ,4BAAA;cACA;;cAEA;cACAR,MAAA,CAAAhE,aAAA;cACAyE,UAAA;gBACAT,MAAA,CAAA/D,WAAA;cACA;cAAAmE,SAAA,CAAAnD,IAAA;cAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAAAoD,SAAA,CAAA/C,EAAA,GAAA+C,SAAA;cAEA9C,OAAA,CAAAC,KAAA,gBAAA6C,SAAA,CAAA/C,EAAA;cACA2C,MAAA,CAAAxC,QAAA,CAAAD,KAAA;cACA;cACAyC,MAAA,CAAAhE,aAAA;cACAyE,UAAA;gBACAT,MAAA,CAAA/D,WAAA;cACA;YAAA;YAAA;cAAA,OAAAmE,SAAA,CAAA3C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IAEA;IACAO,4BAAA,WAAAA,6BAAA;MAAA,IAAAE,MAAA;MACA,UAAAtE,iBAAA,SAAAA,iBAAA,CAAAiE,MAAA;QACA;MACA;;MAEA;MACA,IAAAM,YAAA;MACA,KAAAvE,iBAAA,CAAAwE,OAAA,WAAAC,SAAA;QACA,IAAAC,SAAA,GAAAJ,MAAA,CAAAnH,gBAAA,CAAAwH,IAAA,CACA,UAAAC,QAAA;UAAA,OAAAA,QAAA,CAAAjC,QAAA,KAAA8B,SAAA,CAAA9B,QAAA;QAAA,CACA;QACA,IAAA+B,SAAA;UACAH,YAAA,CAAAM,IAAA,CAAAH,SAAA;QACA;MACA;MAEA,IAAAH,YAAA,CAAAN,MAAA;QACA;QACA,KAAA7G,SAAA,GAAAmH,YAAA;QACA;QACA,KAAApC,SAAA;UACA,IAAAmC,MAAA,CAAAQ,KAAA,CAAAC,SAAA;YACAT,MAAA,CAAAQ,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAT,YAAA;UACA;QACA;MACA;QACA;QACA,KAAAnH,SAAA;MACA;IACA;IAEA;IAEA;IACA6H,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9E,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4E,SAAA;QAAA,WAAA7E,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cAAAwE,SAAA,CAAAzE,IAAA;cAEA;cACA,IAAAsE,MAAA,CAAAlF,iBAAA,IAAAkF,MAAA,CAAAlF,iBAAA,CAAAiE,MAAA;gBACAiB,MAAA,CAAA9H,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAA6E,MAAA,CAAAlF,iBAAA;cACA;gBACAkF,MAAA,CAAA9H,SAAA;cACA;;cAEA;cAAAiI,SAAA,CAAAxE,IAAA;cAAA,OACAqE,MAAA,CAAAhD,aAAA;YAAA;cAEA;cACA,IAAAgD,MAAA,CAAAlF,iBAAA,IAAAkF,MAAA,CAAAlF,iBAAA,CAAAiE,MAAA;gBACAiB,MAAA,CAAAd,4BAAA;cACA;cAAAiB,SAAA,CAAAxE,IAAA;cAAA;YAAA;cAAAwE,SAAA,CAAAzE,IAAA;cAAAyE,SAAA,CAAApE,EAAA,GAAAoE,SAAA;cAEAnE,OAAA,CAAAC,KAAA,CACA,6BAAAkE,SAAA,CAAApE,EAEA;YAAA;YAAA;cAAA,OAAAoE,SAAA,CAAAhE,IAAA;UAAA;QAAA,GAAA8D,QAAA;MAAA;IAEA;IAEA;IACAG,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAxI,WAAA,GAAAwI,OAAA;MACA,KAAAnC,sBAAA;MACA,KAAAnB,gBAAA;IACA;IAEAuD,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAzI,QAAA,GAAAyI,IAAA;MACA,KAAA1I,WAAA;MACA,KAAAqG,sBAAA;MACA,KAAAnB,gBAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAwD,MAAA;MAAA,WAAAtF,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoF,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,QAAA,EAAA7I,KAAA,EAAA8I,OAAA;QAAA,WAAAzF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArF,IAAA,GAAAqF,SAAA,CAAApF,IAAA;YAAA;cACA6E,MAAA,CAAAlG,OAAA;cAAAyG,SAAA,CAAArF,IAAA;cAEAgF,MAAA;gBACAxG,OAAA,EAAAsG,MAAA,CAAArI,eAAA;gBACAL,QAAA,EAAA0I,MAAA,CAAApI,YAAA;gBACA4I,YAAA;gBACAC,EAAA,EAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAF,EAAA;gBACAI,CAAA;gBACAC,QAAA,EACAd,MAAA,CAAAlI,SAAA,CAAAG,SAAA,QAAA+H,MAAA,CAAAlI,SAAA,CAAAG,SAAA;gBACA8I,SAAA,EAAAf,MAAA,CAAAlI,SAAA,CAAAI,SAAA,GACA8H,MAAA,CAAAlI,SAAA,CAAAI,SAAA,MACA;gBACA8I,OAAA,EAAAhB,MAAA,CAAAlI,SAAA,CAAAI,SAAA,GAAA8H,MAAA,CAAAlI,SAAA,CAAAI,SAAA;gBACAC,kBAAA,EACA6H,MAAA,CAAAlI,SAAA,CAAAK,kBAAA,QACA6H,MAAA,CAAAlI,SAAA,CAAAK,kBAAA,GACA;gBACA8I,mBAAA,EAAAjB,MAAA,CAAAlI,SAAA,CAAAM,cAAA,GACA4H,MAAA,CAAAlI,SAAA,CAAAM,cAAA,MACA;gBACA8I,iBAAA,EAAAlB,MAAA,CAAAlI,SAAA,CAAAM,cAAA,GACA4H,MAAA,CAAAlI,SAAA,CAAAM,cAAA,MACA;gBACA+I,QAAA,EAAAnB,MAAA,CAAAlI,SAAA,CAAAE,OAAA;gBACAK,YAAA,EAAA2H,MAAA,CAAAlI,SAAA,CAAAO,YAAA;gBACAE,OAAA,EAAAyH,MAAA,CAAAlI,SAAA,CAAAS,OAAA;gBACA;gBACAU,WAAA,EAAA+G,MAAA,CAAAhH,SAAA,CAAAC,WAAA;gBACA;gBACAmI,uBAAA,EAAApB,MAAA,CAAA5F,gBAAA;gBACA;gBACAiH,eAAA,EAAArB,MAAA,CAAA3F,eAAA;gBACA7B,QAAA,EAAAwH,MAAA,CAAAlI,SAAA,CAAAU;cACA;cAEA,IAAAwH,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;gBACAV,MAAA,CAAAU,QAAA,GAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;cACA;cAAAL,SAAA,CAAApF,IAAA;cAAA,OAEAmG,cAAA,CAAAC,gBAAA,CAAArB,MAAA;YAAA;cAAAC,GAAA,GAAAI,SAAA,CAAAiB,IAAA;cAEA,IAAArB,GAAA,CAAAsB,IAAA;gBACArB,QAAA,GAAAD,GAAA,CAAAuB,IAAA;gBACAnK,KAAA,GAAA4I,GAAA,CAAA5I,KAAA;gBAEA8I,OAAA,YAAAA,QAAAnJ,IAAA;kBAAA,OACAA,IAAA,CAAA6F,GAAA,WAAAC,IAAA,EAAA2E,KAAA;oBAAA;sBACAlB,EAAA,KAAAlD,MAAA,CACAP,IAAA,CAAAC,QAAA,oBAAAM,MAAA,CACAoE,KAAA,OAAApE,MAAA,CAAAqE,IAAA,CAAAC,GAAA,SAAAtE,MAAA,CAAAuE,IAAA,CAAAC,MAAA,GACAC,QAAA,KACAC,SAAA;sBAAA;sBACAC,KAAA,EAAAlF,IAAA,CAAAmF,MAAA;sBACAC,KAAA,EAAApF,IAAA,CAAAqF,YAAA;sBACAC,QAAA,EAAAtF,IAAA,CAAAsF,QAAA;sBACAC,OAAA,EAAAvF,IAAA,CAAAqE,eAAA;sBACApE,QAAA,EAAAD,IAAA,CAAAC,QAAA;sBACAuF,GAAA,EAAAxF,IAAA,CAAAwF,GAAA;oBACA;kBAAA;gBAAA;gBAEAxC,MAAA,CAAAvI,gBAAA,GAAA4I,OAAA,CAAAD,QAAA;gBACAJ,MAAA,CAAAnI,SAAA,GAAAN,KAAA;cACA;cAAAgJ,SAAA,CAAApF,IAAA;cAAA;YAAA;cAAAoF,SAAA,CAAArF,IAAA;cAAAqF,SAAA,CAAAhF,EAAA,GAAAgF,SAAA;cAEA/E,OAAA,CAAAC,KAAA,aAAA8E,SAAA,CAAAhF,EAAA;cACAyE,MAAA,CAAAtE,QAAA,CAAAD,KAAA;YAAA;cAAA8E,SAAA,CAAArF,IAAA;cAEA8E,MAAA,CAAAlG,OAAA;cAAA,OAAAyG,SAAA,CAAAkC,MAAA;YAAA;YAAA;cAAA,OAAAlC,SAAA,CAAA5E,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IAEA;IAEA;IACA1D,gBAAA,WAAAA,iBAAAmG,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjI,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+H,SAAA;QAAA,WAAAhI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,IAAA,GAAA4H,SAAA,CAAA3H,IAAA;YAAA;cAAA,KAEAwH,MAAA,CAAA3I,UAAA;gBAAA8I,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAA,OAAA2H,SAAA,CAAAC,MAAA;YAAA;cAIA,KAAAL,IAAA;gBACAC,MAAA,CAAA5I,YAAA;cACA;;cAEA;cACA,IAAA4I,MAAA,CAAA1I,kBAAA;gBACA+I,YAAA,CAAAL,MAAA,CAAA1I,kBAAA;cACA;;cAEA;cACA0I,MAAA,CAAA1I,kBAAA,GAAA0E,UAAA,kBAAAjE,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoI,SAAA;gBAAA,IAAA/C,MAAA,EAAAhJ,IAAA,EAAA+F,QAAA,EAAAiG,YAAA,EAAA/C,GAAA,EAAAgD,WAAA;gBAAA,WAAAvI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAqI,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAAlI,IAAA;oBAAA;sBAAAkI,SAAA,CAAAnI,IAAA;sBAEA,IAAAwH,IAAA;wBACAC,MAAA,CAAApI,aAAA;sBACA;sBAEAoI,MAAA,CAAA3I,UAAA;sBAEAkG,MAAA;wBACAW,CAAA;wBACAnH,OAAA,EAAAiJ,MAAA,CAAAtL,WAAA;wBACAC,QAAA,EAAAqL,MAAA,CAAArL,QAAA;wBACAmJ,EAAA,EAAAkC,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAA+B,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAF,EAAA;wBACA6C,MAAA,EAAAX,MAAA,CAAA7K,SAAA,CAAAQ,QAAA;wBACAwI,QAAA,EACA6B,MAAA,CAAA7K,SAAA,CAAAG,SAAA,QAAA0K,MAAA,CAAA7K,SAAA,CAAAG,SAAA;wBACA8I,SAAA,EAAA4B,MAAA,CAAA7K,SAAA,CAAAI,SAAA,GACAyK,MAAA,CAAA7K,SAAA,CAAAI,SAAA,MACA;wBACA8I,OAAA,EAAA2B,MAAA,CAAA7K,SAAA,CAAAI,SAAA,GACAyK,MAAA,CAAA7K,SAAA,CAAAI,SAAA,MACA;wBACAC,kBAAA,EACAwK,MAAA,CAAA7K,SAAA,CAAAK,kBAAA,QACAwK,MAAA,CAAA7K,SAAA,CAAAK,kBAAA,GACA;wBACA8I,mBAAA,EAAA0B,MAAA,CAAA7K,SAAA,CAAAM,cAAA,GACAuK,MAAA,CAAA7K,SAAA,CAAAM,cAAA,MACA;wBACA8I,iBAAA,EAAAyB,MAAA,CAAA7K,SAAA,CAAAM,cAAA,GACAuK,MAAA,CAAA7K,SAAA,CAAAM,cAAA,MACA;wBACA+I,QAAA,EAAAwB,MAAA,CAAA7K,SAAA,CAAAE,OAAA;wBACAK,YAAA,EAAAsK,MAAA,CAAA7K,SAAA,CAAAO,YAAA;wBACAE,OAAA,EAAAoK,MAAA,CAAA7K,SAAA,CAAAS,OAAA;wBACAiI,YAAA;sBACA;sBAEA,IAAAmC,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA;wBACAV,MAAA,CAAAU,QAAA,GAAA+B,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA;sBACA;;sBAEA;sBACA,IAAA+B,MAAA,CAAArI,iBAAA,IAAAqI,MAAA,CAAArI,iBAAA,CAAAiE,MAAA;wBACArH,IAAA,GAAAyL,MAAA,CAAArI,iBAAA,CAAAyC,GAAA,WAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAkF,KAAA;wBAAA;wBACAjF,QAAA,GAAA0F,MAAA,CAAArI,iBAAA,CAAAyC,GAAA,CACA,UAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAC,QAAA;wBAAA,CACA;wBAEAiD,MAAA,CAAAqD,UAAA,GAAAC,MAAA,CAAAtM,IAAA;wBACAgJ,MAAA,CAAAjD,QAAA,GAAAuG,MAAA,CAAAvG,QAAA;sBACA;;sBAEA;sBACA,IAAAiD,MAAA,CAAAiB,QAAA;wBACA,IAAAsC,iCAAA;0BAAAzL,OAAA,EAAAkI,MAAA,CAAAiB,QAAA;0BAAAuC,IAAA;wBAAA,GAAAC,IAAA,CACA;0BACAhB,MAAA,CAAAtH,iBAAA;wBACA,CACA;sBACA;sBAIA,IAAAsH,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAV,YAAA;0BACAxJ,OAAA,EAAAiJ,MAAA,CAAAtL,WAAA;0BACAC,QAAA,EAAAqL,MAAA,CAAArL,QAAA;0BACA2F,QAAA,EAAA0F,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;0BACAN,MAAA,EAAAX,MAAA,CAAA7K,SAAA,CAAAQ;wBACA;sBACA;sBAAA,KAEAqK,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBAAAP,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAAAkI,SAAA,CAAAlI,IAAA;sBAAA,OACAmG,cAAA,CAAAuC,iBAAA,KAAAC,cAAA,CAAAnJ,OAAA,MAAAuI,YAAA;oBAAA;sBAAAG,SAAA,CAAA9H,EAAA,GAAA8H,SAAA,CAAA7B,IAAA;sBAAA6B,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAAAkI,SAAA,CAAAlI,IAAA;sBAAA,OACAmG,cAAA,CAAAyC,aAAA,KAAAD,cAAA,CAAAnJ,OAAA,MAAAuF,MAAA;oBAAA;sBAAAmD,SAAA,CAAA9H,EAAA,GAAA8H,SAAA,CAAA7B,IAAA;oBAAA;sBAFArB,GAAA,GAAAkD,SAAA,CAAA9H,EAAA;sBAAA,MAIA4E,GAAA,CAAAsB,IAAA;wBAAA4B,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAGA,IAAAwH,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAT,WAAA,GAAAhD,GAAA,CAAAuB,IAAA;sBACA;wBACAyB,WAAA,GAAAhD,GAAA,CAAAjJ,IAAA,CAAA8M,IAAA;sBACA;;sBAEA;sBACA,IACA,CAAArB,MAAA,CAAA7K,SAAA,CAAAE,OAAA,IACA2K,MAAA,CAAA7K,SAAA,CAAAE,OAAA,CAAAiM,IAAA,WACA;wBACAd,WAAA,GAAAR,MAAA,CAAAuB,mBAAA,CAAAf,WAAA;sBACA;sBAEAR,MAAA,CAAAnL,WAAA,GAAA2L,WAAA;sBAEA,IAAAR,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAjB,MAAA,CAAApL,KAAA,GAAA4I,GAAA,CAAA5I,KAAA;sBACA;wBACAoL,MAAA,CAAApL,KAAA,GAAA4I,GAAA,CAAAjJ,IAAA,CAAAK,KAAA;sBACA;;sBAEA;sBACA,IAAAoL,MAAA,CAAArI,iBAAA,IAAAqI,MAAA,CAAArI,iBAAA,CAAAiE,MAAA;wBACAoE,MAAA,CAAAjE,4BAAA;sBACA;;sBAEA;sBAAA,MAEAiE,MAAA,CAAAnL,WAAA,CAAA+G,MAAA,SACAoE,MAAA,CAAArL,QAAA,IAAAqL,MAAA,CAAAtL,WAAA,SAAAsL,MAAA,CAAApL,KAAA,IACAoL,MAAA,CAAApL,KAAA;wBAAA8L,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAEAwH,MAAA,CAAAtL,WAAA,GAAAyK,IAAA,CAAAqC,GAAA,CACA,GACArC,IAAA,CAAAsC,IAAA,CAAAzB,MAAA,CAAApL,KAAA,GAAAoL,MAAA,CAAArL,QAAA,CACA;sBACA;sBAAA+L,SAAA,CAAAlI,IAAA;sBAAA,OACAwH,MAAA,CAAApG,gBAAA;oBAAA;sBAAA,OAAA8G,SAAA,CAAAN,MAAA;oBAAA;sBAAAM,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAIAwH,MAAA,CAAAjH,QAAA,CAAAD,KAAA,CAAA0E,GAAA,CAAAkE,GAAA;oBAAA;sBAAAhB,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAAAkI,SAAA,CAAAnI,IAAA;sBAAAmI,SAAA,CAAAiB,EAAA,GAAAjB,SAAA;sBAGA7H,OAAA,CAAAC,KAAA,cAAA4H,SAAA,CAAAiB,EAAA;sBACA3B,MAAA,CAAAjH,QAAA,CAAAD,KAAA;oBAAA;sBAAA4H,SAAA,CAAAnI,IAAA;sBAEAyH,MAAA,CAAA3I,UAAA;sBACA2I,MAAA,CAAApI,aAAA;sBACAoI,MAAA,CAAA5I,YAAA;sBACA4I,MAAA,CAAAlK,cAAA;sBAAA,OAAA4K,SAAA,CAAAZ,MAAA;oBAAA;oBAAA;sBAAA,OAAAY,SAAA,CAAA1H,IAAA;kBAAA;gBAAA,GAAAsH,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAnH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IAEA;IACAlF,sBAAA,WAAAA,uBAAA;MAAA,IAAA6G,MAAA;MACA,KAAA9H,SAAA;QACA;QACA,IAAA+H,gBAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,gBAAA;UACAA,gBAAA,CAAAG,SAAA;QACA;;QAEA;QACA,IACAJ,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,IACAL,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,IACAmF,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,CAAAyF,MAAA,EACA;UACAN,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,CAAAyF,MAAA,CAAAF,SAAA;QACA;;QAEA;QACA,IAAAG,SAAA,GAAAL,QAAA,CAAAC,aAAA;QACA,IAAAI,SAAA;UACAA,SAAA,CAAAH,SAAA;QACA;MACA;IACA;IACAI,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtK,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoK,SAAA;QAAA,IAAAC,QAAA;QAAA,WAAAtK,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlK,IAAA,GAAAkK,SAAA,CAAAjK,IAAA;YAAA;cAAAiK,SAAA,CAAAlK,IAAA;cAAAkK,SAAA,CAAAjK,IAAA;cAAA,OAEAmG,cAAA,CAAA+D,WAAA;YAAA;cAAAH,QAAA,GAAAE,SAAA,CAAA5D,IAAA;cACA,IAAA0D,QAAA,IAAAA,QAAA,CAAAzD,IAAA,WAAAyD,QAAA,CAAAhO,IAAA;gBACA8N,MAAA,CAAApM,QAAA,GAAAsM,QAAA,CAAAhO,IAAA;gBACA8N,MAAA,CAAAnM,WAAA,GAAAqM,QAAA,CAAAhO,IAAA;cACA;gBACAsE,OAAA,CAAA8J,IAAA;cACA;cAAAF,SAAA,CAAAjK,IAAA;cAAA;YAAA;cAAAiK,SAAA,CAAAlK,IAAA;cAAAkK,SAAA,CAAA7J,EAAA,GAAA6J,SAAA;cAEA5J,OAAA,CAAAC,KAAA,cAAA2J,SAAA,CAAA7J,EAAA;cACAyJ,MAAA,CAAAtJ,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA2J,SAAA,CAAAzJ,IAAA;UAAA;QAAA,GAAAsJ,QAAA;MAAA;IAEA;IAEA5J,iBAAA,WAAAA,kBAAA;MAAA,IAAAkK,OAAA;MAAA,WAAA7K,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA2K,SAAA;QAAA,IAAArF,GAAA;QAAA,WAAAvF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxK,IAAA,GAAAwK,SAAA,CAAAvK,IAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAvK,IAAA;cAAA,OAEA,IAAAwK,kCAAA;gBACAjM,OAAA;gBACApC,QAAA;gBACAoM,IAAA;cACA;YAAA;cAJAvD,GAAA,GAAAuF,SAAA,CAAAlE,IAAA;cAKA,IAAArB,GAAA,IAAAA,GAAA,CAAAsB,IAAA;gBACA8D,OAAA,CAAAlM,WAAA,GAAA8G,GAAA,CAAAuB,IAAA;cACA;cAAAgE,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAnK,EAAA,GAAAmK,SAAA;cAEAlK,OAAA,CAAAC,KAAA,cAAAiK,SAAA,CAAAnK,EAAA;YAAA;YAAA;cAAA,OAAAmK,SAAA,CAAA/J,IAAA;UAAA;QAAA,GAAA6J,QAAA;MAAA;IAEA;IAEA;IACAI,UAAA,WAAAA,WAAAC,KAAA,EAAA3O,IAAA;MACA,KAAA2O,KAAA;MACA,OAAA3O,IAAA,CAAAgL,KAAA,CAAA4D,OAAA,CAAAD,KAAA;IACA;IAEA;IACAzK,OAAA,WAAAA,QAAAsH,IAAA;MACA,IAAAA,IAAA;QACA,KAAAhF,sBAAA;QACA;QACA,KAAAO,gBAAA;MACA;QACA;QACA,KAAAP,sBAAA;QACA,KAAAnB,gBAAA;MACA;IACA;IAEA;IACAwJ,SAAA,WAAAA,UAAA;MACA;QACA,KAAAjO,SAAA;UACAC,QAAA;UACAC,OAAA;UACAC,SAAA;UACAC,SAAA;UACAC,kBAAA;UACAC,cAAA;UACAC,YAAA;UACAC,QAAA;UACAC,OAAA;QACA;QAEA,KAAAlB,WAAA;QACA,KAAAqG,sBAAA;QACA,KAAAO,gBAAA;MACA,SAAAxC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEAuK,aAAA,WAAAA,cAAAhJ,IAAA,EAAA0G,IAAA;MAAA,IAAAuC,OAAA;MAAA,WAAAvL,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqL,UAAA;QAAA,WAAAtL,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoL,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlL,IAAA,GAAAkL,UAAA,CAAAjL,IAAA;YAAA;cAAAiL,UAAA,CAAAlL,IAAA;cAEA,IAAA+K,OAAA,CAAA3M,cAAA;gBACA0J,YAAA,CAAAiD,OAAA,CAAA3M,cAAA;cACA;cAAA,MAEA0D,IAAA,IAAAA,IAAA,CAAAyD,EAAA;gBAAA2F,UAAA,CAAAjL,IAAA;gBAAA;cAAA;cAAAiL,UAAA,CAAAjL,IAAA;cAAA,OACA,IAAAkL,iCAAA,GAAArJ,IAAA,CAAAyD,EAAA;YAAA;cAAA,MAEAiD,IAAA;gBAAA0C,UAAA,CAAAjL,IAAA;gBAAA;cAAA;cACA,IAAA8K,OAAA,CAAA7G,KAAA;gBACA6G,OAAA,CAAA7G,KAAA,eAAAkH,KAAA;cACA;cAAAF,UAAA,CAAAjL,IAAA;cAAA,OACA8K,OAAA,CAAA5K,iBAAA;YAAA;cAAA+K,UAAA,CAAAjL,IAAA;cAAA;YAAA;cAAAiL,UAAA,CAAAjL,IAAA;cAAA,OAEA8K,OAAA,CAAA5K,iBAAA;YAAA;cAAA+K,UAAA,CAAAjL,IAAA;cAAA,OACA8K,OAAA,CAAAM,kBAAA;YAAA;cAAAH,UAAA,CAAAjL,IAAA;cAAA;YAAA;cAAAiL,UAAA,CAAAlL,IAAA;cAAAkL,UAAA,CAAA7K,EAAA,GAAA6K,UAAA;cAIA5K,OAAA,CAAAC,KAAA,eAAA2K,UAAA,CAAA7K,EAAA;cACA0K,OAAA,CAAAvK,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA2K,UAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAuK,SAAA;MAAA;IAEA;IAEAM,eAAA,WAAAA,gBAAA;MACA;QACA,KAAApN,WAAA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;MACA;IACA;IAEAgL,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;QACA,SAAApN,cAAA;UACA0J,YAAA,MAAA1J,cAAA;QACA;QAEA,KAAAA,cAAA,GAAAqF,UAAA;UACA+H,OAAA,CAAAtN,WAAA;QACA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAArC,WAAA;MACA;IACA;IAEA;IACAuN,cAAA,WAAAA,eAAA3J,IAAA;MACA,KAAAlF,SAAA,CAAAE,OAAA,GAAAgF,IAAA,CAAAhF,OAAA;MACA,KAAAuB,cAAA;MACA,KAAAmE,sBAAA;MACA,KAAArG,WAAA;MACA;MACA,KAAAkF,gBAAA;IACA;IAEAqK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiM,UAAA;QAAA,WAAAlM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9L,IAAA,GAAA8L,UAAA,CAAA7L,IAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAEA,IAAA2L,OAAA,CAAAvN,cAAA;gBACA0J,YAAA,CAAA6D,OAAA,CAAAvN,cAAA;cACA;cAEA,IAAAuN,OAAA,CAAAzH,KAAA;gBACAyH,OAAA,CAAAzH,KAAA,eAAAkH,KAAA;cACA;cAAAU,UAAA,CAAA7L,IAAA;cAAA,OAEA,IAAA8L,mCAAA;YAAA;cAAAD,UAAA,CAAA7L,IAAA;cAAA,OACA0L,OAAA,CAAAxL,iBAAA;YAAA;cAAA2L,UAAA,CAAA7L,IAAA;cAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAAA8L,UAAA,CAAAzL,EAAA,GAAAyL,UAAA;cAEAxL,OAAA,CAAAC,KAAA,eAAAuL,UAAA,CAAAzL,EAAA;cACAsL,OAAA,CAAAnL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAuL,UAAA,CAAArL,IAAA;UAAA;QAAA,GAAAmL,SAAA;MAAA;IAEA;IAEAI,WAAA,WAAAA,YAAA;MACA;QACA,KAAA1N,cAAA;QACA,KAAA+M,kBAAA;QACA,KAAAhN,cAAA;MACA,SAAAkC,KAAA;QACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACA,KAAAjC,cAAA;MACA;IACA;IAEA+M,kBAAA,WAAAA,mBAAA;MAAA,IAAAY,OAAA;MAAA,WAAAzM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuM,UAAA;QAAA,IAAAC,QAAA;QAAA,WAAAzM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArM,IAAA,GAAAqM,UAAA,CAAApM,IAAA;YAAA;cAAAoM,UAAA,CAAArM,IAAA;cAEAiM,OAAA,CAAA3N,cAAA;cAAA+N,UAAA,CAAApM,IAAA;cAAA,OACA,IAAAwK,kCAAA,MAAA7B,cAAA,CAAAnJ,OAAA,MAAAmJ,cAAA,CAAAnJ,OAAA,MACAwM,OAAA,CAAA1N,YAAA;gBACAiK,IAAA;cAAA,EACA;YAAA;cAHA2D,QAAA,GAAAE,UAAA,CAAA/F,IAAA;cAKA,IAAA6F,QAAA;gBACAF,OAAA,CAAAvN,YAAA,GAAAyN,QAAA,CAAA3F,IAAA;gBACAyF,OAAA,CAAAxN,MAAA,GAAA0N,QAAA,CAAA9P,KAAA;cACA;cAEA4P,OAAA,CAAA3N,cAAA;cAAA+N,UAAA,CAAApM,IAAA;cAAA;YAAA;cAAAoM,UAAA,CAAArM,IAAA;cAAAqM,UAAA,CAAAhM,EAAA,GAAAgM,UAAA;cAEA/L,OAAA,CAAAC,KAAA,iBAAA8L,UAAA,CAAAhM,EAAA;cACA4L,OAAA,CAAA3N,cAAA;cACA2N,OAAA,CAAAzL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA8L,UAAA,CAAA5L,IAAA;UAAA;QAAA,GAAAyL,SAAA;MAAA;IAEA;IAEAI,OAAA,WAAAA,QAAAhF,GAAA;MACAiF,MAAA,CAAAC,IAAA,CAAAlF,GAAA;IACA;IAEA;IACAmF,kBAAA,WAAAA,mBAAA3P,OAAA;MACA,SAAAmC,WAAA;QACA;MACA;;MAEA;;MAEA;MACA,KAAAnB,SAAA,CAAAC,WAAA,GAAAjB,OAAA;;MAEA;MACA,KAAAL,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAqI,oBAAA,WAAAA,qBAAAC,aAAA;MACA;;MAEA;MACA,KAAAzN,gBAAA,GAAAyN,aAAA;;MAEA;MACA,KAAAlQ,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAuI,mBAAA,WAAAA,oBAAAC,YAAA;MACA;;MAEA;MACA,KAAA1N,eAAA,GAAA0N,YAAA;;MAEA;MACA,KAAApQ,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAyI,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAtQ,eAAA,GAAAsQ,IAAA;MACA,KAAAnQ,SAAA,CAAAU,QAAA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACA2I,wBAAA,WAAAA,yBAAAnI,IAAA;MACA,KAAAnI,YAAA,GAAAmI,IAAA;MACA,KAAApI,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACA4I,WAAA,WAAAA,YAAAnL,IAAA;MACAyK,MAAA,CAAAC,IAAA,uBAAAnK,MAAA,CACAP,IAAA,CAAAyD,EAAA,aAAAlD,MAAA,CAAAP,IAAA,CAAAoL,KAAA,kBAAA7K,MAAA,CAAAP,IAAA,CAAAqL,UAAA,GACA,QACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MACA,KAAA/B,kBAAA;IACA;IAEA;IACArC,mBAAA,WAAAA,oBAAAqE,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAAhK,MAAA;QACA,OAAAgK,QAAA;MACA;MAEA,IAAAC,QAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;;MAEA;MACAH,QAAA,CAAAzJ,OAAA,WAAA6J,OAAA;QACA;QACA,IAAAC,UAAA,GAAAD,OAAA,CAAAE,KAAA,GACAF,OAAA,CAAAE,KAAA,CAAAC,OAAA,iBAAAA,OAAA,eACA;QAEA,IAAAN,QAAA,CAAA1K,GAAA,CAAA8K,UAAA;UACAJ,QAAA,CAAAO,GAAA,CAAAH,UAAA,EAAAxG,KAAA;QACA;UACAoG,QAAA,CAAAQ,GAAA,CAAAJ,UAAA;YACAD,OAAA,MAAA7E,cAAA,CAAAnJ,OAAA,MAAAgO,OAAA;YACAvG,KAAA;YACA6G,aAAA,EAAAN,OAAA,CAAAE,KAAA;UACA;QACA;MACA;;MAEA;MACAL,QAAA,CAAA1J,OAAA,WAAAoK,KAAA;QAAA,IAAAP,OAAA,GAAAO,KAAA,CAAAP,OAAA;UAAAvG,KAAA,GAAA8G,KAAA,CAAA9G,KAAA;UAAA6G,aAAA,GAAAC,KAAA,CAAAD,aAAA;QACA,IAAA7G,KAAA;UACA;UACA;UACAuG,OAAA,CAAAE,KAAA,MAAAtL,MAAA,CAAA0L,aAAA,kBAAA1L,MAAA,CAAA6E,KAAA;QACA;QACAsG,MAAA,CAAAvJ,IAAA,CAAAwJ,OAAA;MACA;MAEA,OAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}