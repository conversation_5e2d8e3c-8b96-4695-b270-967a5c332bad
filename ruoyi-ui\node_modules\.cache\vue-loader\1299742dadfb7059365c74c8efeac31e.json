{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753944647587}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXBpIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHRvcFNlYWNoIGZyb20gIkAvdmlld3MvY29tcG9uZW50cy90b3BTZWFjaC52dWUiOw0KaW1wb3J0IE1haW5BcnRpY2xlIGZyb20gIi4uL2NvbXBvbmVudHMvTWFpbkFydGljbGUudnVlIjsNCmltcG9ydCB7DQogIGxpc3RBcnRpY2xlSGlzdG9yeSwNCiAgZGVsQXJ0aWNsZUhpc3RvcnksDQogIGFkZEFydGljbGVIaXN0b3J5LA0KICBjbGVhbkFydGljbGVIaXN0b3J5LA0KfSBmcm9tICJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5IjsNCmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsNCmltcG9ydCAic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIjsNCmltcG9ydCBUcmVlVGFibGUgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVUYWJsZS9pbmRleC52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgdG9wU2VhY2gsIE1haW5BcnRpY2xlLCBTcGxpdHBhbmVzLCBQYW5lLCBUcmVlVGFibGUgfSwNCiAgZGljdHM6IFsiaXNfdGVjaG5vbG9neSJdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB3aWR0aDogIjI1OCIsDQogICAgICBpc1JlU2l6ZTogZmFsc2UsDQogICAgICAvKiDmlofnq6DkuLvkvZPnu4Tku7bmlbDmja4gKi8NCiAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgcGFnZVNpemU6IDUwLA0KICAgICAgdG90YWw6IDAsDQogICAgICBBcnRpY2xlTGlzdDogW10sDQogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwNCiAgICAgIGNoZWNrTGlzdDogW10sDQogICAgICAvKiDmoJHlvaLliIbpobXmlbDmja4gKi8NCiAgICAgIHRyZWVDdXJyZW50UGFnZTogMSwNCiAgICAgIHRyZWVQYWdlU2l6ZTogMTAwLA0KICAgICAgdHJlZVRvdGFsOiAwLA0KDQogICAgICAvKiDmkJzntKLnu4Tku7bmlbDmja4gKi8NCiAgICAgIFNlYWNoRGF0YTogew0KICAgICAgICBtZXRhTW9kZTogIiIgLyog5Yy56YWN5qih5byPICovLA0KICAgICAgICBrZXl3b3JkOiAiIiAvKiDlhbPplK7or40gKi8sDQogICAgICAgIHRpbWVSYW5nZTogNCAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgIGN1c3RvbURheTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBjb2xsZWN0aW9uRGF0ZVR5cGU6IG51bGwgLyog5pe26Ze06IyD5Zu0ICovLA0KICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBpc1RlY2hub2xvZ3k6ICIxIiwNCiAgICAgICAgc29ydE1vZGU6ICI0IiwNCiAgICAgICAgZW1vdGlvbjogIjAiLA0KICAgICAgICBoYXNDYWNoZTogIjAiLA0KICAgICAgfSAvKiDmkJzntKLmnaHku7YgKi8sDQogICAgICAvKiDmjpLluo/mqKHlvI8gLSDljZXni6zmj5Dlj5bvvIzpgb/lhY3op6blj5FTZWFjaERhdGHnmoTmt7Hluqbnm5HlkKwgKi8NCiAgICAgIGJ1dHRvbkRpc2FibGVkOiBmYWxzZSAvKiDmjInpkq7pmLLmipYgKi8sDQogICAgICBBY3RpdmVEYXRhOiB7fSwNCiAgICAgIHNlbmlvclNlcmNoRmxhZzogZmFsc2UgLyog5pmu6YCa5qOA57Si5oiW6auY57qn5qOA57SiICovLA0KICAgICAgYXJlYUxpc3Q6IFtdIC8qIOWbveWGheWcsOWMuiAqLywNCiAgICAgIGNvdW50cnlMaXN0OiBbXSAvKiDlm73lrrbmiJblnLDljLogKi8sDQogICAgICBLZUxpc3Q6IFtdLA0KICAgICAgZnVuRXNTZWFjaDogbnVsbCwNCiAgICAgIHRyZWVRdWVyeTogew0KICAgICAgICBmaWx0ZXJ3b3JkczogIiIsIC8vIOa3u+WKoOagkeaQnOe0ouWFs+mUruWtlw0KICAgICAgfSwNCiAgICAgIGRvbWFpbkxpc3Q6IFtdLA0KICAgICAgaW5kdXN0cnlMaXN0OiBbXSwNCiAgICAgIHNob3dIaXN0b3J5OiBmYWxzZSwNCiAgICAgIGhpc3RvcnlMaXN0OiBbXSwNCiAgICAgIGhpc3RvcnlUaW1lb3V0OiBudWxsLA0KICAgICAgZGlhbG9nVmlzaWJsZTE6IGZhbHNlLA0KICAgICAgaGlzdG9yeUxvYWRpbmc6IGZhbHNlLA0KICAgICAgcXVlcnlQYXJhbXMxOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0sDQogICAgICB0b3RhbDE6IDAsDQogICAgICBoaXN0b3J5TGlzdDE6IFtdLA0KICAgICAgaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQ6IGZhbHNlLCAvLyDmoIforrDliJ3lp4vljJbmmK/lkKblrozmiJANCiAgICAgIC8vIOS7jldlY2hhdC52dWXlkIzmraXnmoTlsZ7mgKcNCiAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDmoJHnu4Tku7Zsb2FkaW5n54q25oCBDQogICAgICB0YWJsZUxvYWRpbmc6IGZhbHNlLCAvLyDooajmoLxsb2FkaW5n54q25oCBDQogICAgICBpc1F1ZXJ5aW5nOiBmYWxzZSwgLy8g5p+l6K+i6Ziy5oqWDQogICAgICBxdWVyeURlYm91bmNlVGltZXI6IG51bGwsIC8vIOafpeivoumYsuaKluWumuaXtuWZqA0KICAgICAgaXNSaWdodEZpbHRlcjogZmFsc2UsIC8vIOagh+iusOWPs+S+p+etm+mAieadoeS7tuaYr+WQpuWPkeeUn+WPmOWMlg0KICAgICAgaXNMZWZ0UmVzZXQ6IGZhbHNlLCAvLyDmoIforrDlt6bkvqfmoJHmmK/lkKbph43nva4NCiAgICAgIHNlbGVjdGVkQ2xhc3NpZnk6ICI1IiwgLy8g6YCJ5Lit55qE5pWw5o2u5rqQ5YiG57G7DQogICAgICBzZWxlY3RlZENvdW50cnk6IG51bGwsIC8vIOmAieS4reeahOWbveWutg0KICAgICAgLyog5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yI5rC45LmF5L+d5a2Y77yM5Y+q5pyJ54m55a6a5pON5L2c5omN5pu05paw77yJICovDQogICAgICBzYXZlZENoZWNrYm94RGF0YTogW10sDQogICAgICBnbG9iYWxMb2FkaW5nOiBmYWxzZSwNCiAgICB9Ow0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRyeSB7DQogICAgICAvLyDliJ3lp4vljJZmdW5Fc1NlYWNo5pa55rOVDQogICAgICB0aGlzLmZ1bkVzU2VhY2ggPSB0aGlzLkVzU2VhY2g7DQoNCiAgICAgIC8vIOWFiOWKoOi9veWfuuehgOaVsOaNrg0KICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KDQogICAgICAvLyBpZiAodGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgJiYgdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgPT09ICI4Iikgew0KICAgICAgLy8gICB0aGlzLlNlYWNoRGF0YS50aW1lUmFuZ2UgPSA3Ow0KICAgICAgLy8gfQ0KDQogICAgICAvLyDliqDovb3moJHmlbDmja7lkozlhoXlrrnmlbDmja4NCiAgICAgIGF3YWl0IHRoaXMuaW5pdGlhbGl6ZURhdGEoKTsNCg0KICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5a6M5oiQ77yM6L+Z5qC3d2F0Y2jnm5HlkKzlmajmiY3kvJrlvIDlp4vlt6XkvZwNCiAgICAgIHRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgPSB0cnVlOw0KICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICBjb25zb2xlLmVycm9yKCLnu4Tku7bliJ3lp4vljJblpLHotKU6IiwgZXJyb3IpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7DQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgLy8gVHJlZVRhYmxlIOe7hOS7tuS4jemcgOimgeeJueauiueahOeKtuaAgeajgOafpQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOetm+mAieadoeS7tuWPmOWMlg0KICAgICJTZWFjaERhdGEudGltZVJhbmdlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgIH0sDQogICAgfSwNCiAgICAiU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgIlNlYWNoRGF0YS5pc1RlY2hub2xvZ3kiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZUZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgICJTZWFjaERhdGEuZW1vdGlvbiI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliJ3lp4vljJbmlbDmja4NCiAgICBhc3luYyBpbml0aWFsaXplRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWKoOi9veaWh+eroOWIl+ihqO+8iOWGhemDqOW3sue7j+WkhOeQhuS6hiB0YWJsZUxvYWRpbmfvvIkNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgIC8vIOWKoOi9veagkeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLnF1ZXJ5VHJlZURhdGEoKTsNCiAgICAgICAgLy8g562J5b6F5qCR57uE5Lu25a6M5YWo5riy5p+TDQogICAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliJ3lp4vljJbmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliJ3lp4vljJblpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8gVHJlZVRhYmxlIOe7hOS7tuS6i+S7tuWkhOeQhuaWueazlQ0KDQogICAgLy8g5aSE55CG6YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGVkRGF0YSwgb3BlcmF0aW9uVHlwZSkgew0KICAgICAgaWYgKG9wZXJhdGlvblR5cGUgPT09ICJyb3ctY2xpY2siIHx8IG9wZXJhdGlvblR5cGUgPT09ICJjbGVhci1hbGwiKSB7DQogICAgICAgIC8vIOeCueWHu+ihjO+8iOWNlemAie+8ieaIluWPlua2iOaJgOaciemAieS4re+8muebtOaOpeabv+aNou+8jOS4jemcgOimgei/veWKoOWOu+mHjQ0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gWy4uLnNlbGVjdGVkRGF0YV07DQogICAgICB9IGVsc2UgaWYgKA0KICAgICAgICBvcGVyYXRpb25UeXBlID09PSAiY2hlY2tib3gtY2hhbmdlIiB8fA0KICAgICAgICBvcGVyYXRpb25UeXBlID09PSAic2VsZWN0LWFsbCINCiAgICAgICkgew0KICAgICAgICAvLyDngrnlh7vli77pgInmoYbvvIjlpJrpgInvvInmiJblhajpgInvvJrpnIDopoHmraPnoa7lpITnkIbpgInkuK3lkozlj5bmtojpgInkuK0NCiAgICAgICAgLy8g5YWI5LuO5L+d5a2Y55qE5pWw5o2u5Lit56e76Zmk5b2T5YmN6aG16Z2i55qE5omA5pyJ5pWw5o2uDQogICAgICAgIGNvbnN0IGN1cnJlbnRQYWdlSWRzID0gdGhpcy50cmVlRGF0YVRyYW5zZmVyLm1hcCgNCiAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5zb3VyY2VTbg0KICAgICAgICApOw0KICAgICAgICBjb25zdCBmaWx0ZXJlZENoZWNrTGlzdCA9IHRoaXMuY2hlY2tMaXN0LmZpbHRlcigNCiAgICAgICAgICAoaXRlbSkgPT4gIWN1cnJlbnRQYWdlSWRzLmluY2x1ZGVzKGl0ZW0uc291cmNlU24pDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IGZpbHRlcmVkU2F2ZWREYXRhID0gdGhpcy5zYXZlZENoZWNrYm94RGF0YS5maWx0ZXIoDQogICAgICAgICAgKGl0ZW0pID0+ICFjdXJyZW50UGFnZUlkcy5pbmNsdWRlcyhpdGVtLnNvdXJjZVNuKQ0KICAgICAgICApOw0KDQogICAgICAgIC8vIOeEtuWQjua3u+WKoOW9k+WJjemhtemdouaWsOmAieS4reeahOaVsOaNrg0KICAgICAgICBjb25zdCBjb21iaW5lZENoZWNrTGlzdCA9IFsuLi5maWx0ZXJlZENoZWNrTGlzdCwgLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgICAgY29uc3QgY29tYmluZWRTYXZlZERhdGEgPSBbLi4uZmlsdGVyZWRTYXZlZERhdGEsIC4uLnNlbGVjdGVkRGF0YV07DQoNCiAgICAgICAgLy8g5a+55ZCI5bm25ZCO55qE5pWw5o2u6L+b6KGM5Y676YeN5aSE55CGDQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gdGhpcy5kZWR1cGxpY2F0ZUJ5U291cmNlU24oY29tYmluZWRDaGVja0xpc3QpOw0KICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gdGhpcy5kZWR1cGxpY2F0ZUJ5U291cmNlU24oY29tYmluZWRTYXZlZERhdGEpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g6buY6K6k5oOF5Ya177ya55u05o6l5pu/5o2i77yI5YW85a655oCn5aSE55CG77yJDQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gWy4uLnNlbGVjdGVkRGF0YV07DQogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBbLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgIH0NCg0KICAgICAgLy8g6YeN572u6aG156CB5bm25p+l6K+i5YaF5a65DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgaWYgKCF0aGlzLmlzUmlnaHRGaWx0ZXIpIHsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCJzb3VyY2VJdGVtQ2hhbmdlZCIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmoLnmja5zb3VyY2VTbuWOu+mHjeeahOi+heWKqeaWueazlQ0KICAgIGRlZHVwbGljYXRlQnlTb3VyY2VTbihkYXRhQXJyYXkpIHsNCiAgICAgIGNvbnN0IHNlZW4gPSBuZXcgU2V0KCk7DQogICAgICByZXR1cm4gZGF0YUFycmF5LmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICBpZiAoc2Vlbi5oYXMoaXRlbS5zb3VyY2VTbikpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgc2Vlbi5hZGQoaXRlbS5zb3VyY2VTbik7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumHjee9rg0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgLy8g5YWI5riF56m66L+H5ruk5YWz6ZSu5a2X77yM6YG/5YWN6Kem5Y+RIGhhbmRsZUZpbHRlclNlYXJjaA0KICAgICAgdGhpcy50cmVlUXVlcnkuZmlsdGVyd29yZHMgPSAiIjsNCiAgICAgIHRoaXMuc2VsZWN0ZWRDbGFzc2lmeSA9IG51bGw7DQogICAgICB0aGlzLnNlbGVjdGVkQ291bnRyeSA9IG51bGw7DQoNCiAgICAgIC8vIOeEtuWQjuiuvue9rumHjee9ruagh+iusA0KICAgICAgdGhpcy5pc0xlZnRSZXNldCA9IHRydWU7DQoNCiAgICAgIC8vIOa4heepuumAieS4reeKtuaAgQ0KICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCg0KICAgICAgLy8g5riF56m65L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yI5rC45LmF5L+d5a2Y77yJDQogICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gW107DQoNCiAgICAgIC8vIOmHjee9rumhteeggeW5tuafpeivouWIl+ihqOaVsOaNrg0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KDQogICAgICAvLyDph43mlrDmn6Xor6LmoJHlkozliJfooagNCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnrZvpgInmnaHku7blj5jljJYgLSDmnaXoh6rlj7PkvqfnrZvpgInmnaHku7bnmoTlj5jljJYNCiAgICBoYW5kbGVGaWx0ZXJDaGFuZ2UoKSB7DQogICAgICB0aGlzLmlzUmlnaHRGaWx0ZXIgPSB0cnVlOyAvLyDmoIforrDlj7PkvqfnrZvpgInmnaHku7blj5HnlJ/lj5jljJYNCg0KICAgICAgLy8g5LiN5YaN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQogICAgICAvLyDmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7kvJrlnKjmn6Xor6LlkI7oh6rliqjmgaLlpI0NCg0KICAgICAgLy8g6YeN572u5YiG6aG15Yiw56ys5LiA6aG1DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMuU2VhY2hEYXRhLmhhc0NhY2hlID0gIjAiOw0KDQogICAgICAvLyDmu5rliqjliLDpobbpg6gNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KDQogICAgICAvLyDlkIzml7bmn6Xor6LmoJHlkozliJfooagNCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDlkIzml7bmn6Xor6LmoJHlkozliJfooaggLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgYXN5bmMgcXVlcnlUcmVlQW5kTGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOS/neWtmOW9k+WJjeeahOawuOS5heWLvumAieaVsOaNru+8jOmBv+WFjeWcqOafpeivoui/h+eoi+S4reS4ouWksQ0KICAgICAgICBjb25zdCBzYXZlZERhdGEgPSBbLi4udGhpcy5zYXZlZENoZWNrYm94RGF0YV07DQoNCiAgICAgICAgLy8g5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5YWI5Li05pe25oGi5aSNIGNoZWNrTGlzdCDku6Xkvr/mn6Xor6Lml7bluKbkuIrlj4LmlbANCiAgICAgICAgaWYgKHNhdmVkRGF0YSAmJiBzYXZlZERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gWy4uLnNhdmVkRGF0YV07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5riF56m66YCJ5Lit54q25oCBDQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWQjOaXtuafpeivouagkeaVsOaNruWSjOWPs+S+p+WIl+ihqO+8iOS/neaMgeaAp+iDveS8mOWKv++8iQ0KICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhKCksDQogICAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCksIC8vIHF1ZXJ5QXJ0aWNsZUxpc3Qg5YaF6YOo5bey57uP5aSE55CG5LqGIHRhYmxlTG9hZGluZw0KICAgICAgICBdKTsNCg0KICAgICAgICAvLyDnoa7kv53msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7kuI3kvJrkuKLlpLENCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IHNhdmVkRGF0YTsNCg0KICAgICAgICAvLyDmn6Xor6LlrozmiJDlkI7vvIzlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzpnZnpu5jmgaLlpI3nlYzpnaLpgInkuK3nirbmgIENCiAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmn6Xor6LlrozmiJDlkI7ph43nva7lj7PkvqfnrZvpgInmoIforrANCiAgICAgICAgdGhpcy5pc1JpZ2h0RmlsdGVyID0gZmFsc2U7DQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuaXNMZWZ0UmVzZXQgPSBmYWxzZTsNCiAgICAgICAgfSwgMzAwKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWQjOaXtuafpeivouagkeWSjOWIl+ihqOWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICAvLyDljbPkvb/lh7rplJnkuZ/opoHph43nva7moIforrANCiAgICAgICAgdGhpcy5pc1JpZ2h0RmlsdGVyID0gZmFsc2U7DQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuaXNMZWZ0UmVzZXQgPSBmYWxzZTsNCiAgICAgICAgfSwgMzAwKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5oGi5aSN6YCJ5Lit5pWw5o2u5rqQ55qE5pa55rOV5bey5Yig6Zmk77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAvLyDku47msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7mgaLlpI3pgInkuK3nirbmgIHvvIjku4XlpITnkIbnlYzpnaLpgInkuK3nirbmgIHvvIkNCiAgICByZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCkgew0KICAgICAgaWYgKCF0aGlzLnNhdmVkQ2hlY2tib3hEYXRhIHx8IHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5Zyo5b2T5YmN5qCR5pWw5o2u5Lit5p+l5om+5Yy56YWN55qE6aG5DQogICAgICBjb25zdCBtYXRjaGVkSXRlbXMgPSBbXTsNCiAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEuZm9yRWFjaCgoc2F2ZWRJdGVtKSA9PiB7DQogICAgICAgIGNvbnN0IGZvdW5kSXRlbSA9IHRoaXMudHJlZURhdGFUcmFuc2Zlci5maW5kKA0KICAgICAgICAgICh0cmVlSXRlbSkgPT4gdHJlZUl0ZW0uc291cmNlU24gPT09IHNhdmVkSXRlbS5zb3VyY2VTbg0KICAgICAgICApOw0KICAgICAgICBpZiAoZm91bmRJdGVtKSB7DQogICAgICAgICAgbWF0Y2hlZEl0ZW1zLnB1c2goZm91bmRJdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIGlmIChtYXRjaGVkSXRlbXMubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDmm7TmlrDpgInkuK3liJfooajvvIjmraTml7YgY2hlY2tMaXN0IOW3sue7j+WcqOafpeivouWJjeaBouWkjei/h+S6hu+8iQ0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IG1hdGNoZWRJdGVtczsNCiAgICAgICAgLy8g6YCa55+lIFRyZWVUYWJsZSDnu4Tku7bmgaLlpI3nlYzpnaLpgInkuK3nirbmgIHvvIjkuI3op6blj5Hkuovku7bvvIkNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnRyZWVUYWJsZSkgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlVGFibGUucmVzdG9yZVNlbGVjdGlvblNpbGVudGx5KG1hdGNoZWRJdGVtcyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOayoeacieWMuemFjemhue+8jOa4heepuumAieS4reeKtuaAgQ0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjlvZPliY3pgInkuK3nirbmgIHnmoTmlrnms5Xlt7LliKDpmaTvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4NCg0KICAgIC8vIOafpeivouagkeaVsOaNruW5tuS7juawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaBouWkjemAieS4reeKtuaAge+8iOeUqOS6juWFs+mUruWtl+i/h+a7pO+8iQ0KICAgIGFzeW5jIHF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOWFiOS4tOaXtuaBouWkjSBjaGVja0xpc3Qg5Lul5L6/5p+l6K+i5pe25bim5LiK5Y+C5pWwDQogICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gWy4uLnRoaXMuc2F2ZWRDaGVja2JveERhdGFdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmn6Xor6LmoJHmlbDmja4NCiAgICAgICAgYXdhaXQgdGhpcy5xdWVyeVRyZWVEYXRhKCk7DQoNCiAgICAgICAgLy8g5p+l6K+i5a6M5oiQ5ZCO77yM5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM6Z2Z6buY5oGi5aSN55WM6Z2i6YCJ5Lit54q25oCBDQogICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMucmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKA0KICAgICAgICAgICLmn6Xor6LmoJHmlbDmja7lubbku47msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7mgaLlpI3pgInkuK3nirbmgIHlpLHotKU6IiwNCiAgICAgICAgICBlcnJvcg0KICAgICAgICApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliIbpobXlpITnkIYNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKGN1cnJlbnQpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSBjdXJyZW50Ow0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gc2l6ZTsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgLy8g5p+l6K+i5qCR5pWw5o2uDQogICAgYXN5bmMgcXVlcnlUcmVlRGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgcGFnZU51bTogdGhpcy50cmVlQ3VycmVudFBhZ2UsDQogICAgICAgICAgcGFnZVNpemU6IHRoaXMudHJlZVBhZ2VTaXplLA0KICAgICAgICAgIHBsYXRmb3JtVHlwZTogMSwNCiAgICAgICAgICBpZDogdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgPyAiMSIgOiB0aGlzLiRyb3V0ZS5xdWVyeS5pZCwNCiAgICAgICAgICBtOiAxLA0KICAgICAgICAgIGRhdGVUeXBlOg0KICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEudGltZVJhbmdlICE9IDYgPyB0aGlzLlNlYWNoRGF0YS50aW1lUmFuZ2UgOiAiIiwNCiAgICAgICAgICBzdGFydFRpbWU6IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheQ0KICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXlbMF0NCiAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgZW5kVGltZTogdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5ID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzFdIDogIiIsDQogICAgICAgICAgY29sbGVjdGlvbkRhdGVUeXBlOg0KICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlICE9IDYNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uRGF0ZVR5cGUNCiAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICBjb2xsZWN0aW9uU3RhcnRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZQ0KICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZVswXQ0KICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICBjb2xsZWN0aW9uRW5kVGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMV0NCiAgICAgICAgICAgIDogIiIsDQogICAgICAgICAga2V5d29yZHM6IHRoaXMuU2VhY2hEYXRhLmtleXdvcmQsDQogICAgICAgICAgaXNUZWNobm9sb2d5OiB0aGlzLlNlYWNoRGF0YS5pc1RlY2hub2xvZ3ksDQogICAgICAgICAgZW1vdGlvbjogdGhpcy5TZWFjaERhdGEuZW1vdGlvbiwNCiAgICAgICAgICAvLyDmt7vliqDlhbPplK7lrZfov4fmu6Tlj4LmlbANCiAgICAgICAgICBmaWx0ZXJ3b3JkczogdGhpcy50cmVlUXVlcnkuZmlsdGVyd29yZHMgfHwgIiIsDQogICAgICAgICAgLy8g5re75Yqg5pWw5o2u5rqQ5YiG57G75Y+C5pWwDQogICAgICAgICAgdGhpbmtUYW5rQ2xhc3NpZmljYXRpb246IHRoaXMuc2VsZWN0ZWRDbGFzc2lmeSwNCiAgICAgICAgICAvLyDmt7vliqDlm73lrrbnrZvpgInlj4LmlbANCiAgICAgICAgICBjb3VudHJ5T2ZPcmlnaW46IHRoaXMuc2VsZWN0ZWRDb3VudHJ5LA0KICAgICAgICAgIGhhc0NhY2hlOiB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSwNCiAgICAgICAgfTsNCg0KICAgICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUpIHsNCiAgICAgICAgICBwYXJhbXMubWVudVR5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5tZW51VHlwZTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGFwaS5tb25pdG9yaW5nTWVkaXVtKHBhcmFtcyk7DQoNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICBjb25zdCBkYXRhTGlzdCA9IHJlcy5yb3dzIHx8IFtdOw0KICAgICAgICAgIGNvbnN0IHRvdGFsID0gcmVzLnRvdGFsIHx8IDA7DQoNCiAgICAgICAgICBjb25zdCBtYXBEYXRhID0gKGRhdGEpID0+DQogICAgICAgICAgICBkYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgICAgIGlkOiBgJHsNCiAgICAgICAgICAgICAgICBpdGVtLnNvdXJjZVNuIHx8ICJ1bmtub3duIg0KICAgICAgICAgICAgICB9XyR7aW5kZXh9XyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpDQogICAgICAgICAgICAgICAgLnRvU3RyaW5nKDM2KQ0KICAgICAgICAgICAgICAgIC5zdWJzdHJpbmcoMiwgMTEpfWAsIC8vIOehruS/nee7neWvueWUr+S4gOaApw0KICAgICAgICAgICAgICBsYWJlbDogaXRlbS5jbk5hbWUsDQogICAgICAgICAgICAgIGNvdW50OiBpdGVtLmFydGljbGVDb3VudCB8fCAwLA0KICAgICAgICAgICAgICBvcmRlck51bTogaXRlbS5vcmRlck51bSwNCiAgICAgICAgICAgICAgY291bnRyeTogaXRlbS5jb3VudHJ5T2ZPcmlnaW4gfHwgbnVsbCwNCiAgICAgICAgICAgICAgc291cmNlU246IGl0ZW0uc291cmNlU24sDQogICAgICAgICAgICAgIHVybDogaXRlbS51cmwgfHwgbnVsbCwNCiAgICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIHRoaXMudHJlZURhdGFUcmFuc2ZlciA9IG1hcERhdGEoZGF0YUxpc3QpOw0KICAgICAgICAgIHRoaXMudHJlZVRvdGFsID0gdG90YWw7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuafpeivouagkeaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluaVsOaNrua6kOWksei0pSIpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOafpeivouaWh+eroOWIl+ihqO+8iOW4pumYsuaKlu+8iQ0KICAgIGFzeW5jIHF1ZXJ5QXJ0aWNsZUxpc3QoZmxhZykgew0KICAgICAgLy8g6Ziy5q2i6YeN5aSN5p+l6K+iDQogICAgICBpZiAodGhpcy5pc1F1ZXJ5aW5nKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKCFmbGFnKSB7DQogICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5riF6Zmk5LmL5YmN55qE6Ziy5oqW5a6a5pe25ZmoDQogICAgICBpZiAodGhpcy5xdWVyeURlYm91bmNlVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u6Ziy5oqW77yMMzAwbXPlkI7miafooYzmn6Xor6INCiAgICAgIHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyID0gc2V0VGltZW91dChhc3luYyAoKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgaWYgKGZsYWcgPT09ICJzb3VyY2VJdGVtQ2hhbmdlZCIpIHsNCiAgICAgICAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IHRydWU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy5pc1F1ZXJ5aW5nID0gdHJ1ZTsNCg0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIG06IDEsDQogICAgICAgICAgICBwYWdlTnVtOiB0aGlzLmN1cnJlbnRQYWdlLA0KICAgICAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsDQogICAgICAgICAgICBpZDogdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgPyAiMSIgOiB0aGlzLiRyb3V0ZS5xdWVyeS5pZCwNCiAgICAgICAgICAgIGlzU29ydDogdGhpcy5TZWFjaERhdGEuc29ydE1vZGUsDQogICAgICAgICAgICBkYXRlVHlwZToNCiAgICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEudGltZVJhbmdlICE9IDYgPyB0aGlzLlNlYWNoRGF0YS50aW1lUmFuZ2UgOiAiIiwNCiAgICAgICAgICAgIHN0YXJ0VGltZTogdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5DQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzBdDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBlbmRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXkNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXlbMV0NCiAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZToNCiAgICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlICE9IDYNCiAgICAgICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZQ0KICAgICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBjb2xsZWN0aW9uU3RhcnRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZQ0KICAgICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lWzBdDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBjb2xsZWN0aW9uRW5kVGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZVsxXQ0KICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAga2V5d29yZHM6IHRoaXMuU2VhY2hEYXRhLmtleXdvcmQsDQogICAgICAgICAgICBpc1RlY2hub2xvZ3k6IHRoaXMuU2VhY2hEYXRhLmlzVGVjaG5vbG9neSwNCiAgICAgICAgICAgIGVtb3Rpb246IHRoaXMuU2VhY2hEYXRhLmVtb3Rpb24sDQogICAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDEsDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5tZW51VHlwZSkgew0KICAgICAgICAgICAgcGFyYW1zLm1lbnVUeXBlID0gdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5p6E5bu65p+l6K+i5Y+C5pWwDQogICAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5zYXZlZENoZWNrYm94RGF0YS5tYXAoKGl0ZW0pID0+IGl0ZW0ubGFiZWwpOw0KICAgICAgICAgICAgY29uc3Qgc291cmNlU24gPSB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLm1hcCgNCiAgICAgICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uc291cmNlU24NCiAgICAgICAgICAgICk7DQoNCiAgICAgICAgICAgIHBhcmFtcy53ZUNoYXROYW1lID0gU3RyaW5nKGRhdGEpOw0KICAgICAgICAgICAgcGFyYW1zLnNvdXJjZVNuID0gU3RyaW5nKHNvdXJjZVNuKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDorrDlvZXlhbPplK7or43ljoblj7INCiAgICAgICAgICBpZiAocGFyYW1zLmtleXdvcmRzKSB7DQogICAgICAgICAgICBhZGRBcnRpY2xlSGlzdG9yeSh7IGtleXdvcmQ6IHBhcmFtcy5rZXl3b3JkcywgdHlwZTogMiB9KS50aGVuKA0KICAgICAgICAgICAgICAoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGxldCBxeWtqZHRQYXJhbXM7DQoNCiAgICAgICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkuZG9tYWluKSB7DQogICAgICAgICAgICBxeWtqZHRQYXJhbXMgPSB7DQogICAgICAgICAgICAgIHBhZ2VOdW06IHRoaXMuY3VycmVudFBhZ2UsDQogICAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICAgICAgICBzb3VyY2VTbjogdGhpcy4kcm91dGUucXVlcnkuZG9tYWluLA0KICAgICAgICAgICAgICBpc1NvcnQ6IHRoaXMuU2VhY2hEYXRhLnNvcnRNb2RlLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCByZXMgPSB0aGlzLiRyb3V0ZS5xdWVyeS5kb21haW4NCiAgICAgICAgICAgID8gYXdhaXQgYXBpLnF5a2pkdEFydGljbGVMaXN0KHsgLi4ucXlramR0UGFyYW1zIH0pDQogICAgICAgICAgICA6IGF3YWl0IGFwaS5LZUludGVncmF0aW9uKHsgLi4ucGFyYW1zIH0pOw0KDQogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgbGV0IGFydGljbGVMaXN0Ow0KDQogICAgICAgICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkuZG9tYWluKSB7DQogICAgICAgICAgICAgIGFydGljbGVMaXN0ID0gcmVzLnJvd3MgfHwgW107DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBhcnRpY2xlTGlzdCA9IHJlcy5kYXRhLmxpc3QgfHwgW107DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWOu+mHjemAu+i+ke+8muWPquacieWcqOayoeacieWFs+mUruivjeaQnOe0ouaXtuaJjei/m+ihjOWOu+mHjQ0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAhdGhpcy5TZWFjaERhdGEua2V5d29yZCB8fA0KICAgICAgICAgICAgICB0aGlzLlNlYWNoRGF0YS5rZXl3b3JkLnRyaW0oKSA9PT0gIiINCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICBhcnRpY2xlTGlzdCA9IHRoaXMuZGVkdXBsaWNhdGVBcnRpY2xlcyhhcnRpY2xlTGlzdCk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QgPSBhcnRpY2xlTGlzdDsNCg0KICAgICAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsIHx8IDA7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5oGi5aSN6YCJ5Lit54q25oCB77yI6Z2Z6buY5oGi5aSN77yM5LiN6Kem5Y+R5Y+z5L6n5p+l6K+i77yJDQogICAgICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteS4uuepuueahOaDheWGtQ0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Lmxlbmd0aCA9PSAwICYmDQogICAgICAgICAgICAgIHRoaXMucGFnZVNpemUgKiAodGhpcy5jdXJyZW50UGFnZSAtIDEpID49IHRoaXMudG90YWwgJiYNCiAgICAgICAgICAgICAgdGhpcy50b3RhbCAhPSAwDQogICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IE1hdGgubWF4KA0KICAgICAgICAgICAgICAgIDEsDQogICAgICAgICAgICAgICAgTWF0aC5jZWlsKHRoaXMudG90YWwgLyB0aGlzLnBhZ2VTaXplKQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAvLyDph43mlrDmn6Xor6INCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgICAgICAgIHJldHVybjsgLy8g6YeN5paw5p+l6K+i5pe25LiN6KaB5YWz6ZetbG9hZGluZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgIuiOt+WPluaVsOaNruWksei0pSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmn6Xor6Lmlofnq6DliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIHRoaXMuaXNRdWVyeWluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gZmFsc2U7IC8vIOafpeivouWujOaIkOWQjuWFs+mXrWxvYWRpbmcNCiAgICAgICAgICB0aGlzLmJ1dHRvbkRpc2FibGVkID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0sIDEwMDApOw0KICAgIH0sDQoNCiAgICAvLyDmu5rliqjliLDpobbpg6gNCiAgICBzY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAvLyDlsJ3or5XlpJrnp43mu5rliqjmlrnlvI/noa7kv53mu5rliqjmiJDlip8NCiAgICAgICAgY29uc3Qgc2Nyb2xsQm94RWxlbWVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5zY29sbEJveCIpOw0KICAgICAgICBpZiAoc2Nyb2xsQm94RWxlbWVudCkgew0KICAgICAgICAgIHNjcm9sbEJveEVsZW1lbnQuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWmguaenE1haW5BcnRpY2xl57uE5Lu25pyJc2Nyb2xs5byV55So77yM5Lmf5bCd6K+V5rua5Yqo5a6DDQogICAgICAgIGlmICgNCiAgICAgICAgICB0aGlzLiRyZWZzLm1haW5BcnRpY2xlICYmDQogICAgICAgICAgdGhpcy4kcmVmcy5tYWluQXJ0aWNsZS4kcmVmcyAmJg0KICAgICAgICAgIHRoaXMuJHJlZnMubWFpbkFydGljbGUuJHJlZnMuc2Nyb2xsDQogICAgICAgICkgew0KICAgICAgICAgIHRoaXMuJHJlZnMubWFpbkFydGljbGUuJHJlZnMuc2Nyb2xsLnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmu5rliqjmlbTkuKrlj7PkvqfljLrln58NCiAgICAgICAgY29uc3QgcmlnaHRNYWluID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiLnJpZ2h0TWFpbiIpOw0KICAgICAgICBpZiAocmlnaHRNYWluKSB7DQogICAgICAgICAgcmlnaHRNYWluLnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgYXN5bmMgZ2V0QXJlYSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IFJlc3BvbnNlID0gYXdhaXQgYXBpLmdldEFyZWFMaXN0KCk7DQogICAgICAgIGlmIChSZXNwb25zZSAmJiBSZXNwb25zZS5jb2RlID09IDIwMCAmJiBSZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5hcmVhTGlzdCA9IFJlc3BvbnNlLmRhdGFbMF0gfHwgW107DQogICAgICAgICAgdGhpcy5jb3VudHJ5TGlzdCA9IFJlc3BvbnNlLmRhdGFbMV0gfHwgW107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCLojrflj5blnLDljLrmlbDmja7lpLHotKXmiJbmlbDmja7kuLrnqboiKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWMuuWfn+aVsOaNruWksei0pToiLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlnLDljLrmlbDmja7ojrflj5blpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0QXJ0aWNsZUhpc3RvcnkoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBsaXN0QXJ0aWNsZUhpc3Rvcnkoew0KICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgcGFnZVNpemU6IDUsDQogICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgfSk7DQogICAgICAgIGlmIChyZXMgJiYgcmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuaGlzdG9yeUxpc3QgPSByZXMucm93cyB8fCBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5Y6G5Y+y6K6w5b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qCR6IqC54K56L+H5ruk5pa55rOVDQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgew0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7DQogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoEVzU2VhY2jmlrnms5Xku6Xlhbzlrrnljp/mnInosIPnlKgNCiAgICBFc1NlYWNoKGZsYWcpIHsNCiAgICAgIGlmIChmbGFnID09PSAiZmlsdGVyIikgew0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgLy8g562b6YCJ5Y+Y5YyW5pe25ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGoDQogICAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5YW25LuW5oOF5Ya15Y+q5p+l6K+i5YiX6KGoDQogICAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6YeN572u5pCc57Si5p2h5Lu2IC0g566A5YyW54mI5pysDQogICAgcmVzZXR0aW5nKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5TZWFjaERhdGEgPSB7DQogICAgICAgICAgbWV0YU1vZGU6ICIiIC8qIOWMuemFjeaooeW8jyAqLywNCiAgICAgICAgICBrZXl3b3JkOiAiIiAvKiDlhbPplK7or40gKi8sDQogICAgICAgICAgdGltZVJhbmdlOiAiIiAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgICAgY3VzdG9tRGF5OiBbXSAvKiDoh6rlrprkuYnlpKkgKi8sDQogICAgICAgICAgY29sbGVjdGlvbkRhdGVUeXBlOiBudWxsIC8qIOaXtumXtOiMg+WbtCAqLywNCiAgICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICAgIGlzVGVjaG5vbG9neTogIjEiLA0KICAgICAgICAgIHNvcnRNb2RlOiAiNCIsDQogICAgICAgICAgZW1vdGlvbjogIjAiLA0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLph43nva7mkJzntKLmnaHku7bml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLph43nva7mkJzntKLmnaHku7blpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgcmVtb3ZlSGlzdG9yeShpdGVtLCB0eXBlKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAodGhpcy5oaXN0b3J5VGltZW91dCkgew0KICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChpdGVtICYmIGl0ZW0uaWQpIHsNCiAgICAgICAgICBhd2FpdCBkZWxBcnRpY2xlSGlzdG9yeShbaXRlbS5pZF0pOw0KDQogICAgICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICAgICAgaWYgKHRoaXMuJHJlZnNbImtleXdvcmRSZWYiXSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0uZm9jdXMoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYXdhaXQgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgICAgICAgYXdhaXQgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeTEoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIoOmZpOWOhuWPsuiusOW9leaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIoOmZpOWOhuWPsuiusOW9leWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBzaG93SGlzdG9yeUxpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gdHJ1ZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuaYvuekuuWOhuWPsuWIl+ihqOaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhpZGVIaXN0b3J5TGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICh0aGlzLmhpc3RvcnlUaW1lb3V0KSB7DQogICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuaGlzdG9yeVRpbWVvdXQpOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5oaXN0b3J5VGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuc2hvd0hpc3RvcnkgPSBmYWxzZTsNCiAgICAgICAgfSwgNTAwKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIumakOiXj+WOhuWPsuWIl+ihqOaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuc2hvd0hpc3RvcnkgPSBmYWxzZTsgLy8g56Gu5L+d5Zyo5Ye66ZSZ5pe25Lmf6IO96ZqQ6JeP5YiX6KGoDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWFs+mUruivjeWOhuWPsumAieaLqSAtIOebtOaOpeS7jldlY2hhdC52dWXlpI3liLYNCiAgICBrZXl3b3Jkc0NoYW5nZShpdGVtKSB7DQogICAgICB0aGlzLlNlYWNoRGF0YS5rZXl3b3JkID0gaXRlbS5rZXl3b3JkOw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IGZhbHNlOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIC8vIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIGFzeW5jIGNsZWFySGlzdG9yeSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICh0aGlzLmhpc3RvcnlUaW1lb3V0KSB7DQogICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuaGlzdG9yeVRpbWVvdXQpOw0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuJHJlZnNbImtleXdvcmRSZWYiXSkgew0KICAgICAgICAgIHRoaXMuJHJlZnNbImtleXdvcmRSZWYiXS5mb2N1cygpOw0KICAgICAgICB9DQoNCiAgICAgICAgYXdhaXQgY2xlYW5BcnRpY2xlSGlzdG9yeSgyKTsNCiAgICAgICAgYXdhaXQgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5riF6Zmk5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5riF6Zmk5Y6G5Y+y6K6w5b2V5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIG1vcmVIaXN0b3J5KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IHRydWU7DQogICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSB0cnVlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L295pu05aSa5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRBcnRpY2xlSGlzdG9yeTEoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsaXN0QXJ0aWNsZUhpc3Rvcnkoew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMxLA0KICAgICAgICAgIHR5cGU6IDIsDQogICAgICAgIH0pOw0KDQogICAgICAgIGlmIChyZXNwb25zZSkgew0KICAgICAgICAgIHRoaXMuaGlzdG9yeUxpc3QxID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICB0aGlzLnRvdGFsMSA9IHJlc3BvbnNlLnRvdGFsIHx8IDA7DQogICAgICAgIH0NCg0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bmlofnq6Dljoblj7LorrDlvZXml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluaQnOe0ouWOhuWPsuWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBvcGVuVXJsKHVybCkgew0KICAgICAgd2luZG93Lm9wZW4odXJsLCAiX2JsYW5rIik7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhui/h+a7pOaQnOe0ou+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJDQogICAgaGFuZGxlRmlsdGVyU2VhcmNoKGtleXdvcmQpIHsNCiAgICAgIGlmICh0aGlzLmlzTGVmdFJlc2V0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5LiN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAgIC8vIOabtOaWsOafpeivouWPguaVsOS4reeahCBmaWx0ZXJ3b3Jkcw0KICAgICAgdGhpcy50cmVlUXVlcnkuZmlsdGVyd29yZHMgPSBrZXl3b3JkIHx8ICIiOw0KDQogICAgICAvLyDph43nva7liLDnrKzkuIDpobUNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMuU2VhY2hEYXRhLmhhc0NhY2hlID0gIjEiOw0KDQogICAgICAvLyDosIPnlKjmoJHmlbDmja7mn6Xor6LmjqXlj6PlubbmgaLlpI3pgInkuK3nirbmgIHvvIjkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIkNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaVsOaNrua6kOWIhuexu+WPmOWMlu+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJDQogICAgaGFuZGxlQ2xhc3NpZnlDaGFuZ2UoY2xhc3NpZnlWYWx1ZSkgew0KICAgICAgLy8g5LiN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAgIC8vIOabtOaWsOmAieS4reeahOWIhuexuw0KICAgICAgdGhpcy5zZWxlY3RlZENsYXNzaWZ5ID0gY2xhc3NpZnlWYWx1ZTsNCg0KICAgICAgLy8g6YeN572u5Yiw56ys5LiA6aG1DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCg0KICAgICAgLy8g5Y+q6LCD55So5qCR5pWw5o2u5p+l6K+i5o6l5Y+j5bm25oGi5aSN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblm73lrrbnrZvpgInlj5jljJbvvIjmnaXoh6ogVHJlZVRhYmxlIOe7hOS7tu+8iQ0KICAgIGhhbmRsZUNvdW50cnlDaGFuZ2UoY291bnRyeVZhbHVlKSB7DQogICAgICAvLyDkuI3kv53lrZjlvZPliY3pgInkuK3nirbmgIHvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4NCg0KICAgICAgLy8g5pu05paw6YCJ5Lit55qE5Zu95a62DQogICAgICB0aGlzLnNlbGVjdGVkQ291bnRyeSA9IGNvdW50cnlWYWx1ZTsNCg0KICAgICAgLy8g6YeN572u5Yiw56ys5LiA6aG1DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCg0KICAgICAgLy8g5Y+q6LCD55So5qCR5pWw5o2u5p+l6K+i5o6l5Y+j5bm25oGi5aSN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmoJHlvaLliIbpobXpobXnoIHlj5jljJYNCiAgICBoYW5kbGVUcmVlQ3VycmVudENoYW5nZShwYWdlKSB7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IHBhZ2U7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuagkeW9ouWIhumhteavj+mhteWkp+Wwj+WPmOWMlg0KICAgIGhhbmRsZVRyZWVQYWdlU2l6ZUNoYW5nZShzaXplKSB7DQogICAgICB0aGlzLnRyZWVQYWdlU2l6ZSA9IHNpemU7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOe8uuWkseeahG9wZW5OZXdWaWV35pa55rOVDQogICAgb3Blbk5ld1ZpZXcoaXRlbSkgew0KICAgICAgd2luZG93Lm9wZW4oDQogICAgICAgIGAvZXhwcmVzc0RldGFpbHM/aWQ9JHtpdGVtLmlkfSZkb2NJZD0ke2l0ZW0uZG9jSWR9JnNvdXJjZVR5cGU9JHtpdGVtLnNvdXJjZVR5cGV9YCwNCiAgICAgICAgIl9ibGFuayINCiAgICAgICk7DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOe8uuWkseeahGhhbmRsZUhpc3RvcnlQYWdpbmF0aW9u5pa55rOVDQogICAgaGFuZGxlSGlzdG9yeVBhZ2luYXRpb24oKSB7DQogICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5MSgpOw0KICAgIH0sDQoNCiAgICAvLyDmlofnq6Dljrvph43mlrnms5UNCiAgICBkZWR1cGxpY2F0ZUFydGljbGVzKGFydGljbGVzKSB7DQogICAgICBpZiAoIWFydGljbGVzIHx8IGFydGljbGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gYXJ0aWNsZXM7DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHRpdGxlTWFwID0gbmV3IE1hcCgpOw0KICAgICAgY29uc3QgcmVzdWx0ID0gW107DQoNCiAgICAgIC8vIOe7n+iuoeebuOWQjOagh+mimOeahOaWh+eroOaVsOmHjw0KICAgICAgYXJ0aWNsZXMuZm9yRWFjaCgoYXJ0aWNsZSkgPT4gew0KICAgICAgICAvLyDljrvpmaRIVE1M5qCH562+5ZKM5omA5pyJ56m65qC85p2l5q+U6L6D5qCH6aKYDQogICAgICAgIGNvbnN0IGNsZWFuVGl0bGUgPSBhcnRpY2xlLnRpdGxlDQogICAgICAgICAgPyBhcnRpY2xlLnRpdGxlLnJlcGxhY2UoLzxbXj5dKj4vZywgIiIpLnJlcGxhY2UoL1xzKy9nLCAiIikNCiAgICAgICAgICA6ICIiOw0KDQogICAgICAgIGlmICh0aXRsZU1hcC5oYXMoY2xlYW5UaXRsZSkpIHsNCiAgICAgICAgICB0aXRsZU1hcC5nZXQoY2xlYW5UaXRsZSkuY291bnQrKzsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aXRsZU1hcC5zZXQoY2xlYW5UaXRsZSwgew0KICAgICAgICAgICAgYXJ0aWNsZTogeyAuLi5hcnRpY2xlIH0sDQogICAgICAgICAgICBjb3VudDogMSwNCiAgICAgICAgICAgIG9yaWdpbmFsVGl0bGU6IGFydGljbGUudGl0bGUsIC8vIOS/neWtmOWOn+Wni+agh+mimO+8iOWPr+iDveWMheWQq0hUTUzmoIfnrb7vvIkNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOeUn+aIkOWOu+mHjeWQjueahOaWh+eroOWIl+ihqA0KICAgICAgdGl0bGVNYXAuZm9yRWFjaCgoeyBhcnRpY2xlLCBjb3VudCwgb3JpZ2luYWxUaXRsZSB9KSA9PiB7DQogICAgICAgIGlmIChjb3VudCA+IDEpIHsNCiAgICAgICAgICAvLyDlpoLmnpzmnInph43lpI3vvIzlnKjmoIfpopjlkI7pnaLliqDkuIrmlbDph4/moIforrANCiAgICAgICAgICAvLyDkvb/nlKjljp/lp4vmoIfpopjvvIjkv53mjIFIVE1M5qC85byP77yJDQogICAgICAgICAgYXJ0aWNsZS50aXRsZSA9IGAke29yaWdpbmFsVGl0bGUgfHwgIiJ977yIJHtjb3VudH3vvIlgOw0KICAgICAgICB9DQogICAgICAgIHJlc3VsdC5wdXNoKGFydGljbGUpOw0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiByZXN1bHQ7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["keMonitor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "keMonitor.vue", "sourceRoot": "src/views/KeMonitor", "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}