{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753945041459}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXBpIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHRvcFNlYWNoIGZyb20gIkAvdmlld3MvY29tcG9uZW50cy90b3BTZWFjaC52dWUiOw0KaW1wb3J0IE1haW5BcnRpY2xlIGZyb20gIi4uL2NvbXBvbmVudHMvTWFpbkFydGljbGUudnVlIjsNCmltcG9ydCB7DQogIGxpc3RBcnRpY2xlSGlzdG9yeSwNCiAgZGVsQXJ0aWNsZUhpc3RvcnksDQogIGFkZEFydGljbGVIaXN0b3J5LA0KICBjbGVhbkFydGljbGVIaXN0b3J5LA0KfSBmcm9tICJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5IjsNCmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsNCmltcG9ydCAic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIjsNCmltcG9ydCBUcmVlVGFibGUgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVUYWJsZS9pbmRleC52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgdG9wU2VhY2gsIE1haW5BcnRpY2xlLCBTcGxpdHBhbmVzLCBQYW5lLCBUcmVlVGFibGUgfSwNCiAgZGljdHM6IFsiaXNfdGVjaG5vbG9neSJdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB3aWR0aDogIjI1OCIsDQogICAgICBpc1JlU2l6ZTogZmFsc2UsDQogICAgICAvKiDmlofnq6DkuLvkvZPnu4Tku7bmlbDmja4gKi8NCiAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgcGFnZVNpemU6IDUwLA0KICAgICAgdG90YWw6IDAsDQogICAgICBBcnRpY2xlTGlzdDogW10sDQogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwNCiAgICAgIGNoZWNrTGlzdDogW10sDQogICAgICAvKiDmoJHlvaLliIbpobXmlbDmja4gKi8NCiAgICAgIHRyZWVDdXJyZW50UGFnZTogMSwNCiAgICAgIHRyZWVQYWdlU2l6ZTogMTAwLA0KICAgICAgdHJlZVRvdGFsOiAwLA0KDQogICAgICAvKiDmkJzntKLnu4Tku7bmlbDmja4gKi8NCiAgICAgIFNlYWNoRGF0YTogew0KICAgICAgICBtZXRhTW9kZTogIiIgLyog5Yy56YWN5qih5byPICovLA0KICAgICAgICBrZXl3b3JkOiAiIiAvKiDlhbPplK7or40gKi8sDQogICAgICAgIHRpbWVSYW5nZTogNCAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgIGN1c3RvbURheTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBjb2xsZWN0aW9uRGF0ZVR5cGU6IG51bGwgLyog5pe26Ze06IyD5Zu0ICovLA0KICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBpc1RlY2hub2xvZ3k6ICIxIiwNCiAgICAgICAgc29ydE1vZGU6ICI0IiwNCiAgICAgICAgZW1vdGlvbjogIjAiLA0KICAgICAgICBoYXNDYWNoZTogIjAiLA0KICAgICAgfSAvKiDmkJzntKLmnaHku7YgKi8sDQogICAgICAvKiDmjpLluo/mqKHlvI8gLSDljZXni6zmj5Dlj5bvvIzpgb/lhY3op6blj5FTZWFjaERhdGHnmoTmt7Hluqbnm5HlkKwgKi8NCiAgICAgIGJ1dHRvbkRpc2FibGVkOiBmYWxzZSAvKiDmjInpkq7pmLLmipYgKi8sDQogICAgICBBY3RpdmVEYXRhOiB7fSwNCiAgICAgIHNlbmlvclNlcmNoRmxhZzogZmFsc2UgLyog5pmu6YCa5qOA57Si5oiW6auY57qn5qOA57SiICovLA0KICAgICAgYXJlYUxpc3Q6IFtdIC8qIOWbveWGheWcsOWMuiAqLywNCiAgICAgIGNvdW50cnlMaXN0OiBbXSAvKiDlm73lrrbmiJblnLDljLogKi8sDQogICAgICBLZUxpc3Q6IFtdLA0KICAgICAgZnVuRXNTZWFjaDogbnVsbCwNCiAgICAgIHRyZWVRdWVyeTogew0KICAgICAgICBmaWx0ZXJ3b3JkczogIiIsIC8vIOa3u+WKoOagkeaQnOe0ouWFs+mUruWtlw0KICAgICAgfSwNCiAgICAgIGRvbWFpbkxpc3Q6IFtdLA0KICAgICAgaW5kdXN0cnlMaXN0OiBbXSwNCiAgICAgIHNob3dIaXN0b3J5OiBmYWxzZSwNCiAgICAgIGhpc3RvcnlMaXN0OiBbXSwNCiAgICAgIGhpc3RvcnlUaW1lb3V0OiBudWxsLA0KICAgICAgZGlhbG9nVmlzaWJsZTE6IGZhbHNlLA0KICAgICAgaGlzdG9yeUxvYWRpbmc6IGZhbHNlLA0KICAgICAgcXVlcnlQYXJhbXMxOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0sDQogICAgICB0b3RhbDE6IDAsDQogICAgICBoaXN0b3J5TGlzdDE6IFtdLA0KICAgICAgaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQ6IGZhbHNlLCAvLyDmoIforrDliJ3lp4vljJbmmK/lkKblrozmiJANCiAgICAgIC8vIOS7jldlY2hhdC52dWXlkIzmraXnmoTlsZ7mgKcNCiAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDmoJHnu4Tku7Zsb2FkaW5n54q25oCBDQogICAgICB0YWJsZUxvYWRpbmc6IGZhbHNlLCAvLyDooajmoLxsb2FkaW5n54q25oCBDQogICAgICBpc1F1ZXJ5aW5nOiBmYWxzZSwgLy8g5p+l6K+i6Ziy5oqWDQogICAgICBxdWVyeURlYm91bmNlVGltZXI6IG51bGwsIC8vIOafpeivoumYsuaKluWumuaXtuWZqA0KICAgICAgaXNSaWdodEZpbHRlcjogZmFsc2UsIC8vIOagh+iusOWPs+S+p+etm+mAieadoeS7tuaYr+WQpuWPkeeUn+WPmOWMlg0KICAgICAgaXNMZWZ0UmVzZXQ6IGZhbHNlLCAvLyDmoIforrDlt6bkvqfmoJHmmK/lkKbph43nva4NCiAgICAgIHNlbGVjdGVkQ2xhc3NpZnk6ICI1IiwgLy8g6YCJ5Lit55qE5pWw5o2u5rqQ5YiG57G7DQogICAgICBzZWxlY3RlZENvdW50cnk6IG51bGwsIC8vIOmAieS4reeahOWbveWutg0KICAgICAgLyog5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yI5rC45LmF5L+d5a2Y77yM5Y+q5pyJ54m55a6a5pON5L2c5omN5pu05paw77yJICovDQogICAgICBzYXZlZENoZWNrYm94RGF0YTogW10sDQogICAgICBnbG9iYWxMb2FkaW5nOiBmYWxzZSwNCiAgICB9Ow0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRyeSB7DQogICAgICAvLyDliJ3lp4vljJZmdW5Fc1NlYWNo5pa55rOVDQogICAgICB0aGlzLmZ1bkVzU2VhY2ggPSB0aGlzLkVzU2VhY2g7DQoNCiAgICAgIC8vIOWFiOWKoOi9veWfuuehgOaVsOaNrg0KICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KDQogICAgICAvLyBpZiAodGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgJiYgdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUgPT09ICI4Iikgew0KICAgICAgLy8gICB0aGlzLlNlYWNoRGF0YS50aW1lUmFuZ2UgPSA3Ow0KICAgICAgLy8gfQ0KDQogICAgICAvLyDliqDovb3moJHmlbDmja7lkozlhoXlrrnmlbDmja4NCiAgICAgIGF3YWl0IHRoaXMuaW5pdGlhbGl6ZURhdGEoKTsNCg0KICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5a6M5oiQ77yM6L+Z5qC3d2F0Y2jnm5HlkKzlmajmiY3kvJrlvIDlp4vlt6XkvZwNCiAgICAgIHRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgPSB0cnVlOw0KICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICBjb25zb2xlLmVycm9yKCLnu4Tku7bliJ3lp4vljJblpLHotKU6IiwgZXJyb3IpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7DQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgLy8gVHJlZVRhYmxlIOe7hOS7tuS4jemcgOimgeeJueauiueahOeKtuaAgeajgOafpQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOetm+mAieadoeS7tuWPmOWMlg0KICAgICJTZWFjaERhdGEudGltZVJhbmdlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgIH0sDQogICAgfSwNCiAgICAiU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgIlNlYWNoRGF0YS5pc1RlY2hub2xvZ3kiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZUZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgICJTZWFjaERhdGEuZW1vdGlvbiI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliJ3lp4vljJbmlbDmja4NCiAgICBhc3luYyBpbml0aWFsaXplRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IHRydWU7DQogICAgICAgIC8vIOWFiOWKoOi9veagkeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLnF1ZXJ5VHJlZURhdGEoKTsNCiAgICAgICAgLy8g562J5b6F5qCR57uE5Lu25a6M5YWo5riy5p+TDQogICAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCk7DQoNCiAgICAgICAgLy8g6buY6K6k5YWo6YCJ56ys5LiA6aG15pWw5o2u5rqQDQogICAgICAgIGlmICh0aGlzLnRyZWVEYXRhVHJhbnNmZXIgJiYgdGhpcy50cmVlRGF0YVRyYW5zZmVyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDlhajpgInnrKzkuIDpobXnmoTmiYDmnInmlbDmja7mupANCiAgICAgICAgICBjb25zdCBmaXJzdFBhZ2VEYXRhID0gWy4uLnRoaXMudHJlZURhdGFUcmFuc2Zlcl07DQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBmaXJzdFBhZ2VEYXRhOw0KICAgICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBmaXJzdFBhZ2VEYXRhOw0KDQogICAgICAgICAgLy8g6YCa55+lIFRyZWVUYWJsZSDnu4Tku7borr7nva7pgInkuK3nirbmgIENCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICBpZiAodGhpcy4kcmVmcy50cmVlVGFibGUpIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlVGFibGUucmVzdG9yZVNlbGVjdGlvblNpbGVudGx5KGZpcnN0UGFnZURhdGEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgLy8g5bu26L+f5LiA5LiL5YaN5p+l6K+i5paH56ug5YiX6KGo77yM56Gu5L+d6YCJ5Lit54q25oCB5bey6K6+572uDQogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgICAgICB9LCAxMDApOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWmguaenOayoeacieaVsOaNrua6kO+8jOebtOaOpeafpeivouaWh+eroOWIl+ihqA0KICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliJ3lp4vljJbmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliJ3lp4vljJblpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UiKTsNCiAgICAgICAgdGhpcy5nbG9iYWxMb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIFRyZWVUYWJsZSDnu4Tku7bkuovku7blpITnkIbmlrnms5UNCg0KICAgIC8vIOWkhOeQhumAieaLqeWPmOWMlg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3RlZERhdGEsIG9wZXJhdGlvblR5cGUpIHsNCiAgICAgIGlmIChvcGVyYXRpb25UeXBlID09PSAicm93LWNsaWNrIiB8fCBvcGVyYXRpb25UeXBlID09PSAiY2xlYXItYWxsIikgew0KICAgICAgICAvLyDngrnlh7vooYzvvIjljZXpgInvvInmiJblj5bmtojmiYDmnInpgInkuK3vvJrnm7TmjqXmm7/mjaLvvIzkuI3pnIDopoHov73liqDljrvph40NCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgfSBlbHNlIGlmICgNCiAgICAgICAgb3BlcmF0aW9uVHlwZSA9PT0gImNoZWNrYm94LWNoYW5nZSIgfHwNCiAgICAgICAgb3BlcmF0aW9uVHlwZSA9PT0gInNlbGVjdC1hbGwiDQogICAgICApIHsNCiAgICAgICAgLy8g54K55Ye75Yu+6YCJ5qGG77yI5aSa6YCJ77yJ5oiW5YWo6YCJ77ya6ZyA6KaB5q2j56Gu5aSE55CG6YCJ5Lit5ZKM5Y+W5raI6YCJ5LitDQogICAgICAgIC8vIOWFiOS7juS/neWtmOeahOaVsOaNruS4reenu+mZpOW9k+WJjemhtemdoueahOaJgOacieaVsOaNrg0KICAgICAgICBjb25zdCBjdXJyZW50UGFnZUlkcyA9IHRoaXMudHJlZURhdGFUcmFuc2Zlci5tYXAoDQogICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uc291cmNlU24NCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgZmlsdGVyZWRDaGVja0xpc3QgPSB0aGlzLmNoZWNrTGlzdC5maWx0ZXIoDQogICAgICAgICAgKGl0ZW0pID0+ICFjdXJyZW50UGFnZUlkcy5pbmNsdWRlcyhpdGVtLnNvdXJjZVNuKQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCBmaWx0ZXJlZFNhdmVkRGF0YSA9IHRoaXMuc2F2ZWRDaGVja2JveERhdGEuZmlsdGVyKA0KICAgICAgICAgIChpdGVtKSA9PiAhY3VycmVudFBhZ2VJZHMuaW5jbHVkZXMoaXRlbS5zb3VyY2VTbikNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDnhLblkI7mt7vliqDlvZPliY3pobXpnaLmlrDpgInkuK3nmoTmlbDmja4NCiAgICAgICAgY29uc3QgY29tYmluZWRDaGVja0xpc3QgPSBbLi4uZmlsdGVyZWRDaGVja0xpc3QsIC4uLnNlbGVjdGVkRGF0YV07DQogICAgICAgIGNvbnN0IGNvbWJpbmVkU2F2ZWREYXRhID0gWy4uLmZpbHRlcmVkU2F2ZWREYXRhLCAuLi5zZWxlY3RlZERhdGFdOw0KDQogICAgICAgIC8vIOWvueWQiOW5tuWQjueahOaVsOaNrui/m+ihjOWOu+mHjeWkhOeQhg0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IHRoaXMuZGVkdXBsaWNhdGVCeVNvdXJjZVNuKGNvbWJpbmVkQ2hlY2tMaXN0KTsNCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IHRoaXMuZGVkdXBsaWNhdGVCeVNvdXJjZVNuKGNvbWJpbmVkU2F2ZWREYXRhKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOm7mOiupOaDheWGte+8muebtOaOpeabv+aNou+8iOWFvOWuueaAp+WkhOeQhu+8iQ0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gWy4uLnNlbGVjdGVkRGF0YV07DQogICAgICB9DQoNCiAgICAgIC8vIOmHjee9rumhteeggeW5tuafpeivouWGheWuuQ0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIGlmICghdGhpcy5pc1JpZ2h0RmlsdGVyKSB7DQogICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgic291cmNlSXRlbUNoYW5nZWQiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qC55o2uc291cmNlU27ljrvph43nmoTovoXliqnmlrnms5UNCiAgICBkZWR1cGxpY2F0ZUJ5U291cmNlU24oZGF0YUFycmF5KSB7DQogICAgICBjb25zdCBzZWVuID0gbmV3IFNldCgpOw0KICAgICAgcmV0dXJuIGRhdGFBcnJheS5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKHNlZW4uaGFzKGl0ZW0uc291cmNlU24pKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICAgIHNlZW4uYWRkKGl0ZW0uc291cmNlU24pOw0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbph43nva4NCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIC8vIOWFiOa4heepuui/h+a7pOWFs+mUruWtl++8jOmBv+WFjeinpuWPkSBoYW5kbGVGaWx0ZXJTZWFyY2gNCiAgICAgIHRoaXMudHJlZVF1ZXJ5LmZpbHRlcndvcmRzID0gIiI7DQogICAgICB0aGlzLnNlbGVjdGVkQ2xhc3NpZnkgPSBudWxsOw0KICAgICAgdGhpcy5zZWxlY3RlZENvdW50cnkgPSBudWxsOw0KDQogICAgICAvLyDnhLblkI7orr7nva7ph43nva7moIforrANCiAgICAgIHRoaXMuaXNMZWZ0UmVzZXQgPSB0cnVlOw0KDQogICAgICAvLyDmuIXnqbrpgInkuK3nirbmgIENCiAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQoNCiAgICAgIC8vIOa4heepuuS/neWtmOeahOWLvumAieaVsOaNru+8iOawuOS5heS/neWtmO+8iQ0KICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IFtdOw0KDQogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LliJfooajmlbDmja4NCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCg0KICAgICAgLy8g6YeN5paw5p+l6K+i5qCR5ZKM5YiX6KGoDQogICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG562b6YCJ5p2h5Lu25Y+Y5YyWIC0g5p2l6Ieq5Y+z5L6n562b6YCJ5p2h5Lu255qE5Y+Y5YyWDQogICAgaGFuZGxlRmlsdGVyQ2hhbmdlKCkgew0KICAgICAgdGhpcy5pc1JpZ2h0RmlsdGVyID0gdHJ1ZTsgLy8g5qCH6K6w5Y+z5L6n562b6YCJ5p2h5Lu25Y+R55Sf5Y+Y5YyWDQoNCiAgICAgIC8vIOS4jeWGjeS/neWtmOW9k+WJjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KICAgICAgLy8g5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5Lya5Zyo5p+l6K+i5ZCO6Ieq5Yqo5oGi5aSNDQoNCiAgICAgIC8vIOmHjee9ruWIhumhteWIsOesrOS4gOmhtQ0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIwIjsNCg0KICAgICAgLy8g5rua5Yqo5Yiw6aG26YOoDQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCg0KICAgICAgLy8g5ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGoDQogICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICB9LA0KDQogICAgLy8g5ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGoIC0g55u05o6l5LuOV2VjaGF0LnZ1ZeWkjeWItg0KICAgIGFzeW5jIHF1ZXJ5VHJlZUFuZExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDkv53lrZjlvZPliY3nmoTmsLjkuYXli77pgInmlbDmja7vvIzpgb/lhY3lnKjmn6Xor6Lov4fnqIvkuK3kuKLlpLENCiAgICAgICAgY29uc3Qgc2F2ZWREYXRhID0gWy4uLnRoaXMuc2F2ZWRDaGVja2JveERhdGFdOw0KDQogICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOWFiOS4tOaXtuaBouWkjSBjaGVja0xpc3Qg5Lul5L6/5p+l6K+i5pe25bim5LiK5Y+C5pWwDQogICAgICAgIGlmIChzYXZlZERhdGEgJiYgc2F2ZWREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi5zYXZlZERhdGFdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWmguaenOayoeacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOa4heepuumAieS4reeKtuaAgQ0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlkIzml7bmn6Xor6LmoJHmlbDmja7lkozlj7PkvqfliJfooajvvIjkv53mjIHmgKfog73kvJjlir/vvIkNCiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAgIHRoaXMucXVlcnlUcmVlRGF0YSgpLA0KICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpLCAvLyBxdWVyeUFydGljbGVMaXN0IOWGhemDqOW3sue7j+WkhOeQhuS6hiB0YWJsZUxvYWRpbmcNCiAgICAgICAgXSk7DQoNCiAgICAgICAgLy8g56Gu5L+d5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5LiN5Lya5Lii5aSxDQogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBzYXZlZERhdGE7DQoNCiAgICAgICAgLy8g5p+l6K+i5a6M5oiQ5ZCO77yM5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM6Z2Z6buY5oGi5aSN55WM6Z2i6YCJ5Lit54q25oCBDQogICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMucmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5p+l6K+i5a6M5oiQ5ZCO6YeN572u5Y+z5L6n562b6YCJ5qCH6K6wDQogICAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IGZhbHNlOw0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmlzTGVmdFJlc2V0ID0gZmFsc2U7DQogICAgICAgIH0sIDMwMCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLlkIzml7bmn6Xor6LmoJHlkozliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmn6Xor6LlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgLy8g5Y2z5L2/5Ye66ZSZ5Lmf6KaB6YeN572u5qCH6K6wDQogICAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IGZhbHNlOw0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmlzTGVmdFJlc2V0ID0gZmFsc2U7DQogICAgICAgIH0sIDMwMCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaBouWkjemAieS4reaVsOaNrua6kOeahOaWueazleW3suWIoOmZpO+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KDQogICAgLy8g5LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB77yI5LuF5aSE55CG55WM6Z2i6YCJ5Lit54q25oCB77yJDQogICAgcmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy5zYXZlZENoZWNrYm94RGF0YSB8fCB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWcqOW9k+WJjeagkeaVsOaNruS4reafpeaJvuWMuemFjeeahOmhuQ0KICAgICAgY29uc3QgbWF0Y2hlZEl0ZW1zID0gW107DQogICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmZvckVhY2goKHNhdmVkSXRlbSkgPT4gew0KICAgICAgICBjb25zdCBmb3VuZEl0ZW0gPSB0aGlzLnRyZWVEYXRhVHJhbnNmZXIuZmluZCgNCiAgICAgICAgICAodHJlZUl0ZW0pID0+IHRyZWVJdGVtLnNvdXJjZVNuID09PSBzYXZlZEl0ZW0uc291cmNlU24NCiAgICAgICAgKTsNCiAgICAgICAgaWYgKGZvdW5kSXRlbSkgew0KICAgICAgICAgIG1hdGNoZWRJdGVtcy5wdXNoKGZvdW5kSXRlbSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBpZiAobWF0Y2hlZEl0ZW1zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5pu05paw6YCJ5Lit5YiX6KGo77yI5q2k5pe2IGNoZWNrTGlzdCDlt7Lnu4/lnKjmn6Xor6LliY3mgaLlpI3ov4fkuobvvIkNCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBtYXRjaGVkSXRlbXM7DQogICAgICAgIC8vIOmAmuefpSBUcmVlVGFibGUg57uE5Lu25oGi5aSN55WM6Z2i6YCJ5Lit54q25oCB77yI5LiN6Kem5Y+R5LqL5Lu277yJDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBpZiAodGhpcy4kcmVmcy50cmVlVGFibGUpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVRhYmxlLnJlc3RvcmVTZWxlY3Rpb25TaWxlbnRseShtYXRjaGVkSXRlbXMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInljLnphY3pobnvvIzmuIXnqbrpgInkuK3nirbmgIENCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB55qE5pa55rOV5bey5Yig6Zmk77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAvLyDmn6Xor6LmoJHmlbDmja7lubbku47msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7mgaLlpI3pgInkuK3nirbmgIHvvIjnlKjkuo7lhbPplK7lrZfov4fmu6TvvIkNCiAgICBhc3luYyBxdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzlhYjkuLTml7bmgaLlpI0gY2hlY2tMaXN0IOS7peS+v+afpeivouaXtuW4puS4iuWPguaVsA0KICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi50aGlzLnNhdmVkQ2hlY2tib3hEYXRhXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5p+l6K+i5qCR5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMucXVlcnlUcmVlRGF0YSgpOw0KDQogICAgICAgIC8vIOafpeivouWujOaIkOWQju+8jOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOmdmem7mOaBouWkjeeVjOmdoumAieS4reeKtuaAgQ0KICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnJlc3RvcmVGcm9tU2F2ZWRDaGVja2JveERhdGEoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigNCiAgICAgICAgICAi5p+l6K+i5qCR5pWw5o2u5bm25LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB5aSx6LSlOiIsDQogICAgICAgICAgZXJyb3INCiAgICAgICAgKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiG6aG15aSE55CGDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShjdXJyZW50KSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gY3VycmVudDsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIGhhbmRsZVNpemVDaGFuZ2Uoc2l6ZSkgew0KICAgICAgdGhpcy5wYWdlU2l6ZSA9IHNpemU7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOafpeivouagkeaVsOaNrg0KICAgIGFzeW5jIHF1ZXJ5VHJlZURhdGEoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHBhZ2VOdW06IHRoaXMudHJlZUN1cnJlbnRQYWdlLA0KICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnRyZWVQYWdlU2l6ZSwNCiAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDEsDQogICAgICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlID8gIjEiIDogdGhpcy4kcm91dGUucXVlcnkuaWQsDQogICAgICAgICAgbTogMSwNCiAgICAgICAgICBkYXRlVHlwZToNCiAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLnRpbWVSYW5nZSAhPSA2ID8gdGhpcy5TZWFjaERhdGEudGltZVJhbmdlIDogIiIsDQogICAgICAgICAgc3RhcnRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXkNCiAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzBdDQogICAgICAgICAgICA6ICIiLA0KICAgICAgICAgIGVuZFRpbWU6IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheSA/IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheVsxXSA6ICIiLA0KICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZToNCiAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSAhPSA2DQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgY29sbGVjdGlvblN0YXJ0VGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMF0NCiAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgY29sbGVjdGlvbkVuZFRpbWU6IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lDQogICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lWzFdDQogICAgICAgICAgICA6ICIiLA0KICAgICAgICAgIGtleXdvcmRzOiB0aGlzLlNlYWNoRGF0YS5rZXl3b3JkLA0KICAgICAgICAgIGlzVGVjaG5vbG9neTogdGhpcy5TZWFjaERhdGEuaXNUZWNobm9sb2d5LA0KICAgICAgICAgIGVtb3Rpb246IHRoaXMuU2VhY2hEYXRhLmVtb3Rpb24sDQogICAgICAgICAgLy8g5re75Yqg5YWz6ZSu5a2X6L+H5ruk5Y+C5pWwDQogICAgICAgICAgZmlsdGVyd29yZHM6IHRoaXMudHJlZVF1ZXJ5LmZpbHRlcndvcmRzIHx8ICIiLA0KICAgICAgICAgIC8vIOa3u+WKoOaVsOaNrua6kOWIhuexu+WPguaVsA0KICAgICAgICAgIHRoaW5rVGFua0NsYXNzaWZpY2F0aW9uOiB0aGlzLnNlbGVjdGVkQ2xhc3NpZnksDQogICAgICAgICAgLy8g5re75Yqg5Zu95a62562b6YCJ5Y+C5pWwDQogICAgICAgICAgY291bnRyeU9mT3JpZ2luOiB0aGlzLnNlbGVjdGVkQ291bnRyeSwNCiAgICAgICAgICBoYXNDYWNoZTogdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUsDQogICAgICAgIH07DQoNCiAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlKSB7DQogICAgICAgICAgcGFyYW1zLm1lbnVUeXBlID0gdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGU7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBhcGkubW9uaXRvcmluZ01lZGl1bShwYXJhbXMpOw0KDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgY29uc3QgZGF0YUxpc3QgPSByZXMucm93cyB8fCBbXTsNCiAgICAgICAgICBjb25zdCB0b3RhbCA9IHJlcy50b3RhbCB8fCAwOw0KDQogICAgICAgICAgY29uc3QgbWFwRGF0YSA9IChkYXRhKSA9Pg0KICAgICAgICAgICAgZGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgICBpZDogYCR7DQogICAgICAgICAgICAgICAgaXRlbS5zb3VyY2VTbiB8fCAidW5rbm93biINCiAgICAgICAgICAgICAgfV8ke2luZGV4fV8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKQ0KICAgICAgICAgICAgICAgIC50b1N0cmluZygzNikNCiAgICAgICAgICAgICAgICAuc3Vic3RyaW5nKDIsIDExKX1gLCAvLyDnoa7kv53nu53lr7nllK/kuIDmgKcNCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uY25OYW1lLA0KICAgICAgICAgICAgICBjb3VudDogaXRlbS5hcnRpY2xlQ291bnQgfHwgMCwNCiAgICAgICAgICAgICAgb3JkZXJOdW06IGl0ZW0ub3JkZXJOdW0sDQogICAgICAgICAgICAgIGNvdW50cnk6IGl0ZW0uY291bnRyeU9mT3JpZ2luIHx8IG51bGwsDQogICAgICAgICAgICAgIHNvdXJjZVNuOiBpdGVtLnNvdXJjZVNuLA0KICAgICAgICAgICAgICB1cmw6IGl0ZW0udXJsIHx8IG51bGwsDQogICAgICAgICAgICB9KSk7DQoNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhVHJhbnNmZXIgPSBtYXBEYXRhKGRhdGFMaXN0KTsNCiAgICAgICAgICB0aGlzLnRyZWVUb3RhbCA9IHRvdGFsOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmn6Xor6LmoJHmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bmlbDmja7mupDlpLHotKUiKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmn6Xor6Lmlofnq6DliJfooajvvIjluKbpmLLmipbvvIkNCiAgICBhc3luYyBxdWVyeUFydGljbGVMaXN0KGZsYWcpIHsNCiAgICAgIC8vIOmYsuatoumHjeWkjeafpeivog0KICAgICAgaWYgKHRoaXMuaXNRdWVyeWluZykgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICghZmxhZykgew0KICAgICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IHRydWU7DQogICAgICB9DQoNCiAgICAgIC8vIOa4hemZpOS5i+WJjeeahOmYsuaKluWumuaXtuWZqA0KICAgICAgaWYgKHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyKSB7DQogICAgICAgIGNsZWFyVGltZW91dCh0aGlzLnF1ZXJ5RGVib3VuY2VUaW1lcik7DQogICAgICB9DQoNCiAgICAgIC8vIOiuvue9rumYsuaKlu+8jDMwMG1z5ZCO5omn6KGM5p+l6K+iDQogICAgICB0aGlzLnF1ZXJ5RGVib3VuY2VUaW1lciA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGlmIChmbGFnID09PSAic291cmNlSXRlbUNoYW5nZWQiKSB7DQogICAgICAgICAgICB0aGlzLmdsb2JhbExvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHRoaXMuaXNRdWVyeWluZyA9IHRydWU7DQoNCiAgICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgICBtOiAxLA0KICAgICAgICAgICAgcGFnZU51bTogdGhpcy5jdXJyZW50UGFnZSwNCiAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlID8gIjEiIDogdGhpcy4kcm91dGUucXVlcnkuaWQsDQogICAgICAgICAgICBpc1NvcnQ6IHRoaXMuU2VhY2hEYXRhLnNvcnRNb2RlLA0KICAgICAgICAgICAgZGF0ZVR5cGU6DQogICAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLnRpbWVSYW5nZSAhPSA2ID8gdGhpcy5TZWFjaERhdGEudGltZVJhbmdlIDogIiIsDQogICAgICAgICAgICBzdGFydFRpbWU6IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheQ0KICAgICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheVswXQ0KICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAgZW5kVGltZTogdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5DQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzFdDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBjb2xsZWN0aW9uRGF0ZVR5cGU6DQogICAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSAhPSA2DQogICAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uRGF0ZVR5cGUNCiAgICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAgY29sbGVjdGlvblN0YXJ0VGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZVswXQ0KICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAgY29sbGVjdGlvbkVuZFRpbWU6IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lDQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMV0NCiAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGtleXdvcmRzOiB0aGlzLlNlYWNoRGF0YS5rZXl3b3JkLA0KICAgICAgICAgICAgaXNUZWNobm9sb2d5OiB0aGlzLlNlYWNoRGF0YS5pc1RlY2hub2xvZ3ksDQogICAgICAgICAgICBlbW90aW9uOiB0aGlzLlNlYWNoRGF0YS5lbW90aW9uLA0KICAgICAgICAgICAgcGxhdGZvcm1UeXBlOiAxLA0KICAgICAgICAgIH07DQoNCiAgICAgICAgICBpZiAodGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUpIHsNCiAgICAgICAgICAgIHBhcmFtcy5tZW51VHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaehOW7uuafpeivouWPguaVsA0KICAgICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMuc2F2ZWRDaGVja2JveERhdGEubWFwKChpdGVtKSA9PiBpdGVtLmxhYmVsKTsNCiAgICAgICAgICAgIGNvbnN0IHNvdXJjZVNuID0gdGhpcy5zYXZlZENoZWNrYm94RGF0YS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiBpdGVtLnNvdXJjZVNuDQogICAgICAgICAgICApOw0KDQogICAgICAgICAgICBwYXJhbXMud2VDaGF0TmFtZSA9IFN0cmluZyhkYXRhKTsNCiAgICAgICAgICAgIHBhcmFtcy5zb3VyY2VTbiA9IFN0cmluZyhzb3VyY2VTbik7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6K6w5b2V5YWz6ZSu6K+N5Y6G5Y+yDQogICAgICAgICAgaWYgKHBhcmFtcy5rZXl3b3Jkcykgew0KICAgICAgICAgICAgYWRkQXJ0aWNsZUhpc3RvcnkoeyBrZXl3b3JkOiBwYXJhbXMua2V5d29yZHMsIHR5cGU6IDIgfSkudGhlbigNCiAgICAgICAgICAgICAgKCkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBsZXQgcXlramR0UGFyYW1zOw0KDQogICAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgICAgICAgcXlramR0UGFyYW1zID0gew0KICAgICAgICAgICAgICBwYWdlTnVtOiB0aGlzLmN1cnJlbnRQYWdlLA0KICAgICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy5wYWdlU2l6ZSwNCiAgICAgICAgICAgICAgc291cmNlU246IHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbiwNCiAgICAgICAgICAgICAgaXNTb3J0OiB0aGlzLlNlYWNoRGF0YS5zb3J0TW9kZSwNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgcmVzID0gdGhpcy4kcm91dGUucXVlcnkuZG9tYWluDQogICAgICAgICAgICA/IGF3YWl0IGFwaS5xeWtqZHRBcnRpY2xlTGlzdCh7IC4uLnF5a2pkdFBhcmFtcyB9KQ0KICAgICAgICAgICAgOiBhd2FpdCBhcGkuS2VJbnRlZ3JhdGlvbih7IC4uLnBhcmFtcyB9KTsNCg0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIGxldCBhcnRpY2xlTGlzdDsNCg0KICAgICAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgICAgICAgICBhcnRpY2xlTGlzdCA9IHJlcy5yb3dzIHx8IFtdOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgYXJ0aWNsZUxpc3QgPSByZXMuZGF0YS5saXN0IHx8IFtdOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDljrvph43pgLvovpHvvJrlj6rmnInlnKjmsqHmnInlhbPplK7or43mkJzntKLml7bmiY3ov5vooYzljrvph40NCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgIXRoaXMuU2VhY2hEYXRhLmtleXdvcmQgfHwNCiAgICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEua2V5d29yZC50cmltKCkgPT09ICIiDQogICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgYXJ0aWNsZUxpc3QgPSB0aGlzLmRlZHVwbGljYXRlQXJ0aWNsZXMoYXJ0aWNsZUxpc3QpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0ID0gYXJ0aWNsZUxpc3Q7DQoNCiAgICAgICAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5kb21haW4pIHsNCiAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbCB8fCAwOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOaBouWkjemAieS4reeKtuaAge+8iOmdmem7mOaBouWkje+8jOS4jeinpuWPkeWPs+S+p+afpeivou+8iQ0KICAgICAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIHRoaXMucmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDlpITnkIbliIbpobXkuLrnqbrnmoTmg4XlhrUNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5sZW5ndGggPT0gMCAmJg0KICAgICAgICAgICAgICB0aGlzLnBhZ2VTaXplICogKHRoaXMuY3VycmVudFBhZ2UgLSAxKSA+PSB0aGlzLnRvdGFsICYmDQogICAgICAgICAgICAgIHRoaXMudG90YWwgIT0gMA0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSBNYXRoLm1heCgNCiAgICAgICAgICAgICAgICAxLA0KICAgICAgICAgICAgICAgIE1hdGguY2VpbCh0aGlzLnRvdGFsIC8gdGhpcy5wYWdlU2l6ZSkNCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgLy8g6YeN5paw5p+l6K+iDQogICAgICAgICAgICAgIGF3YWl0IHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICAgICAgICByZXR1cm47IC8vIOmHjeaWsOafpeivouaXtuS4jeimgeWFs+mXrWxvYWRpbmcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICLojrflj5bmlbDmja7lpLHotKUiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5p+l6K+i5paH56ug5YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmn6Xor6LlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICB0aGlzLmlzUXVlcnlpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmdsb2JhbExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IGZhbHNlOyAvLyDmn6Xor6LlrozmiJDlkI7lhbPpl61sb2FkaW5nDQogICAgICAgICAgdGhpcy5idXR0b25EaXNhYmxlZCA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9LCAxMDAwKTsNCiAgICB9LA0KDQogICAgLy8g5rua5Yqo5Yiw6aG26YOoDQogICAgc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5bCd6K+V5aSa56eN5rua5Yqo5pa55byP56Gu5L+d5rua5Yqo5oiQ5YqfDQogICAgICAgIGNvbnN0IHNjcm9sbEJveEVsZW1lbnQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIuc2NvbGxCb3giKTsNCiAgICAgICAgaWYgKHNjcm9sbEJveEVsZW1lbnQpIHsNCiAgICAgICAgICBzY3JvbGxCb3hFbGVtZW50LnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpoLmnpxNYWluQXJ0aWNsZee7hOS7tuaciXNjcm9sbOW8leeUqO+8jOS5n+Wwneivlea7muWKqOWugw0KICAgICAgICBpZiAoDQogICAgICAgICAgdGhpcy4kcmVmcy5tYWluQXJ0aWNsZSAmJg0KICAgICAgICAgIHRoaXMuJHJlZnMubWFpbkFydGljbGUuJHJlZnMgJiYNCiAgICAgICAgICB0aGlzLiRyZWZzLm1haW5BcnRpY2xlLiRyZWZzLnNjcm9sbA0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLm1haW5BcnRpY2xlLiRyZWZzLnNjcm9sbC5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5rua5Yqo5pW05Liq5Y+z5L6n5Yy65Z+fDQogICAgICAgIGNvbnN0IHJpZ2h0TWFpbiA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5yaWdodE1haW4iKTsNCiAgICAgICAgaWYgKHJpZ2h0TWFpbikgew0KICAgICAgICAgIHJpZ2h0TWFpbi5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGFzeW5jIGdldEFyZWEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBSZXNwb25zZSA9IGF3YWl0IGFwaS5nZXRBcmVhTGlzdCgpOw0KICAgICAgICBpZiAoUmVzcG9uc2UgJiYgUmVzcG9uc2UuY29kZSA9PSAyMDAgJiYgUmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuYXJlYUxpc3QgPSBSZXNwb25zZS5kYXRhWzBdIHx8IFtdOw0KICAgICAgICAgIHRoaXMuY291bnRyeUxpc3QgPSBSZXNwb25zZS5kYXRhWzFdIHx8IFtdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2Fybigi6I635Y+W5Zyw5Yy65pWw5o2u5aSx6LSl5oiW5pWw5o2u5Li656m6Iik7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bljLrln5/mlbDmja7lpLHotKU6IiwgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Zyw5Yy65pWw5o2u6I635Y+W5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGdldEFydGljbGVIaXN0b3J5KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgbGlzdEFydGljbGVIaXN0b3J5KHsNCiAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgIHBhZ2VTaXplOiA1LA0KICAgICAgICAgIHR5cGU6IDIsDQogICAgICAgIH0pOw0KICAgICAgICBpZiAocmVzICYmIHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmhpc3RvcnlMaXN0ID0gcmVzLnJvd3MgfHwgW107DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWOhuWPsuiusOW9leWksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOagkeiKgueCuei/h+a7pOaWueazlQ0KICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOw0KICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqBFc1NlYWNo5pa55rOV5Lul5YW85a655Y6f5pyJ6LCD55SoDQogICAgRXNTZWFjaChmbGFnKSB7DQogICAgICBpZiAoZmxhZyA9PT0gImZpbHRlciIpIHsNCiAgICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICAgIC8vIOetm+mAieWPmOWMluaXtuWQjOaXtuafpeivouagkeWSjOWIl+ihqA0KICAgICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWFtuS7luaDheWGteWPquafpeivouWIl+ihqA0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOmHjee9ruaQnOe0ouadoeS7tiAtIOeugOWMlueJiOacrA0KICAgIHJlc2V0dGluZygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuU2VhY2hEYXRhID0gew0KICAgICAgICAgIG1ldGFNb2RlOiAiIiAvKiDljLnphY3mqKHlvI8gKi8sDQogICAgICAgICAga2V5d29yZDogIiIgLyog5YWz6ZSu6K+NICovLA0KICAgICAgICAgIHRpbWVSYW5nZTogIiIgLyog5pe26Ze06IyD5Zu0ICovLA0KICAgICAgICAgIGN1c3RvbURheTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZTogbnVsbCAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgICAgY29sbGVjdGlvblRpbWU6IFtdIC8qIOiHquWumuS5ieWkqSAqLywNCiAgICAgICAgICBpc1RlY2hub2xvZ3k6ICIxIiwNCiAgICAgICAgICBzb3J0TW9kZTogIjQiLA0KICAgICAgICAgIGVtb3Rpb246ICIwIiwNCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6YeN572u5pCc57Si5p2h5Lu25pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6YeN572u5pCc57Si5p2h5Lu25aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIHJlbW92ZUhpc3RvcnkoaXRlbSwgdHlwZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuaGlzdG9yeVRpbWVvdXQpIHsNCiAgICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoaXRlbSAmJiBpdGVtLmlkKSB7DQogICAgICAgICAgYXdhaXQgZGVsQXJ0aWNsZUhpc3RvcnkoW2l0ZW0uaWRdKTsNCg0KICAgICAgICAgIGlmICh0eXBlID09IDEpIHsNCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmc1sia2V5d29yZFJlZiJdLmZvY3VzKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBhd2FpdCB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliKDpmaTljoblj7LorrDlvZXml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliKDpmaTljoblj7LorrDlvZXlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgc2hvd0hpc3RvcnlMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5zaG93SGlzdG9yeSA9IHRydWU7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmmL7npLrljoblj7LliJfooajml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoaWRlSGlzdG9yeUxpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAodGhpcy5oaXN0b3J5VGltZW91dCkgew0KICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuaGlzdG9yeVRpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7DQogICAgICAgIH0sIDUwMCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLpmpDol4/ljoblj7LliJfooajml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7IC8vIOehruS/neWcqOWHuumUmeaXtuS5n+iDvemakOiXj+WIl+ihqA0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlhbPplK7or43ljoblj7LpgInmi6kgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAga2V5d29yZHNDaGFuZ2UoaXRlbSkgew0KICAgICAgdGhpcy5TZWFjaERhdGEua2V5d29yZCA9IGl0ZW0ua2V5d29yZDsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZTsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7DQogICAgICAvLyB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgIH0sDQoNCiAgICBhc3luYyBjbGVhckhpc3RvcnkoKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAodGhpcy5oaXN0b3J5VGltZW91dCkgew0KICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0pIHsNCiAgICAgICAgICB0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0uZm9jdXMoKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGF3YWl0IGNsZWFuQXJ0aWNsZUhpc3RvcnkoMik7DQogICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIua4hemZpOWOhuWPsuiusOW9leaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIua4hemZpOWOhuWPsuiusOW9leWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBtb3JlSGlzdG9yeSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOw0KICAgICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5MSgpOw0KICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gdHJ1ZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWKoOi9veabtOWkmuWOhuWPsuiusOW9leaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0QXJ0aWNsZUhpc3RvcnkxKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IHRydWU7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbGlzdEFydGljbGVIaXN0b3J5KHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zMSwNCiAgICAgICAgICB0eXBlOiAyLA0KICAgICAgICB9KTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UpIHsNCiAgICAgICAgICB0aGlzLmhpc3RvcnlMaXN0MSA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgICAgdGhpcy50b3RhbDEgPSByZXNwb25zZS50b3RhbCB8fCAwOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5paH56ug5Y6G5Y+y6K6w5b2V5pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bmkJzntKLljoblj7LlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgb3BlblVybCh1cmwpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHVybCwgIl9ibGFuayIpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbov4fmu6TmkJzntKLvvIjmnaXoh6ogVHJlZVRhYmxlIOe7hOS7tu+8iQ0KICAgIGhhbmRsZUZpbHRlclNlYXJjaChrZXl3b3JkKSB7DQogICAgICBpZiAodGhpcy5pc0xlZnRSZXNldCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOS4jeS/neWtmOW9k+WJjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KDQogICAgICAvLyDmm7TmlrDmn6Xor6Llj4LmlbDkuK3nmoQgZmlsdGVyd29yZHMNCiAgICAgIHRoaXMudHJlZVF1ZXJ5LmZpbHRlcndvcmRzID0ga2V5d29yZCB8fCAiIjsNCg0KICAgICAgLy8g6YeN572u5Yiw56ys5LiA6aG1DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLlNlYWNoRGF0YS5oYXNDYWNoZSA9ICIxIjsNCg0KICAgICAgLy8g6LCD55So5qCR5pWw5o2u5p+l6K+i5o6l5Y+j5bm25oGi5aSN6YCJ5Lit54q25oCB77yI5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yJDQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmlbDmja7mupDliIbnsbvlj5jljJbvvIjmnaXoh6ogVHJlZVRhYmxlIOe7hOS7tu+8iQ0KICAgIGhhbmRsZUNsYXNzaWZ5Q2hhbmdlKGNsYXNzaWZ5VmFsdWUpIHsNCiAgICAgIC8vIOS4jeS/neWtmOW9k+WJjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KDQogICAgICAvLyDmm7TmlrDpgInkuK3nmoTliIbnsbsNCiAgICAgIHRoaXMuc2VsZWN0ZWRDbGFzc2lmeSA9IGNsYXNzaWZ5VmFsdWU7DQoNCiAgICAgIC8vIOmHjee9ruWIsOesrOS4gOmhtQ0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7DQoNCiAgICAgIC8vIOWPquiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5Zu95a62562b6YCJ5Y+Y5YyW77yI5p2l6IeqIFRyZWVUYWJsZSDnu4Tku7bvvIkNCiAgICBoYW5kbGVDb3VudHJ5Q2hhbmdlKGNvdW50cnlWYWx1ZSkgew0KICAgICAgLy8g5LiN5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAgIC8vIOabtOaWsOmAieS4reeahOWbveWutg0KICAgICAgdGhpcy5zZWxlY3RlZENvdW50cnkgPSBjb3VudHJ5VmFsdWU7DQoNCiAgICAgIC8vIOmHjee9ruWIsOesrOS4gOmhtQ0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7DQoNCiAgICAgIC8vIOWPquiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5qCR5b2i5YiG6aG16aG156CB5Y+Y5YyWDQogICAgaGFuZGxlVHJlZUN1cnJlbnRDaGFuZ2UocGFnZSkgew0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSBwYWdlOw0KICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7DQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmoJHlvaLliIbpobXmr4/pobXlpKflsI/lj5jljJYNCiAgICBoYW5kbGVUcmVlUGFnZVNpemVDaGFuZ2Uoc2l6ZSkgew0KICAgICAgdGhpcy50cmVlUGFnZVNpemUgPSBzaXplOw0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5TZWFjaERhdGEuaGFzQ2FjaGUgPSAiMSI7DQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnvLrlpLHnmoRvcGVuTmV3Vmlld+aWueazlQ0KICAgIG9wZW5OZXdWaWV3KGl0ZW0pIHsNCiAgICAgIHdpbmRvdy5vcGVuKA0KICAgICAgICBgL2V4cHJlc3NEZXRhaWxzP2lkPSR7aXRlbS5pZH0mZG9jSWQ9JHtpdGVtLmRvY0lkfSZzb3VyY2VUeXBlPSR7aXRlbS5zb3VyY2VUeXBlfWAsDQogICAgICAgICJfYmxhbmsiDQogICAgICApOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnvLrlpLHnmoRoYW5kbGVIaXN0b3J5UGFnaW5hdGlvbuaWueazlQ0KICAgIGhhbmRsZUhpc3RvcnlQYWdpbmF0aW9uKCkgew0KICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeTEoKTsNCiAgICB9LA0KDQogICAgLy8g5paH56ug5Y676YeN5pa55rOVDQogICAgZGVkdXBsaWNhdGVBcnRpY2xlcyhhcnRpY2xlcykgew0KICAgICAgaWYgKCFhcnRpY2xlcyB8fCBhcnRpY2xlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIGFydGljbGVzOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB0aXRsZU1hcCA9IG5ldyBNYXAoKTsNCiAgICAgIGNvbnN0IHJlc3VsdCA9IFtdOw0KDQogICAgICAvLyDnu5/orqHnm7jlkIzmoIfpopjnmoTmlofnq6DmlbDph48NCiAgICAgIGFydGljbGVzLmZvckVhY2goKGFydGljbGUpID0+IHsNCiAgICAgICAgLy8g5Y676ZmkSFRNTOagh+etvuWSjOaJgOacieepuuagvOadpeavlOi+g+agh+mimA0KICAgICAgICBjb25zdCBjbGVhblRpdGxlID0gYXJ0aWNsZS50aXRsZQ0KICAgICAgICAgID8gYXJ0aWNsZS50aXRsZS5yZXBsYWNlKC88W14+XSo+L2csICIiKS5yZXBsYWNlKC9ccysvZywgIiIpDQogICAgICAgICAgOiAiIjsNCg0KICAgICAgICBpZiAodGl0bGVNYXAuaGFzKGNsZWFuVGl0bGUpKSB7DQogICAgICAgICAgdGl0bGVNYXAuZ2V0KGNsZWFuVGl0bGUpLmNvdW50Kys7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGl0bGVNYXAuc2V0KGNsZWFuVGl0bGUsIHsNCiAgICAgICAgICAgIGFydGljbGU6IHsgLi4uYXJ0aWNsZSB9LA0KICAgICAgICAgICAgY291bnQ6IDEsDQogICAgICAgICAgICBvcmlnaW5hbFRpdGxlOiBhcnRpY2xlLnRpdGxlLCAvLyDkv53lrZjljp/lp4vmoIfpopjvvIjlj6/og73ljIXlkKtIVE1M5qCH562+77yJDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICAvLyDnlJ/miJDljrvph43lkI7nmoTmlofnq6DliJfooagNCiAgICAgIHRpdGxlTWFwLmZvckVhY2goKHsgYXJ0aWNsZSwgY291bnQsIG9yaWdpbmFsVGl0bGUgfSkgPT4gew0KICAgICAgICBpZiAoY291bnQgPiAxKSB7DQogICAgICAgICAgLy8g5aaC5p6c5pyJ6YeN5aSN77yM5Zyo5qCH6aKY5ZCO6Z2i5Yqg5LiK5pWw6YeP5qCH6K6wDQogICAgICAgICAgLy8g5L2/55So5Y6f5aeL5qCH6aKY77yI5L+d5oyBSFRNTOagvOW8j++8iQ0KICAgICAgICAgIGFydGljbGUudGl0bGUgPSBgJHtvcmlnaW5hbFRpdGxlIHx8ICIife+8iCR7Y291bnR977yJYDsNCiAgICAgICAgfQ0KICAgICAgICByZXN1bHQucHVzaChhcnRpY2xlKTsNCiAgICAgIH0pOw0KDQogICAgICByZXR1cm4gcmVzdWx0Ow0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["keMonitor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "keMonitor.vue", "sourceRoot": "src/views/KeMonitor", "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        this.globalLoading = true;\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 默认全选第一页数据源\r\n        if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {\r\n          // 全选第一页的所有数据源\r\n          const firstPageData = [...this.treeDataTransfer];\r\n          this.checkList = firstPageData;\r\n          this.savedCheckboxData = firstPageData;\r\n\r\n          // 通知 TreeTable 组件设置选中状态\r\n          this.$nextTick(() => {\r\n            if (this.$refs.treeTable) {\r\n              this.$refs.treeTable.restoreSelectionSilently(firstPageData);\r\n            }\r\n          });\r\n\r\n          // 延迟一下再查询文章列表，确保选中状态已设置\r\n          setTimeout(() => {\r\n            this.queryArticleList();\r\n          }, 100);\r\n        } else {\r\n          // 如果没有数据源，直接查询文章列表\r\n          this.queryArticleList();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}