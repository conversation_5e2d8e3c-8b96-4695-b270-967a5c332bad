{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=template&id=794a9327&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753944647587}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}