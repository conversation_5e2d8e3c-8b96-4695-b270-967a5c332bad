{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1753944503958}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXBpIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHsNCiAgbGlzdFdvcmssDQogIGdldFdvcmssDQogIGRlbFdvcmssDQogIGFkZFdvcmssDQogIHVwZGF0ZVdvcmssDQp9IGZyb20gIkAvYXBpL2FydGljbGUvd29yayI7DQppbXBvcnQgeyBsaXN0S2V5d29yZHMgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2tleXdvcmRzIjsNCmltcG9ydCBBUEkgZnJvbSAiQC9hcGkvU2NpZW5jZUFwaS9pbmRleC5qcyI7DQppbXBvcnQgew0KICBsaXN0QXJ0aWNsZUhpc3RvcnksDQogIGRlbEFydGljbGVIaXN0b3J5LA0KICBhZGRBcnRpY2xlSGlzdG9yeSwNCiAgY2xlYW5BcnRpY2xlSGlzdG9yeSwNCiAgZ2V0TGlzdEJ5SWRzLA0KfSBmcm9tICJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5IjsNCmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsNCmltcG9ydCAic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIjsNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICJ2dWV4IjsNCmltcG9ydCBUcmVlVGFibGUgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVUYWJsZS9pbmRleC52dWUiOw0KaW1wb3J0IHsgZGVlcHNlZWtBaVFhLCBkaWZ5QWlRYSwgb2xsYW1hQWlRYSB9IGZyb20gIkAvYXBpL2luZm9Fc2NhbGF0aW9uL2FpIjsNCmltcG9ydCB7IG1hcmtlZCB9IGZyb20gIm1hcmtlZCI7DQppbXBvcnQgeyBnZXRDb25maWdLZXkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29uZmlnIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFNwbGl0cGFuZXMsIFBhbmUsIFRyZWVUYWJsZSB9LA0KICBkaWN0czogWyJpc190ZWNobm9sb2d5Il0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGFibGVMb2FkaW5nOiBmYWxzZSwgLy8g6KGo5qC8bG9hZGluZ+eKtuaAgQ0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgaWQ6IDEwMCwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwLA0KICAgICAgICBkYXRlVHlwZTogNCwNCiAgICAgICAgdGFnczogIiIsDQogICAgICAgIHRhZ3NTdWJzZXQ6IFtdLA0KICAgICAgICBrZXl3b3JkczogIiIsDQogICAgICAgIGlzVGVjaG5vbG9neTogIjEiLA0KICAgICAgICBzb3J0TW9kZTogIjQiLA0KICAgICAgICBlbW90aW9uOiAiMCIsDQogICAgICAgIGhhc0NhY2hlOiAiMCIsDQogICAgICB9LA0KICAgICAgdG90YWw6IDAsDQogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwgLy8g5Y6f5aeL5qCR5b2i5pWw5o2uDQogICAgICBmaWx0ZXJUZXh0OiAiIiwgLy8g5bem5L6n5qCR5pCc57Si5qCPDQogICAgICBjaGVja0xpc3Q6IFtdLCAvLyDlt6bkvqfli77pgInmlbDmja4NCiAgICAgIEFydGljbGVMaXN0OiBbXSwgLy8g5YiX6KGo5pWw5o2uDQogICAgICBjaGVja2VkOiBmYWxzZSwgLy8g5YWo6YCJDQogICAgICBpZHM6IFtdLCAvLyDpgInkuK3nmoTmlbDmja4NCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwgLy8g5re75Yqg5Yiw5oql5ZGK5by55qGGDQogICAgICByZXBvcnRPcHRpb25zOiBbXSwgLy8g5oql5ZGK5YiX6KGoDQogICAgICByZXBvcnRJZDogIiIsIC8vIOW3sumAieaLqeeahOaKpeWRig0KICAgICAgdGFnc0xpc3Q6IFtdLCAvLyDmo4DntKLor43lupPkuoznuqfliJfooagNCiAgICAgIHRhZ3NMaXN0MTogW10sIC8vIOajgOe0ouivjeW6k+S4gOe6p+WIl+ihqA0KICAgICAgY2hlY2tBbGw6IGZhbHNlLCAvLyDmo4DntKLor43lupPlhajpgIkNCiAgICAgIGlzSW5kZXRlcm1pbmF0ZTogdHJ1ZSwgLy8g5qOA57Si6K+N5bqT6YCJ5LqG5YC8DQogICAgICBzaG93SGlzdG9yeTogZmFsc2UsDQogICAgICBoaXN0b3J5TGlzdDogW10sDQogICAgICBoaXN0b3J5VGltZW91dDogbnVsbCwNCiAgICAgIGRpYWxvZ1Zpc2libGUxOiBmYWxzZSwNCiAgICAgIGhpc3RvcnlMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1ZXJ5UGFyYW1zMTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9LA0KICAgICAgdG90YWwxOiAwLA0KICAgICAgaGlzdG9yeUxpc3QxOiBbXSwNCiAgICAgIHNob3dTdW1tYXJ5OiB0cnVlLA0KICAgICAgLyog5qCR5b2i5YiG6aG15pWw5o2uICovDQogICAgICB0cmVlQ3VycmVudFBhZ2U6IDEsDQogICAgICB0cmVlUGFnZVNpemU6IDEwMCwNCiAgICAgIHRyZWVUb3RhbDogMCwNCiAgICAgIC8qIOWIneWni+WMluWujOaIkOagh+iusCAqLw0KICAgICAgaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgLyog5pCc57Si6Ziy5oqWICovDQogICAgICBzZWFyY2hEZWJvdW5jZVRpbWVyOiBudWxsLA0KICAgICAgLyog5p+l6K+i6Ziy5oqWICovDQogICAgICBxdWVyeURlYm91bmNlVGltZXI6IG51bGwsDQogICAgICAvKiDpmLLmraLph43lpI3mn6Xor6IgKi8NCiAgICAgIGlzUXVlcnlpbmc6IGZhbHNlLA0KICAgICAgLyog5qCH6K6w5Y+z5L6n562b6YCJ5p2h5Lu25piv5ZCm5Y+R55Sf5Y+Y5YyWICovDQogICAgICBpc1JpZ2h0RmlsdGVyOiBmYWxzZSwNCiAgICAgIC8qIOagh+iusOW3puS+p+agkeaYr+WQpumHjee9riAqLw0KICAgICAgaXNMZWZ0UmVzZXQ6IGZhbHNlLA0KICAgICAgLyog6YCJ5Lit55qE5pWw5o2u5rqQ5YiG57G7ICovDQogICAgICBzZWxlY3RlZENsYXNzaWZ5OiAiNSIsDQogICAgICAvKiDkv53lrZjnmoTli77pgInmlbDmja7vvIjmsLjkuYXkv53lrZjvvIzlj6rmnInnibnlrprmk43kvZzmiY3mm7TmlrDvvIkgKi8NCiAgICAgIHNhdmVkQ2hlY2tib3hEYXRhOiBbXSwNCiAgICAgIC8vIGFp55u45YWzDQogICAgICBhaURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY2hhdE1lc3NhZ2VzOiBbXSwNCiAgICAgIGlzVGhpbmtpbmc6IGZhbHNlLA0KICAgICAgdXNlckF2YXRhcjogIiIsIC8vIOeUqOaIt+WktOWDjw0KICAgICAgc3RyZWFtaW5nTWVzc2FnZTogIiIsIC8vIOa3u+WKoOeUqOS6juWtmOWCqOato+WcqOa1geW8j+i+k+WHuueahOa2iOaBrw0KICAgICAgbWFya2Rvd25PcHRpb25zOiB7DQogICAgICAgIGdmbTogdHJ1ZSwNCiAgICAgICAgYnJlYWtzOiB0cnVlLA0KICAgICAgICBoZWFkZXJJZHM6IHRydWUsDQogICAgICAgIG1hbmdsZTogZmFsc2UsDQogICAgICAgIGhlYWRlclByZWZpeDogIiIsDQogICAgICAgIHBlZGFudGljOiBmYWxzZSwNCiAgICAgICAgc2FuaXRpemU6IGZhbHNlLA0KICAgICAgICBzbWFydExpc3RzOiB0cnVlLA0KICAgICAgICBzbWFydHlwYW50czogdHJ1ZSwNCiAgICAgICAgeGh0bWw6IHRydWUsDQogICAgICB9LA0KICAgICAgaXNSZXF1ZXN0aW5nOiBmYWxzZSwgLy8g5qCH6K6w5piv5ZCm5q2j5Zyo6K+35rGC5LitDQogICAgICBpc0Fib3J0ZWQ6IGZhbHNlLCAvLyDmoIforrDmmK/lkKblt7LkuK3mlq0NCiAgICAgIGN1cnJlbnRSZWFkZXI6IG51bGwsIC8vIOW9k+WJjeeahCByZWFkZXINCiAgICAgIGFpUGxhdGZvcm06ICIiLA0KICAgICAgYXJ0aWNsZUFpUHJvbXB0OiAiIiwNCiAgICAgIG5vZGVDaGVja0xpc3Q6IFtdLA0KICAgICAgY2hhcnREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoYXJ0SHRtbDogIiIsDQogICAgICBjaGFydExvYWRpbmc6IHRydWUsDQogICAgICBjdXJyZW50Q2hhcnRJZnJhbWU6IG51bGwsIC8vIOa3u+WKoOWPmOmHj+i3n+i4quW9k+WJjWlmcmFtZQ0KICAgICAgZGlmeUFwaWtleTogew0KICAgICAgICBhcnRpY2xlOiAiIiwNCiAgICAgICAgY2hhcnQ6ICIiLA0KICAgICAgfSwNCiAgICAgIGNoYXJ0UHJvbXB0OiAiIiwNCiAgICAgIGdsb2JhbExvYWRpbmc6IGZhbHNlLA0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgLy8g55uR5ZCs562b6YCJ5p2h5Lu25Y+Y5YyWDQogICAgInF1ZXJ5UGFyYW1zLmRhdGVUeXBlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgICJxdWVyeVBhcmFtcy5pc1RlY2hub2xvZ3kiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgInF1ZXJ5UGFyYW1zLmVtb3Rpb24iOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgInF1ZXJ5UGFyYW1zLnRhZ3MiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KDQogICAgICAgIC8vIHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCA9IFtdOw0KICAgICAgICB0aGlzLmNoZWNrQWxsID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5pc0luZGV0ZXJtaW5hdGUgPSBmYWxzZTsNCg0KICAgICAgICBpZiAobmV3VmFsICE9ICIiKSB7DQogICAgICAgICAgLy8g5LiN5Zyo6L+Z6YeM6K6+572udGFibGVMb2FkaW5n77yM6K6p5ZCO57ut55qEcXVlcnlBcnRpY2xlTGlzdOadpeWkhOeQhg0KICAgICAgICAgIGxpc3RLZXl3b3Jkcyh7IHBhcmVudElkOiBuZXdWYWwsIHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMCB9KQ0KICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGlzLnRhZ3NMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlQ2hlY2tBbGxUYWdzU3Vic2V0KHRydWUpOw0KICAgICAgICAgICAgICAvLyB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bmo4DntKLor43lupPlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5bmo4DntKLor43lupPlpLHotKUiKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaGFuZGxlUmlnaHRGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICB9LA0KICAgICJxdWVyeVBhcmFtcy50YWdzU3Vic2V0Ijogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoDQogICAgICAgICAgIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwNCiAgICAgICAgICBKU09OLnN0cmluZ2lmeShuZXdWYWwpID09PSBKU09OLnN0cmluZ2lmeShvbGRWYWwpDQogICAgICAgICkNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlUmlnaHRGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgIH0sDQogICAgfSwNCiAgICAicXVlcnlQYXJhbXMuc29ydE1vZGUiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICB9LA0KICAgIH0sDQogICAgZGlhbG9nVmlzaWJsZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgYXBpLmdldE5ld0J1aWx0KHsgc291cmNlVHlwZTogIjEiIH0pLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgICBpZiAoZGF0YS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy5yZXBvcnRPcHRpb25zID0gZGF0YS5kYXRhOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuaKpeWRiuWIl+ihqOiOt+WPluWksei0peS6hiIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICAgICAgICB0aGlzLmNsb3NlUmVwb3J0KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC4uLm1hcEdldHRlcnMoWyJyb2xlcyJdKSwNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBnZXRDb25maWdLZXkoInN5cy5haS5wbGF0Zm9ybSIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLmFpUGxhdGZvcm0gPSByZXMubXNnOw0KICAgICAgfQ0KICAgIH0pOw0KICAgIGdldENvbmZpZ0tleSgid2VjaGF0LmFpLmFydGljbGVQcm9tcHQiKS50aGVuKChyZXMpID0+IHsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQgPSByZXMubXNnOw0KICAgICAgfQ0KICAgIH0pOw0KICAgIGdldENvbmZpZ0tleSgid2VjaGF0LmFpLmNoYXJ0UHJvbXB0IikudGhlbigocmVzKSA9PiB7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuY2hhcnRQcm9tcHQgPSByZXMubXNnOw0KICAgICAgfQ0KICAgIH0pOw0KICAgIC8vIOiOt+WPlueUqOaIt+WktOWDjw0KICAgIHRoaXMudXNlckF2YXRhciA9IHRoaXMuJHN0b3JlLmdldHRlcnMuYXZhdGFyOw0KICAgIHRyeSB7DQogICAgICAvLyDlhYjliqDovb3ln7rnoYDmlbDmja4NCiAgICAgIFByb21pc2UuYWxsKFsNCiAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpLA0KICAgICAgICBsaXN0S2V5d29yZHMoeyBwYXJlbnRJZDogMCwgcGFnZU51bTogMSwgcGFnZVNpemU6IDEwIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMudGFnc0xpc3QxID0gcmVzLmRhdGEuZmlsdGVyKChpdGVtKSA9PiBpdGVtLnBhcmVudElkID09IDApOw0KICAgICAgICB9KSwNCiAgICAgIF0pOw0KDQogICAgICAvLyDliqDovb3moJHmlbDmja7lkozlhoXlrrnmlbDmja4NCiAgICAgIGF3YWl0IHRoaXMuaW5pdGlhbGl6ZURhdGEoKTsNCg0KICAgICAgaWYgKHRoaXMucm9sZXMuaW5jbHVkZXMoImluZm9ybWF0aW9uIikpIHsNCiAgICAgICAgdGhpcy5zaG93U3VtbWFyeSA9IGZhbHNlOw0KICAgICAgfQ0KDQogICAgICAvLyDmoIforrDliJ3lp4vljJblrozmiJDvvIzov5nmoLd3YXRjaOebkeWQrOWZqOaJjeS8muW8gOWni+W3peS9nA0KICAgICAgdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCA9IHRydWU7DQogICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgIGNvbnNvbGUuZXJyb3IoIue7hOS7tuWIneWni+WMluWksei0pToiLCBlcnJvcik7DQogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliJ3lp4vljJblpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UiKTsNCiAgICB9DQogIH0sDQoNCiAgbW91bnRlZCgpIHt9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5Yid5aeL5YyW5pWw5o2uDQogICAgYXN5bmMgaW5pdGlhbGl6ZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlhYjliqDovb3moJHmlbDmja4NCiAgICAgICAgYXdhaXQgdGhpcy5xdWVyeVRyZWVEYXRhKCk7DQogICAgICAgIC8vIOetieW+heagkee7hOS7tuWujOWFqOa4suafkw0KICAgICAgICBhd2FpdCB0aGlzLiRuZXh0VGljaygpOw0KDQogICAgICAgIC8vIOm7mOiupOWFqOmAieesrOS4gOmhteaVsOaNrua6kA0KICAgICAgICBpZiAodGhpcy50cmVlRGF0YVRyYW5zZmVyICYmIHRoaXMudHJlZURhdGFUcmFuc2Zlci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5YWo6YCJ56ys5LiA6aG155qE5omA5pyJ5pWw5o2u5rqQDQogICAgICAgICAgY29uc3QgZmlyc3RQYWdlRGF0YSA9IFsuLi50aGlzLnRyZWVEYXRhVHJhbnNmZXJdOw0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gZmlyc3RQYWdlRGF0YTsNCiAgICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gZmlyc3RQYWdlRGF0YTsNCg0KICAgICAgICAgIC8vIOmAmuefpSBUcmVlVGFibGUg57uE5Lu26K6+572u6YCJ5Lit54q25oCBDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgaWYgKHRoaXMuJHJlZnMudHJlZVRhYmxlKSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVRhYmxlLnJlc3RvcmVTZWxlY3Rpb25TaWxlbnRseShmaXJzdFBhZ2VEYXRhKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOW7tui/n+S4gOS4i+WGjeafpeivouaWh+eroOWIl+ihqO+8jOehruS/nemAieS4reeKtuaAgeW3suiuvue9rg0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgICAgfSwgMTAwKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzmsqHmnInmlbDmja7mupDvvIznm7TmjqXmn6Xor6Lmlofnq6DliJfooagNCiAgICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yid5aeL5YyW5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWPs+S+p+etm+mAieadoeS7tuWPmOWMlg0KICAgIGhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCkgew0KICAgICAgdGhpcy5pc1JpZ2h0RmlsdGVyID0gdHJ1ZTsgLy8g5qCH6K6w5Y+z5L6n562b6YCJ5p2h5Lu25Y+R55Sf5Y+Y5YyWDQoNCiAgICAgIC8vIOS4jeWGjeS/neWtmOW9k+WJjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KICAgICAgLy8g5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5Lya5Zyo5p+l6K+i5ZCO6Ieq5Yqo5oGi5aSNDQoNCiAgICAgIC8vIOmHjee9ruWIhumhteWIsOesrOS4gOmhtQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFzQ2FjaGUgPSAiMCI7DQoNCiAgICAgIC8vIOa7muWKqOWIsOmhtumDqA0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQoNCiAgICAgIC8vIOWQjOaXtuafpeivouagkeWSjOWIl+ihqA0KICAgICAgdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOWQjOaXtuafpeivouagkeWSjOWIl+ihqA0KICAgIGFzeW5jIHF1ZXJ5VHJlZUFuZExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDkv53lrZjlvZPliY3nmoTmsLjkuYXli77pgInmlbDmja7vvIzpgb/lhY3lnKjmn6Xor6Lov4fnqIvkuK3kuKLlpLENCiAgICAgICAgY29uc3Qgc2F2ZWREYXRhID0gWy4uLnRoaXMuc2F2ZWRDaGVja2JveERhdGFdOw0KDQogICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOWFiOS4tOaXtuaBouWkjSBjaGVja0xpc3Qg5Lul5L6/5p+l6K+i5pe25bim5LiK5Y+C5pWwDQogICAgICAgIGlmIChzYXZlZERhdGEgJiYgc2F2ZWREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi5zYXZlZERhdGFdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWmguaenOayoeacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOa4heepuumAieS4reeKtuaAgQ0KICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlkIzml7bmn6Xor6LmoJHmlbDmja7lkozlj7PkvqfliJfooajvvIjkv53mjIHmgKfog73kvJjlir/vvIkNCiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAgIHRoaXMucXVlcnlUcmVlRGF0YSgpLA0KICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpLCAvLyBxdWVyeUFydGljbGVMaXN0IOWGhemDqOW3sue7j+WkhOeQhuS6hiB0YWJsZUxvYWRpbmcNCiAgICAgICAgXSk7DQoNCiAgICAgICAgLy8g56Gu5L+d5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5LiN5Lya5Lii5aSxDQogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBzYXZlZERhdGE7DQoNCiAgICAgICAgLy8g5p+l6K+i5a6M5oiQ5ZCO77yM5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM6Z2Z6buY5oGi5aSN55WM6Z2i6YCJ5Lit54q25oCBDQogICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMucmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5p+l6K+i5a6M5oiQ5ZCO6YeN572u5Y+z5L6n562b6YCJ5qCH6K6wDQogICAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IGZhbHNlOw0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmlzTGVmdFJlc2V0ID0gZmFsc2U7DQogICAgICAgIH0sIDMwMCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLlkIzml7bmn6Xor6LmoJHlkozliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmn6Xor6LlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgLy8g5Y2z5L2/5Ye66ZSZ5Lmf6KaB6YeN572u5qCH6K6wDQogICAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IGZhbHNlOw0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmlzTGVmdFJlc2V0ID0gZmFsc2U7DQogICAgICAgIH0sIDMwMCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaBouWkjemAieS4reaVsOaNrua6kOeahOaWueazleW3suWIoOmZpO+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KDQogICAgLy8g5LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB77yI5LuF5aSE55CG55WM6Z2i6YCJ5Lit54q25oCB77yJDQogICAgcmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy5zYXZlZENoZWNrYm94RGF0YSB8fCB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWcqOW9k+WJjeagkeaVsOaNruS4reafpeaJvuWMuemFjeeahOmhuQ0KICAgICAgY29uc3QgbWF0Y2hlZEl0ZW1zID0gW107DQogICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmZvckVhY2goKHNhdmVkSXRlbSkgPT4gew0KICAgICAgICBjb25zdCBmb3VuZEl0ZW0gPSB0aGlzLnRyZWVEYXRhVHJhbnNmZXIuZmluZCgNCiAgICAgICAgICAodHJlZUl0ZW0pID0+IHRyZWVJdGVtLnNvdXJjZVNuID09PSBzYXZlZEl0ZW0uc291cmNlU24NCiAgICAgICAgKTsNCiAgICAgICAgaWYgKGZvdW5kSXRlbSkgew0KICAgICAgICAgIG1hdGNoZWRJdGVtcy5wdXNoKGZvdW5kSXRlbSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBpZiAobWF0Y2hlZEl0ZW1zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5pu05paw6YCJ5Lit5YiX6KGo77yI5q2k5pe2IGNoZWNrTGlzdCDlt7Lnu4/lnKjmn6Xor6LliY3mgaLlpI3ov4fkuobvvIkNCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBtYXRjaGVkSXRlbXM7DQogICAgICAgIC8vIOmAmuefpSBUcmVlVGFibGUg57uE5Lu25oGi5aSN55WM6Z2i6YCJ5Lit54q25oCB77yI5LiN6Kem5Y+R5LqL5Lu277yJDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBpZiAodGhpcy4kcmVmcy50cmVlVGFibGUpIHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVRhYmxlLnJlc3RvcmVTZWxlY3Rpb25TaWxlbnRseShtYXRjaGVkSXRlbXMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInljLnphY3pobnvvIzmuIXnqbrpgInkuK3nirbmgIENCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit54q25oCB55qE5pa55rOV5bey5Yig6Zmk77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQoNCiAgICAvLyDmn6Xor6LmoJHmlbDmja7lubbku47msLjkuYXkv53lrZjnmoTli77pgInmlbDmja7mgaLlpI3pgInkuK3nirbmgIHvvIjnlKjkuo7lhbPplK7lrZfov4fmu6TvvIkNCiAgICBhc3luYyBxdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzlhYjkuLTml7bmgaLlpI0gY2hlY2tMaXN0IOS7peS+v+afpeivouaXtuW4puS4iuWPguaVsA0KICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi50aGlzLnNhdmVkQ2hlY2tib3hEYXRhXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5p+l6K+i5qCR5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMucXVlcnlUcmVlRGF0YSgpOw0KDQogICAgICAgIC8vIOafpeivouWujOaIkOWQju+8jOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOmdmem7mOaBouWkjeeVjOmdoumAieS4reeKtuaAgQ0KICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnJlc3RvcmVGcm9tU2F2ZWRDaGVja2JveERhdGEoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigNCiAgICAgICAgICAi5p+l6K+i5qCR5pWw5o2u5bm25LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB5aSx6LSlOiIsDQogICAgICAgICAgZXJyb3INCiAgICAgICAgKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiG6aG15aSE55CGDQogICAgaGFuZGxlUGFnaW5hdGlvbigpIHsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOWOhuWPsuiusOW9leWIhumhteWkhOeQhg0KICAgIGhhbmRsZUhpc3RvcnlQYWdpbmF0aW9uKCkgew0KICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeTEoKTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3QgZGlhbG9nQ29udGVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5lbC1kaWFsb2dfX2JvZHkiKTsNCiAgICAgICAgaWYgKGRpYWxvZ0NvbnRlbnQpIHsNCiAgICAgICAgICBkaWFsb2dDb250ZW50LnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmn6Xor6LmoJHmlbDmja4NCiAgICBhc3luYyBxdWVyeVRyZWVEYXRhKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDAsDQogICAgICAgICAgaWQ6IHRoaXMucXVlcnlQYXJhbXMuaWQsDQogICAgICAgICAgcGFnZU51bTogdGhpcy50cmVlQ3VycmVudFBhZ2UsDQogICAgICAgICAgcGFnZVNpemU6IHRoaXMudHJlZVBhZ2VTaXplLA0KICAgICAgICAgIG06IDEsDQogICAgICAgICAgZGF0ZVR5cGU6DQogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRhdGVUeXBlICE9IDYgPyB0aGlzLnF1ZXJ5UGFyYW1zLmRhdGVUeXBlIDogIiIsDQogICAgICAgICAgdGFnczogdGhpcy5xdWVyeVBhcmFtcy50YWdzLA0KICAgICAgICAgIHRhZ3NTdWJzZXQ6IHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCwNCiAgICAgICAgICBrZXl3b3JkczogdGhpcy5xdWVyeVBhcmFtcy5rZXl3b3JkcywNCiAgICAgICAgICBpc1RlY2hub2xvZ3k6IHRoaXMucXVlcnlQYXJhbXMuaXNUZWNobm9sb2d5LA0KICAgICAgICAgIGVtb3Rpb246IHRoaXMucXVlcnlQYXJhbXMuZW1vdGlvbiwNCiAgICAgICAgICBsYWJlbDogdGhpcy5xdWVyeVBhcmFtcy50YWdzU3Vic2V0LmpvaW4oIiwiKSwNCiAgICAgICAgICAvLyDmt7vliqDlhbPplK7lrZfov4fmu6Tlj4LmlbANCiAgICAgICAgICBmaWx0ZXJ3b3JkczogdGhpcy5maWx0ZXJUZXh0IHx8ICIiLA0KICAgICAgICAgIC8vIOa3u+WKoOaVsOaNrua6kOWIhuexu+WPguaVsA0KICAgICAgICAgIHRoaW5rVGFua0NsYXNzaWZpY2F0aW9uOiB0aGlzLnNlbGVjdGVkQ2xhc3NpZnksDQogICAgICAgICAgaGFzQ2FjaGU6IHRoaXMucXVlcnlQYXJhbXMuaGFzQ2FjaGUsDQogICAgICAgIH07DQoNCiAgICAgICAgaWYgKCF0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3MpIHsNCiAgICAgICAgICBwYXJhbXMudGFnc1N1YnNldCA9IFtdOw0KICAgICAgICAgIHBhcmFtcy5sYWJlbCA9ICIiOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgYXBpLm1vbml0b3JpbmdNZWRpdW0ocGFyYW1zKTsNCg0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGRhdGFMaXN0ID0gcmVzLnJvd3MgfHwgW107DQogICAgICAgICAgY29uc3QgdG90YWwgPSByZXMudG90YWwgfHwgMDsNCg0KICAgICAgICAgIGNvbnN0IG1hcERhdGEgPSAoZGF0YSkgPT4NCiAgICAgICAgICAgIGRhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IGAkew0KICAgICAgICAgICAgICAgIGl0ZW0uc291cmNlU24gfHwgInVua25vd24iDQogICAgICAgICAgICAgIH1fJHtpbmRleH1fJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkNCiAgICAgICAgICAgICAgICAudG9TdHJpbmcoMzYpDQogICAgICAgICAgICAgICAgLnN1YnN0cmluZygyLCAxMSl9YCwgLy8g56Gu5L+d57ud5a+55ZSv5LiA5oCnDQogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmNuTmFtZSwNCiAgICAgICAgICAgICAgY291bnQ6IGl0ZW0uYXJ0aWNsZUNvdW50IHx8IDAsDQogICAgICAgICAgICAgIG9yZGVyTnVtOiBpdGVtLm9yZGVyTnVtLA0KICAgICAgICAgICAgICBjb3VudHJ5OiBpdGVtLmNvdW50cnlPZk9yaWdpbiB8fCBudWxsLA0KICAgICAgICAgICAgICBzb3VyY2VTbjogaXRlbS5zb3VyY2VTbiwNCiAgICAgICAgICAgICAgdXJsOiBpdGVtLnVybCB8fCBudWxsLA0KICAgICAgICAgICAgfSkpOw0KDQogICAgICAgICAgdGhpcy50cmVlRGF0YVRyYW5zZmVyID0gbWFwRGF0YShkYXRhTGlzdCk7DQogICAgICAgICAgdGhpcy50cmVlVG90YWwgPSB0b3RhbDsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5p+l6K+i5qCR5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W5pWw5o2u5rqQ5aSx6LSlIik7DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5p+l6K+i5paH56ug5YiX6KGo77yI5bim6Ziy5oqW77yJDQogICAgYXN5bmMgcXVlcnlBcnRpY2xlTGlzdChmbGFnKSB7DQogICAgICAvLyDpmLLmraLph43lpI3mn6Xor6INCiAgICAgIGlmICh0aGlzLmlzUXVlcnlpbmcpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAoIWZsYWcpIHsNCiAgICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSB0cnVlOw0KICAgICAgfQ0KDQogICAgICAvLyDmuIXpmaTkuYvliY3nmoTpmLLmipblrprml7blmagNCiAgICAgIGlmICh0aGlzLnF1ZXJ5RGVib3VuY2VUaW1lcikgew0KICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5xdWVyeURlYm91bmNlVGltZXIpOw0KICAgICAgfQ0KDQogICAgICAvLyDorr7nva7pmLLmipbvvIwzMDBtc+WQjuaJp+ihjOafpeivog0KICAgICAgdGhpcy5xdWVyeURlYm91bmNlVGltZXIgPSBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBpZiAoZmxhZyA9PT0gInNvdXJjZUl0ZW1DaGFuZ2VkIikgew0KICAgICAgICAgICAgdGhpcy5nbG9iYWxMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLmlzUXVlcnlpbmcgPSB0cnVlOw0KDQogICAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgICAgbTogMSwNCiAgICAgICAgICAgIHBhZ2VOdW06IHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSwNCiAgICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplLA0KICAgICAgICAgICAgaWQ6IHRoaXMucXVlcnlQYXJhbXMuaWQsDQogICAgICAgICAgICBpc1NvcnQ6IHRoaXMucXVlcnlQYXJhbXMuc29ydE1vZGUsDQogICAgICAgICAgICBkYXRlVHlwZToNCiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kYXRlVHlwZSAhPSA2ID8gdGhpcy5xdWVyeVBhcmFtcy5kYXRlVHlwZSA6ICIiLA0KICAgICAgICAgICAgdGFnczogdGhpcy5xdWVyeVBhcmFtcy50YWdzLA0KICAgICAgICAgICAgdGFnc1N1YnNldDogdGhpcy5xdWVyeVBhcmFtcy50YWdzU3Vic2V0LA0KICAgICAgICAgICAga2V5d29yZHM6IHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMsDQogICAgICAgICAgICBpc1RlY2hub2xvZ3k6IHRoaXMucXVlcnlQYXJhbXMuaXNUZWNobm9sb2d5LA0KICAgICAgICAgICAgZW1vdGlvbjogdGhpcy5xdWVyeVBhcmFtcy5lbW90aW9uLA0KICAgICAgICAgICAgbGFiZWw6IHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldC5qb2luKCIsIiksDQogICAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDAsDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy50YWdzKSB7DQogICAgICAgICAgICBwYXJhbXMudGFnc1N1YnNldCA9IFtdOw0KICAgICAgICAgICAgcGFyYW1zLmxhYmVsID0gIiI7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5p6E5bu65p+l6K+i5Y+C5pWwDQogICAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5zYXZlZENoZWNrYm94RGF0YS5tYXAoKGl0ZW0pID0+IGl0ZW0ubGFiZWwpOw0KICAgICAgICAgICAgY29uc3Qgc291cmNlU24gPSB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLm1hcCgNCiAgICAgICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uc291cmNlU24NCiAgICAgICAgICAgICk7DQoNCiAgICAgICAgICAgIHBhcmFtcy53ZUNoYXROYW1lID0gU3RyaW5nKGRhdGEpOw0KICAgICAgICAgICAgcGFyYW1zLnNvdXJjZVNuID0gU3RyaW5nKHNvdXJjZVNuKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDorrDlvZXlhbPplK7or43ljoblj7INCiAgICAgICAgICBpZiAocGFyYW1zLmtleXdvcmRzKSB7DQogICAgICAgICAgICBhZGRBcnRpY2xlSGlzdG9yeSh7IGtleXdvcmQ6IHBhcmFtcy5rZXl3b3JkcywgdHlwZTogMSB9KS50aGVuKA0KICAgICAgICAgICAgICAoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGFwaS5lc1JldHJpZXZhbChwYXJhbXMpOw0KDQogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgbGV0IGFydGljbGVMaXN0ID0gcmVzLmRhdGEubGlzdA0KICAgICAgICAgICAgICA/IHJlcy5kYXRhLmxpc3QubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgICBpdGVtLmNuVGl0bGUgPSBpdGVtLmNuVGl0bGUNCiAgICAgICAgICAgICAgICAgICAgPyB0aGlzLmNoYW5nZUNvbG9yKGl0ZW0uY25UaXRsZSkNCiAgICAgICAgICAgICAgICAgICAgOiBudWxsOw0KICAgICAgICAgICAgICAgICAgaXRlbS50aXRsZSA9IHRoaXMuY2hhbmdlQ29sb3IoaXRlbS50aXRsZSk7DQogICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbTsNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICA6IFtdOw0KDQogICAgICAgICAgICAvLyDljrvph43pgLvovpHvvJrlj6rmnInlnKjmsqHmnInlhbPplK7or43mkJzntKLml7bmiY3ov5vooYzljrvph40NCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgIXRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMgfHwNCiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5rZXl3b3Jkcy50cmltKCkgPT09ICIiDQogICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgYXJ0aWNsZUxpc3QgPSB0aGlzLmRlZHVwbGljYXRlQXJ0aWNsZXMoYXJ0aWNsZUxpc3QpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0ID0gYXJ0aWNsZUxpc3Q7DQogICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsNCg0KICAgICAgICAgICAgLy8g5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5oGi5aSN6YCJ5Lit54q25oCB77yI6Z2Z6buY5oGi5aSN77yM5LiN6Kem5Y+R5Y+z5L6n5p+l6K+i77yJDQogICAgICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteS4uuepuueahOaDheWGtQ0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Lmxlbmd0aCA9PSAwICYmDQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZVNpemUgKiAodGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtIC0gMSkgPj0NCiAgICAgICAgICAgICAgICB0aGlzLnRvdGFsICYmDQogICAgICAgICAgICAgIHRoaXMudG90YWwgIT0gMA0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IE1hdGgubWF4KA0KICAgICAgICAgICAgICAgIDEsDQogICAgICAgICAgICAgICAgTWF0aC5jZWlsKHRoaXMudG90YWwgLyB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplKQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAvLyDph43mlrDmn6Xor6INCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgICAgICAgIHJldHVybjsgLy8g6YeN5paw5p+l6K+i5pe25LiN6KaB5YWz6ZetbG9hZGluZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgIuiOt+WPluaVsOaNruWksei0pSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmn6Xor6Lmlofnq6DliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIHRoaXMuaXNRdWVyeWluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gZmFsc2U7IC8vIOafpeivouWujOaIkOWQjuWFs+mXrWxvYWRpbmcNCiAgICAgICAgfQ0KICAgICAgfSwgMTAwMCk7DQogICAgfSwNCg0KICAgIC8vIFRyZWVUYWJsZSDnu4Tku7bkuovku7blpITnkIbmlrnms5UNCg0KICAgIC8vIOWkhOeQhumAieaLqeWPmOWMlg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3RlZERhdGEsIG9wZXJhdGlvblR5cGUpIHsNCiAgICAgIGlmIChvcGVyYXRpb25UeXBlID09PSAicm93LWNsaWNrIiB8fCBvcGVyYXRpb25UeXBlID09PSAiY2xlYXItYWxsIikgew0KICAgICAgICAvLyDngrnlh7vooYzvvIjljZXpgInvvInmiJblj5bmtojmiYDmnInpgInkuK3vvJrnm7TmjqXmm7/mjaLvvIzkuI3pnIDopoHov73liqDljrvph40NCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgfSBlbHNlIGlmICgNCiAgICAgICAgb3BlcmF0aW9uVHlwZSA9PT0gImNoZWNrYm94LWNoYW5nZSIgfHwNCiAgICAgICAgb3BlcmF0aW9uVHlwZSA9PT0gInNlbGVjdC1hbGwiDQogICAgICApIHsNCiAgICAgICAgLy8g54K55Ye75Yu+6YCJ5qGG77yI5aSa6YCJ77yJ5oiW5YWo6YCJ77ya6ZyA6KaB5q2j56Gu5aSE55CG6YCJ5Lit5ZKM5Y+W5raI6YCJ5LitDQogICAgICAgIC8vIOWFiOS7juS/neWtmOeahOaVsOaNruS4reenu+mZpOW9k+WJjemhtemdoueahOaJgOacieaVsOaNrg0KICAgICAgICBjb25zdCBjdXJyZW50UGFnZUlkcyA9IHRoaXMudHJlZURhdGFUcmFuc2Zlci5tYXAoDQogICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uc291cmNlU24NCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgZmlsdGVyZWRDaGVja0xpc3QgPSB0aGlzLmNoZWNrTGlzdC5maWx0ZXIoDQogICAgICAgICAgKGl0ZW0pID0+ICFjdXJyZW50UGFnZUlkcy5pbmNsdWRlcyhpdGVtLnNvdXJjZVNuKQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCBmaWx0ZXJlZFNhdmVkRGF0YSA9IHRoaXMuc2F2ZWRDaGVja2JveERhdGEuZmlsdGVyKA0KICAgICAgICAgIChpdGVtKSA9PiAhY3VycmVudFBhZ2VJZHMuaW5jbHVkZXMoaXRlbS5zb3VyY2VTbikNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDnhLblkI7mt7vliqDlvZPliY3pobXpnaLmlrDpgInkuK3nmoTmlbDmja4NCiAgICAgICAgY29uc3QgY29tYmluZWRDaGVja0xpc3QgPSBbLi4uZmlsdGVyZWRDaGVja0xpc3QsIC4uLnNlbGVjdGVkRGF0YV07DQogICAgICAgIGNvbnN0IGNvbWJpbmVkU2F2ZWREYXRhID0gWy4uLmZpbHRlcmVkU2F2ZWREYXRhLCAuLi5zZWxlY3RlZERhdGFdOw0KDQogICAgICAgIC8vIOWvueWQiOW5tuWQjueahOaVsOaNrui/m+ihjOWOu+mHjeWkhOeQhg0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IHRoaXMuZGVkdXBsaWNhdGVCeVNvdXJjZVNuKGNvbWJpbmVkQ2hlY2tMaXN0KTsNCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IHRoaXMuZGVkdXBsaWNhdGVCeVNvdXJjZVNuKGNvbWJpbmVkU2F2ZWREYXRhKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOm7mOiupOaDheWGte+8muebtOaOpeabv+aNou+8iOWFvOWuueaAp+WkhOeQhu+8iQ0KICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gWy4uLnNlbGVjdGVkRGF0YV07DQogICAgICB9DQoNCiAgICAgIC8vIOmHjee9rumhteeggeW5tuafpeivouWGheWuuQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgaWYgKCF0aGlzLmlzUmlnaHRGaWx0ZXIpIHsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCJzb3VyY2VJdGVtQ2hhbmdlZCIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmoLnmja5zb3VyY2VTbuWOu+mHjeeahOi+heWKqeaWueazlQ0KICAgIGRlZHVwbGljYXRlQnlTb3VyY2VTbihkYXRhQXJyYXkpIHsNCiAgICAgIGNvbnN0IHNlZW4gPSBuZXcgU2V0KCk7DQogICAgICByZXR1cm4gZGF0YUFycmF5LmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICBpZiAoc2Vlbi5oYXMoaXRlbS5zb3VyY2VTbikpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgc2Vlbi5hZGQoaXRlbS5zb3VyY2VTbik7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumHjee9rg0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgLy8g5YWI5riF56m66L+H5ruk5paH5pys77yM6YG/5YWN6Kem5Y+RIGhhbmRsZUZpbHRlclNlYXJjaA0KICAgICAgdGhpcy5maWx0ZXJUZXh0ID0gIiI7DQogICAgICB0aGlzLnNlbGVjdGVkQ2xhc3NpZnkgPSBudWxsOw0KDQogICAgICAvLyDnhLblkI7orr7nva7ph43nva7moIforrANCiAgICAgIHRoaXMuaXNMZWZ0UmVzZXQgPSB0cnVlOw0KDQogICAgICAvLyDmuIXnqbrpgInkuK3nirbmgIENCiAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQoNCiAgICAgIC8vIOa4heepuuS/neWtmOeahOWLvumAieaVsOaNru+8iOawuOS5heS/neWtmO+8iQ0KICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IFtdOw0KDQogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LliJfooajmlbDmja4NCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhc0NhY2hlID0gIjEiOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQoNCiAgICAgIC8vIOmHjeaWsOafpeivouagkeWSjOWIl+ihqA0KICAgICAgdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOmHjee9ruagkemAieaLqe+8iOS/neeVmeWOn+aWueazleWQjeS7peWFvOWuue+8iQ0KICAgIHRyZWVDbGVhcigpIHsNCiAgICAgIHRoaXMuaGFuZGxlUmVzZXQoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5qCR5YiG6aG1DQogICAgaGFuZGxlVHJlZUN1cnJlbnRDaGFuZ2UocGFnZSkgew0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSBwYWdlOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIGhhbmRsZVRyZWVQYWdlU2l6ZUNoYW5nZShzaXplKSB7DQogICAgICB0aGlzLnRyZWVQYWdlU2l6ZSA9IHNpemU7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhc0NhY2hlID0gIjEiOw0KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsNCiAgICB9LA0KDQogICAgLy8g5qOA57Si6K+N5bqT5YWo6YCJ5aSE55CGDQogICAgaGFuZGxlQ2hlY2tBbGxUYWdzU3Vic2V0KHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy50YWdzU3Vic2V0ID0gdmFsDQogICAgICAgID8gdGhpcy50YWdzTGlzdC5tYXAoKGl0ZW0pID0+IGl0ZW0ubmFtZSkNCiAgICAgICAgOiBbXTsNCiAgICAgIHRoaXMuaXNJbmRldGVybWluYXRlID0gZmFsc2U7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KDQogICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhui/h+a7pOaQnOe0ou+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJDQogICAgaGFuZGxlRmlsdGVyU2VhcmNoKGtleXdvcmQpIHsNCiAgICAgIGlmICh0aGlzLmlzTGVmdFJlc2V0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw6L+H5ruk5paH5pysDQogICAgICB0aGlzLmZpbHRlclRleHQgPSBrZXl3b3JkIHx8ICIiOw0KDQogICAgICAvLyDph43nva7liLDnrKzkuIDpobUNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFzQ2FjaGUgPSAiMSI7DQoNCiAgICAgIC8vIOiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8iOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8iQ0KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5pWw5o2u5rqQ5YiG57G75Y+Y5YyW77yI5p2l6IeqIFRyZWVUYWJsZSDnu4Tku7bvvIkNCiAgICBoYW5kbGVDbGFzc2lmeUNoYW5nZShjbGFzc2lmeVZhbHVlKSB7DQogICAgICAvLyDmm7TmlrDpgInkuK3nmoTliIbnsbsNCiAgICAgIHRoaXMuc2VsZWN0ZWRDbGFzc2lmeSA9IGNsYXNzaWZ5VmFsdWU7DQoNCiAgICAgIC8vIOmHjee9ruWIsOesrOS4gOmhtQ0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5oYXNDYWNoZSA9ICIxIjsNCg0KICAgICAgLy8g5Y+q6LCD55So5qCR5pWw5o2u5p+l6K+i5o6l5Y+j5bm25oGi5aSN6YCJ5Lit54q25oCB77yM5L2/55So5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2uDQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICAvLyDmo4DntKLor43lupPlpJrpgInlpITnkIYNCiAgICBoYW5kbGVDaGVja2VkQ2hhbmdlKHZhbHVlKSB7DQogICAgICBsZXQgY2hlY2tlZENvdW50ID0gdmFsdWUubGVuZ3RoOw0KICAgICAgdGhpcy5jaGVja0FsbCA9IGNoZWNrZWRDb3VudCA9PT0gdGhpcy50YWdzTGlzdC5sZW5ndGg7DQogICAgICB0aGlzLmlzSW5kZXRlcm1pbmF0ZSA9DQogICAgICAgIGNoZWNrZWRDb3VudCA+IDAgJiYgY2hlY2tlZENvdW50IDwgdGhpcy50YWdzTGlzdC5sZW5ndGg7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KDQogICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgfSwNCg0KICAgIC8vIOaQnOe0ouWkhOeQhg0KICAgIGhhbmRsZVNlYXJjaChmbGFnKSB7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIGlmICghZmxhZykgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOWFs+mUruivjeWOhuWPsumAieaLqQ0KICAgIGtleXdvcmRzQ2hhbmdlKGl0ZW0pIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMgPSBpdGVtLmtleXdvcmQ7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gZmFsc2U7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAvLyB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDlj7PkvqfooajmoLzlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVUYWJsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA9PSB0aGlzLkFydGljbGVMaXN0Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLmNoZWNrZWQgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jaGVja2VkID0gZmFsc2U7DQogICAgICB9DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvLyDlhajpgIkNCiAgICBoYW5kbGVDaGVja0FsbENoYW5nZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy4kcmVmc1sidGFibGUiXS50b2dnbGVBbGxTZWxlY3Rpb24oKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHJlZnNbInRhYmxlIl0uY2xlYXJTZWxlY3Rpb24oKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaJk+W8gOa3u+WKoOWIsOaKpeWRig0KICAgIG9wZW5SZXBvcnQoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fli77pgInopoHmt7vliqDnmoTmlbDmja4iLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy8g56Gu5a6a5re75Yqg5Yiw5oql5ZGKDQogICAgYXN5bmMgcmVwb3J0U3VibWl0KCkgew0KICAgICAgaWYgKCF0aGlzLnJlcG9ydElkKQ0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeimgea3u+WKoOWIsOeahOaKpeWRiiIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIGxldCBrZXlXb3JkTGlzdCA9IHRoaXMuaWRzLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICByZXR1cm4geyByZXBvcnRJZDogdGhpcy5yZXBvcnRJZCwgbGlzdElkOiBpdGVtIH07DQogICAgICB9KTsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBhcGkuQWRkUmVwb3J0KGtleVdvcmRMaXN0KTsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlt7Lmt7vliqDliLDmiqXlkYoiLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIua3u+WKoOWIsOaKpeWRiuWksei0pSzor7fogZTns7vnrqHnkIblkZgiLA0KICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmc1sidGFibGUiXS5jbGVhclNlbGVjdGlvbigpOw0KICAgICAgdGhpcy5jaGVja2VkID0gZmFsc2U7DQogICAgICB0aGlzLmNsb3NlUmVwb3J0KCk7DQogICAgfSwNCiAgICAvLyDlhbPpl63mt7vliqDliLDmiqXlkYoNCiAgICBjbG9zZVJlcG9ydCgpIHsNCiAgICAgIHRoaXMucmVwb3J0SWQgPSAiIjsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQogICAgLy8g5om56YeP5Yig6ZmkDQogICAgYmF0Y2hEZWxldGUoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fli77pgInopoHliKDpmaTnmoTmlbDmja4iLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTlt7Lli77pgInnmoTmlbDmja7pobk/IikNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIEFQSS5iYXRjaFJlbW92ZSh0aGlzLmlkcy5qb2luKCIsIikpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLy8g5re75Yqg5Yiw5Y+w6LSmDQogICAgb3BlblRhaXpoYW5nKCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35Yu+6YCJ6KaB5re75Yqg55qE5pWw5o2uIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5re75Yqg5bey5Yu+6YCJ55qE5pWw5o2u6aG55Yiw5Y+w6LSm57uf6K6hPyIpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBhZGRXb3JrKHRoaXMuaWRzKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAic3VjY2VzcyIsIG1lc3NhZ2U6ICLmt7vliqDmiJDlip8hIiB9KTsNCiAgICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLy8g5Y+R5biD5Yiw5q+P5pel5pyA5paw54Ot54K5DQogICAgcHVibGlzaEhvdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WLvumAieimgeWPkeW4g+WIsOavj+aXpeacgOaWsOeDreeCueeahOaVsOaNriIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWPkeW4g+W3suWLvumAieeahOaVsOaNrumhueWIsOavj+aXpeacgOaWsOeDreeCuT8iKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgQVBJLnB1Ymxpc2hFdmVyeWRheUhvdCh0aGlzLmlkcy5qb2luKCIsIikpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICJzdWNjZXNzIiwgbWVzc2FnZTogIuWPkeW4g+aIkOWKnyEiIH0pOw0KICAgICAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvLyDljoblj7LorrDlvZXnm7jlhbPmlrnms5UNCiAgICBhc3luYyByZW1vdmVIaXN0b3J5KGl0ZW0sIHR5cGUpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgIGF3YWl0IGRlbEFydGljbGVIaXN0b3J5KFtpdGVtLmlkXSk7DQogICAgICBpZiAodHlwZSA9PSAxKSB7DQogICAgICAgIHRoaXMuJHJlZnNbImtleXdvcmRSZWYiXS5mb2N1cygpOw0KICAgICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIHNob3dIaXN0b3J5TGlzdCgpIHsNCiAgICAgIHRoaXMuc2hvd0hpc3RvcnkgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBoaWRlSGlzdG9yeUxpc3QoKSB7DQogICAgICB0aGlzLmhpc3RvcnlUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuc2hvd0hpc3RvcnkgPSBmYWxzZTsNCiAgICAgIH0sIDUwMCk7DQogICAgfSwNCg0KICAgIGdldEFydGljbGVIaXN0b3J5KCkgew0KICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0QXJ0aWNsZUhpc3RvcnkoeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogNSwgdHlwZTogMSB9KS50aGVuKA0KICAgICAgICAocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICB0aGlzLmhpc3RvcnlMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICk7DQogICAgfSwNCg0KICAgIGFzeW5jIGNsZWFySGlzdG9yeSgpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgIHRoaXMuJHJlZnNbImtleXdvcmRSZWYiXS5mb2N1cygpOw0KICAgICAgYXdhaXQgY2xlYW5BcnRpY2xlSGlzdG9yeSgxKTsNCiAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICB9LA0KDQogICAgbW9yZUhpc3RvcnkoKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7DQogICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgZ2V0QXJ0aWNsZUhpc3RvcnkxKCkgew0KICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0QXJ0aWNsZUhpc3RvcnkoeyAuLi50aGlzLnF1ZXJ5UGFyYW1zMSwgdHlwZTogMSB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmhpc3RvcnlMaXN0MSA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwxID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmlofnq6Dor6bmg4UNCiAgICBvcGVuTmV3VmlldyhpdGVtKSB7DQogICAgICB3aW5kb3cub3BlbigNCiAgICAgICAgYC9leHByZXNzRGV0YWlscz9pZD0ke2l0ZW0uaWR9JmRvY0lkPSR7aXRlbS5kb2NJZH0mc291cmNlVHlwZT0ke2l0ZW0uc291cmNlVHlwZX1gLA0KICAgICAgICAiX2JsYW5rIg0KICAgICAgKTsNCiAgICB9LA0KDQogICAgLy8g5qOA5p+l5paH5pys5piv5ZCm5pyJ5a6e6ZmF5YaF5a65DQogICAgaGFzQWN0dWFsQ29udGVudCh0ZXh0KSB7DQogICAgICBpZiAoIXRleHQpIHJldHVybiBmYWxzZTsNCiAgICAgIGNvbnN0IGNvbnRlbnRXaXRob3V0VGFncyA9IHRleHQucmVwbGFjZSgvPFtePl0qPi9nLCAiIik7DQogICAgICByZXR1cm4gL1tcdTRlMDAtXHU5ZmE1YS16QS1aMC05XS8udGVzdChjb250ZW50V2l0aG91dFRhZ3MpOw0KICAgIH0sDQoNCiAgICAvLyDmu5rliqjliLDpobbpg6gNCiAgICBzY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMucmlnaHRNYWluKSB7DQogICAgICAgIHRoaXMuJHJlZnMucmlnaHRNYWluLnNjcm9sbFRvcCA9IDA7DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLiRyZWZzLnRhYmxlKSB7DQogICAgICAgIGNvbnN0IGJvZHlXcmFwcGVyID0gdGhpcy4kcmVmcy50YWJsZS4kZWwucXVlcnlTZWxlY3RvcigNCiAgICAgICAgICAiLmVsLXRhYmxlX19ib2R5LXdyYXBwZXIiDQogICAgICAgICk7DQogICAgICAgIGlmIChib2R5V3JhcHBlcikgew0KICAgICAgICAgIGJvZHlXcmFwcGVyLnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YWz6ZSu5a2X6auY5LquDQogICAgY2hhbmdlQ29sb3Ioc3RyKSB7DQogICAgICBjb25zdCByZWdleCA9IC88aW1nXGJbXj5dKj4vZ2k7DQogICAgICBsZXQgU3RyID0gc3RyICYmIHN0ci5yZXBsYWNlKHJlZ2V4LCAiIik7DQogICAgICBpZiAoDQogICAgICAgIFN0ciAmJg0KICAgICAgICAoKHRoaXMucXVlcnlQYXJhbXMudGFncyAmJg0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCAmJg0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldC5sZW5ndGgpIHx8DQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5rZXl3b3JkcykNCiAgICAgICkgew0KICAgICAgICBsZXQga2V5d29yZHMgPSBbDQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcy50YWdzU3Vic2V0LA0KICAgICAgICAgIC4uLih0aGlzLnF1ZXJ5UGFyYW1zLmtleXdvcmRzDQogICAgICAgICAgICA/IHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMuc3BsaXQoIiwiKQ0KICAgICAgICAgICAgOiBbXSksDQogICAgICAgIF07DQogICAgICAgIGtleXdvcmRzLmZvckVhY2goKGtleWl0ZW0pID0+IHsNCiAgICAgICAgICBpZiAoa2V5aXRlbSAmJiBrZXlpdGVtLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGxldCByZXBsYWNlUmVnID0gbmV3IFJlZ0V4cChrZXlpdGVtLCAiZyIpOw0KICAgICAgICAgICAgbGV0IHJlcGxhY2VTdHJpbmcgPQ0KICAgICAgICAgICAgICAnPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCIgc3R5bGU9ImNvbG9yOiByZWQ7Ij4nICsNCiAgICAgICAgICAgICAga2V5aXRlbSArDQogICAgICAgICAgICAgICI8L3NwYW4+IjsNCiAgICAgICAgICAgIFN0ciA9IFN0ci5yZXBsYWNlKHJlcGxhY2VSZWcsIHJlcGxhY2VTdHJpbmcpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICByZXR1cm4gU3RyOw0KICAgIH0sDQoNCiAgICAvLyDlv6vnhafnlJ/miJANCiAgICByZXN1bHRFdmVudCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nmlofnq6AiKTsNCiAgICAgIH0NCiAgICAgIGxldCBpZHMgPSB0aGlzLmlkczsNCiAgICAgIGxldCB6aHVhbmd0YWkgPSAi55Sf5oiQIjsNCiAgICAgIGxldCB1cmwgPSAiIjsNCiAgICAgIGlmIChpZHMubGVuZ3RoID09IDEpIHsNCiAgICAgICAgbGV0IHJvdyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmlkID09IGlkc1swXSk7DQogICAgICAgIGlmIChyb3cgJiYgcm93LnNuYXBzaG90VXJsKSB6aHVhbmd0YWkgPSAi5p+l55yLIjsNCiAgICAgICAgdXJsID0gcm93LnNuYXBzaG90VXJsOw0KICAgICAgfQ0KICAgICAgaWYgKHpodWFuZ3RhaSA9PSAi55Sf5oiQIikgew0KICAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICBtZXNzYWdlOiAi5b+r54Wn5q2j5Zyo55Sf5oiQ5Lit77yM6K+356iN5ZCO5p+l55yLIiwNCiAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiBmYWxzZSwNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuWFs+mXrSIsDQogICAgICAgICAgYmVmb3JlQ2xvc2U6IChfLCBfXywgZG9uZSkgPT4gew0KICAgICAgICAgICAgZG9uZSgpOw0KICAgICAgICAgIH0sDQogICAgICAgIH0pOw0KICAgICAgICBBUEkuZG93bkxvYWRFeHBvcnRLZShpZHMpDQogICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSAhPSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogIueUs+ivt+Wksei0pe+8jOivt+iBlOezu+euoeeQhuWRmO+8jOehruiupOmHh+mbhuWZqOaYr+WQpuato+W4uCIsDQogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UobmV3IFJlZ0V4cCgiL2hvbWUvbG9jYWwvZHB4L3NlcnZlci1hcGkvIiwgImciKSwgIi8iKTsNCiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UobmV3IFJlZ0V4cCgiL2hvbWUvbG9jYWwvZHB4LyIsICJnIiksICIvIik7DQogICAgICAgIHdpbmRvdy5vcGVuKHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gKyB1cmwsICJfYmxhbmsiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgb3BlblVybCh1cmwpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHVybCwgIl9ibGFuayIpOw0KICAgIH0sDQoNCiAgICAvLyBhaeebuOWFsw0KICAgIC8vIGRpZnkNCiAgICBhc3luYyBkaWZ5QWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5q2j5Zyo6L+b6KGM55qE6K+35rGC77yM5Lit5pat5a6DDQogICAgICBpZiAodGhpcy5pc1JlcXVlc3RpbmcpIHsNCiAgICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOw0KICAgICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygi5Lit5pat5LmL5YmN55qE6K+35rGC5aSx6LSlIiwgZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IHRydWU7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlumAieS4reeahOaWh+eroA0KICAgICAgICBjb25zdCBzZWxlY3RlZEFydGljbGVzID0gdGhpcy5BcnRpY2xlTGlzdC5maWx0ZXIoKGFydGljbGUpID0+DQogICAgICAgICAgdGhpcy5pZHMuaW5jbHVkZXMoYXJ0aWNsZS5pZCkNCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgdGl0bGVzID0gc2VsZWN0ZWRBcnRpY2xlcw0KICAgICAgICAgIC5tYXAoKGFydGljbGUpID0+IGDjgIoke2FydGljbGUuY25UaXRsZSB8fCBhcnRpY2xlLnRpdGxlfeOAi2ApDQogICAgICAgICAgLmpvaW4oIlxuIik7DQoNCiAgICAgICAgLy8g6I635Y+W5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0QnlJZHModGhpcy5pZHMuam9pbigiLCIpKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlc1Jlc3BvbnNlLmRhdGE/Lmxlbmd0aCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6I635Y+W5paH56ug5YaF5a655aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmoLzlvI/ljJbmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNDb250ZW50ID0gYXJ0aWNsZXNSZXNwb25zZS5kYXRhDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0NCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LmNuVGl0bGUgfHwNCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LnRpdGxlIHx8DQogICAgICAgICAgICAgICIiOw0KICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGFydGljbGUuY29udGVudCB8fCAiIjsNCiAgICAgICAgICAgIHJldHVybiBg44CQ56ysICR7aW5kZXggKyAxfSDnr4fmlofnq6DjgJHjgIoke3RpdGxlfeOAi1xuXG4ke2NvbnRlbnR9YDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCJcblxuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4iKTsNCg0KICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga8NCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaCh7DQogICAgICAgICAgcm9sZTogInVzZXIiLA0KICAgICAgICAgIGNvbnRlbnQ6IGDluK7miJHmt7Hluqbop6Por7vku6XkuIske3RoaXMuaWRzLmxlbmd0aH3nr4fmlofnq6DvvJpcbiR7dGl0bGVzfWAsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOWIm+W7ukFJ5raI5oGvDQogICAgICAgIGNvbnN0IGFpTWVzc2FnZSA9IHsNCiAgICAgICAgICByb2xlOiAiYXNzaXN0YW50IiwNCiAgICAgICAgICBjb250ZW50OiAiIiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaChhaU1lc3NhZ2UpOw0KDQogICAgICAgIC8vIOaehOW7uuaPkOekuuivjQ0KICAgICAgICBjb25zdCBwcm9tcHQgPQ0KICAgICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0DQogICAgICAgICAgICAucmVwbGFjZSgiYXJ0aWNsZUxlbmd0aCIsIHRoaXMuaWRzLmxlbmd0aCkNCiAgICAgICAgICAgIC5yZXBsYWNlKC9cJmd0Oy9nLCAiPiIpICsNCiAgICAgICAgICBgKirku6XkuIvmmK/lvoXlpITnkIbnmoTmlofnq6DvvJoqKlxuXG4ke2FydGljbGVzQ29udGVudH1gOw0KDQogICAgICAgIC8vIOiwg+eUqEFJ5o6l5Y+jDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlmeUFpUWEoDQogICAgICAgICAgYXJ0aWNsZXNDb250ZW50LA0KICAgICAgICAgICJzdHJlYW1pbmciLA0KICAgICAgICAgICJkaWZ5LmFydGljbGUuYXBpa2V5Ig0KICAgICAgICApOw0KICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSeaOpeWPo+iwg+eUqOWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IHJlYWRlcjsNCiAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICBsZXQgYnVmZmVyID0gIiI7DQogICAgICAgIGxldCBwZW5kaW5nQnVmZmVyID0gIiI7IC8vIOeUqOS6juWtmOWCqOW+heWkhOeQhueahOS4jeWujOaVtOaVsOaNrg0KICAgICAgICBsZXQgaXNJblRoaW5rVGFnID0gZmFsc2U7IC8vIOaWsOWinu+8muagh+iusOaYr+WQpuWcqHRoaW5r5qCH562+5YaFDQoNCiAgICAgICAgLy8g5bCGVW5pY29kZei9rOS5ieWtl+espihcdVhYWFgp6L2s5o2i5Li65a6e6ZmF5a2X56ymDQogICAgICAgIGNvbnN0IGRlY29kZVVuaWNvZGUgPSAoc3RyKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cXHVbXGRBLUZhLWZdezR9L2csIChtYXRjaCkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIFN0cmluZy5mcm9tQ2hhckNvZGUocGFyc2VJbnQobWF0Y2gucmVwbGFjZSgvXFx1L2csICIiKSwgMTYpKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDmm7TmlrDlhoXlrrnnmoTlh73mlbANCiAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGNvbnN0IHJlbmRlcmVkQ29udGVudCA9IG1hcmtlZChuZXdDb250ZW50LCB0aGlzLm1hcmtkb3duT3B0aW9ucyk7DQogICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IHJlbmRlcmVkQ29udGVudDsNCg0KICAgICAgICAgICAgLy8g56Gu5L+d5raI5oGv5a655Zmo5rua5Yqo5Yiw5bqV6YOoDQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgY2hhdE1lc3NhZ2VzLnNjcm9sbFRvcCA9IGNoYXRNZXNzYWdlcy5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLmuLLmn5PlhoXlrrnml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICBpZiAodGhpcy5pc0Fib3J0ZWQpIHsNCiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7DQoNCiAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgLy8g5aSE55CG5pyA5ZCO5Y+v6IO95Ymp5L2Z55qE5pWw5o2uDQogICAgICAgICAgICBpZiAocGVuZGluZ0J1ZmZlcikgew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGxhc3REYXRhID0gSlNPTi5wYXJzZShwZW5kaW5nQnVmZmVyKTsNCiAgICAgICAgICAgICAgICBpZiAobGFzdERhdGEuYW5zd2VyKSB7DQogICAgICAgICAgICAgICAgICAvLyDop6PnoIFVbmljb2Rl6L2s5LmJ5a2X56ymDQogICAgICAgICAgICAgICAgICBjb25zdCBkZWNvZGVkQW5zd2VyID0gZGVjb2RlVW5pY29kZShsYXN0RGF0YS5hbnN3ZXIpOw0KICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IGRlY29kZWRBbnN3ZXI7DQogICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCLlpITnkIbmnIDlkI7nmoTmlbDmja7ml7blh7rplJk6IiwgZSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgIHBlbmRpbmdCdWZmZXIgKz0gY2h1bms7DQoNCiAgICAgICAgICAvLyDlpITnkIblrozmlbTnmoTmlbDmja7ooYwNCiAgICAgICAgICB3aGlsZSAocGVuZGluZ0J1ZmZlci5pbmNsdWRlcygiXG4iKSkgew0KICAgICAgICAgICAgY29uc3QgbmV3bGluZUluZGV4ID0gcGVuZGluZ0J1ZmZlci5pbmRleE9mKCJcbiIpOw0KICAgICAgICAgICAgY29uc3QgbGluZSA9IHBlbmRpbmdCdWZmZXIuc2xpY2UoMCwgbmV3bGluZUluZGV4KS50cmltKCk7DQogICAgICAgICAgICBwZW5kaW5nQnVmZmVyID0gcGVuZGluZ0J1ZmZlci5zbGljZShuZXdsaW5lSW5kZXggKyAxKTsNCg0KICAgICAgICAgICAgaWYgKCFsaW5lIHx8IGxpbmUgPT09ICJkYXRhOiIgfHwgIWxpbmUuc3RhcnRzV2l0aCgiZGF0YToiKSkgew0KICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGxpbmUuc2xpY2UoNSkudHJpbSgpOw0KICAgICAgICAgICAgICBpZiAoZGF0YSA9PT0gIltET05FXSIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gSlNPTi5wYXJzZShkYXRhKTsNCiAgICAgICAgICAgICAgaWYgKCFqc29uRGF0YS5hbnN3ZXIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOi3s+i/h+eJueauiuWtl+espg0KICAgICAgICAgICAgICBpZiAoanNvbkRhdGEuYW5zd2VyID09PSAiYGBgIiB8fCBqc29uRGF0YS5hbnN3ZXIgPT09ICJtYXJrZG93biIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOino+eggVVuaWNvZGXovazkuYnlrZfnrKYNCiAgICAgICAgICAgICAgbGV0IGFuc3dlciA9IGRlY29kZVVuaWNvZGUoanNvbkRhdGEuYW5zd2VyKTsNCg0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKs8dGhpbms+5byA5aeL5qCH562+DQogICAgICAgICAgICAgIGlmIChhbnN3ZXIuaW5jbHVkZXMoIjx0aGluaz4iKSkgew0KICAgICAgICAgICAgICAgIGlzSW5UaGlua1RhZyA9IHRydWU7DQogICAgICAgICAgICAgICAgY29udGludWU7IC8vIOi3s+i/h+WMheWQqzx0aGluaz7nmoTpg6jliIYNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuWMheWQqzwvdGhpbms+57uT5p2f5qCH562+DQogICAgICAgICAgICAgIGlmIChhbnN3ZXIuaW5jbHVkZXMoIjwvdGhpbms+IikpIHsNCiAgICAgICAgICAgICAgICBpc0luVGhpbmtUYWcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsgLy8g6Lez6L+H5YyF5ZCrPC90aGluaz7nmoTpg6jliIYNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOWPquacieS4jeWcqHRoaW5r5qCH562+5YaF55qE5YaF5a655omN5Lya6KKr5re75Yqg5YiwYnVmZmVy5LitDQogICAgICAgICAgICAgIGlmICghaXNJblRoaW5rVGFnICYmIGFuc3dlcikgew0KICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBhbnN3ZXI7DQogICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi6Kej5p6Q5pWw5o2u6KGM5pe25Ye66ZSZOiIsIHsNCiAgICAgICAgICAgICAgICBsaW5lLA0KICAgICAgICAgICAgICAgIGVycm9yOiBwYXJzZUVycm9yLm1lc3NhZ2UsDQogICAgICAgICAgICAgICAgcGVuZGluZ0J1ZmZlciwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigiQUnop6Por7vlh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsNCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gT2xsYW1hDQogICAgYXN5bmMgb2xsYW1hQWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5q2j5Zyo6L+b6KGM55qE6K+35rGC77yM5Lit5pat5a6DDQogICAgICBpZiAodGhpcy5pc1JlcXVlc3RpbmcpIHsNCiAgICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOw0KICAgICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygi5Lit5pat5LmL5YmN55qE6K+35rGC5aSx6LSlIiwgZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOetieW+heS5i+WJjeeahOivt+axgueKtuaAgea4heeQhuWujOaIkA0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDojrflj5bpgInkuK3nmoTmlofnq6ANCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICAgIHRoaXMuaWRzLmluY2x1ZGVzKGFydGljbGUuaWQpDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgICAubWFwKChhcnRpY2xlKSA9PiBg44CKJHthcnRpY2xlLmNuVGl0bGUgfHwgYXJ0aWNsZS50aXRsZX3jgItgKQ0KICAgICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICAgIC8vIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKHRoaXMuaWRzLmpvaW4oIiwiKSk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIuiOt+WPluaWh+eroOWGheWuueWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC85byP5YyW5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmlkcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBrw0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCg0KICAgICAgICAvLyDmnoTlu7rmj5DnpLror40NCiAgICAgICAgY29uc3QgcHJvbXB0ID0NCiAgICAgICAgICB0aGlzLmFydGljbGVBaVByb21wdA0KICAgICAgICAgICAgLnJlcGxhY2UoImFydGljbGVMZW5ndGgiLCB0aGlzLmlkcy5sZW5ndGgpDQogICAgICAgICAgICAucmVwbGFjZSgvXCZndDsvZywgIj4iKSArDQogICAgICAgICAgYCoq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICAvLyDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG9sbGFtYUFpUWEocHJvbXB0LCB0cnVlKTsNCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LmdldFJlYWRlcigpOw0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7IC8vIOS/neWtmOW9k+WJjeeahCByZWFkZXINCiAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICBsZXQgYnVmZmVyID0gIiI7DQogICAgICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IERhdGUubm93KCk7DQogICAgICAgIGxldCBpc1RoaW5rQ29udGVudCA9IGZhbHNlOw0KICAgICAgICBsZXQgdGVtcEJ1ZmZlciA9ICIiOw0KDQogICAgICAgIC8vIOabtOaWsOWGheWuueeahOWHveaVsA0KICAgICAgICBjb25zdCB1cGRhdGVDb250ZW50ID0gKG5ld0NvbnRlbnQpID0+IHsNCiAgICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IERhdGUubm93KCk7DQogICAgICAgICAgLy8g5o6n5Yi25pu05paw6aKR546H77yM6YG/5YWN6L+H5LqO6aKR57mB55qERE9N5pu05pawDQogICAgICAgICAgaWYgKGN1cnJlbnRUaW1lIC0gbGFzdFVwZGF0ZVRpbWUgPj0gNTApIHsNCiAgICAgICAgICAgIGFpTWVzc2FnZS5jb250ZW50ID0gbmV3Q29udGVudDsNCiAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lID0gY3VycmVudFRpbWU7DQogICAgICAgICAgICAvLyDnoa7kv53mtojmga/lrrnlmajmu5rliqjliLDlupXpg6gNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gdGhpcy4kcmVmcy5jaGF0TWVzc2FnZXM7DQogICAgICAgICAgICAgIGlmIChjaGF0TWVzc2FnZXMpIHsNCiAgICAgICAgICAgICAgICBjaGF0TWVzc2FnZXMuc2Nyb2xsVG9wID0gY2hhdE1lc3NhZ2VzLnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCBwcm9jZXNzU3RyZWFtID0gYXN5bmMgKCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICAgICAgaWYgKHRoaXMuaXNBYm9ydGVkKSB7DQogICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpOw0KICAgICAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgICAgIGlmIChidWZmZXIubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCJcbiIpLmZpbHRlcigobGluZSkgPT4gbGluZS50cmltKCkpOw0KDQogICAgICAgICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykgew0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UobGluZSk7DQogICAgICAgICAgICAgICAgICBpZiAoIWpzb25EYXRhLnJlc3BvbnNlKSBjb250aW51ZTsNCg0KICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBqc29uRGF0YS5yZXNwb25zZTsNCg0KICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H54m55q6K5a2X56ymDQogICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgPT09ICJgYGAiIHx8IHJlc3BvbnNlID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyICs9IHJlc3BvbnNlOw0KDQogICAgICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKvlrozmlbTnmoR0aGlua+agh+etvuWvuQ0KICAgICAgICAgICAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGhpbmtTdGFydEluZGV4ID0gdGVtcEJ1ZmZlci5pbmRleE9mKCI8dGhpbms+Iik7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHRoaW5rRW5kSW5kZXggPSB0ZW1wQnVmZmVyLmluZGV4T2YoIjwvdGhpbms+Iik7DQoNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaW5rU3RhcnRJbmRleCA9PT0gLTEgJiYgdGhpbmtFbmRJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDmsqHmnIl0aGlua+agh+etvu+8jOebtOaOpea3u+WKoOWIsGJ1ZmZlcg0KICAgICAgICAgICAgICAgICAgICAgIGlmICghaXNUaGlua0NvbnRlbnQpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSB0ZW1wQnVmZmVyOw0KICAgICAgICAgICAgICAgICAgICAgICAgLy8g5L2/55SobWFya2Vk5riy5p+TbWFya2Rvd27lhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQobWFya2VkKGJ1ZmZlciwgdGhpcy5tYXJrZG93bk9wdGlvbnMpKTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9ICIiOw0KICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRoaW5rU3RhcnRJbmRleCAhPT0gLTEgJiYgdGhpbmtFbmRJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInlvIDlp4vmoIfnrb7vvIznrYnlvoXnu5PmnZ/moIfnrb4NCiAgICAgICAgICAgICAgICAgICAgICBpc1RoaW5rQ29udGVudCA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaW5rU3RhcnRJbmRleCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSB0ZW1wQnVmZmVyLnN1YnN0cmluZygwLCB0aGlua1N0YXJ0SW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgLy8g5L2/55SobWFya2Vk5riy5p+TbWFya2Rvd27lhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQobWFya2VkKGJ1ZmZlciwgdGhpcy5tYXJrZG93bk9wdGlvbnMpKTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKHRoaW5rU3RhcnRJbmRleCk7DQogICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpbmtTdGFydEluZGV4ID09PSAtMSAmJiB0aGlua0VuZEluZGV4ICE9PSAtMSkgew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOWPquaciee7k+adn+agh+etvu+8jOenu+mZpOS5i+WJjeeahOWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgIGlzVGhpbmtDb250ZW50ID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKHRoaW5rRW5kSW5kZXggKyA4KTsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDmnInlrozmlbTnmoR0aGlua+agh+etvuWvuQ0KICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcoMCwgdGhpbmtTdGFydEluZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua0VuZEluZGV4ICsgOCk7DQogICAgICAgICAgICAgICAgICAgICAgaXNUaGlua0NvbnRlbnQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi5peg5pWI55qESlNPTuihjO+8jOW3sui3s+i/hyIsIHsNCiAgICAgICAgICAgICAgICAgICAgbGluZSwNCiAgICAgICAgICAgICAgICAgICAgZXJyb3I6IHBhcnNlRXJyb3IubWVzc2FnZSwNCiAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKHN0cmVhbUVycm9yKSB7DQogICAgICAgICAgICBpZiAoc3RyZWFtRXJyb3IubWVzc2FnZSA9PT0gIkFib3J0RXJyb3IiKSB7DQogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5aSE55CG5rWB5byP5ZON5bqU5pe25Ye66ZSZOiIsIHN0cmVhbUVycm9yKTsNCiAgICAgICAgICAgIHRocm93IHN0cmVhbUVycm9yOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICBhd2FpdCBwcm9jZXNzU3RyZWFtKCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAvLyDliKTmlq3mmK/lkKbmmK/kuK3mlq3lr7zoh7TnmoTplJnor68NCiAgICAgICAgaWYgKGVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCLor7fmsYLlt7LooqvkuK3mlq0iKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5lcnJvcigiQUnop6Por7vlh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsgLy8g5riF55CG5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICAvLyDlj6rmnInlnKjmsqHmnInooqvkuK3mlq3nmoTmg4XlhrXkuIvmiY3ph43nva7nirbmgIENCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gZGVlcHNlZWsNCiAgICBhc3luYyBkZWVwc2Vla0FpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDnrYnlvoXkuYvliY3nmoTor7fmsYLnirbmgIHmuIXnkIblrozmiJANCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gZmFsc2U7DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICB0aGlzLmlkcy5pbmNsdWRlcyhhcnRpY2xlLmlkKQ0KICAgICAgKTsNCiAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgLm1hcCgoYXJ0aWNsZSkgPT4gYOOAiiR7YXJ0aWNsZS5jblRpdGxlIHx8IGFydGljbGUudGl0bGV944CLYCkNCiAgICAgICAgLmpvaW4oIlxuIik7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGFydGljbGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0QnlJZHModGhpcy5pZHMuam9pbigiLCIpKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlc1Jlc3BvbnNlLmRhdGEgfHwgIWFydGljbGVzUmVzcG9uc2UuZGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkZhaWxlZCB0byBnZXQgYXJ0aWNsZSBjb250ZW50cyIpOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNDb250ZW50ID0gYXJ0aWNsZXNSZXNwb25zZS5kYXRhDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0NCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LmNuVGl0bGUgfHwNCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LnRpdGxlIHx8DQogICAgICAgICAgICAgICIiOw0KICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGFydGljbGUuY29udGVudCB8fCAiIjsNCiAgICAgICAgICAgIHJldHVybiBg44CQ56ysICR7aW5kZXggKyAxfSDnr4fmlofnq6DjgJHjgIoke3RpdGxlfeOAi1xuXG4ke2NvbnRlbnR9YDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCJcblxuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4iKTsNCg0KICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga8NCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaCh7DQogICAgICAgICAgcm9sZTogInVzZXIiLA0KICAgICAgICAgIGNvbnRlbnQ6IGDluK7miJHmt7Hluqbop6Por7vku6XkuIske3RoaXMuaWRzLmxlbmd0aH3nr4fmlofnq6DvvJpcbiR7dGl0bGVzfWAsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOWIm+W7ukFJ5raI5oGv5bm25re75Yqg5Yiw5a+56K+d5LitDQogICAgICAgIGNvbnN0IGFpTWVzc2FnZSA9IHsNCiAgICAgICAgICByb2xlOiAiYXNzaXN0YW50IiwNCiAgICAgICAgICBjb250ZW50OiAiIiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaChhaU1lc3NhZ2UpOw0KICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICAgIGNvbnN0IHByb21wdCA9DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQNCiAgICAgICAgICAgIC5yZXBsYWNlKCJhcnRpY2xlTGVuZ3RoIiwgdGhpcy5pZHMubGVuZ3RoKQ0KICAgICAgICAgICAgLnJlcGxhY2UoL1wmZ3Q7L2csICI+IikgKw0KICAgICAgICAgIGBcblxuKirku6XkuIvmmK/lvoXlpITnkIbnmoTmlofnq6DvvJoqKlxuXG4ke2FydGljbGVzQ29udGVudH1gOw0KDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGVlcHNlZWtBaVFhKHByb21wdCwgdHJ1ZSk7DQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7DQogICAgICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7IC8vIOS/neWtmOW9k+WJjeeahCByZWFkZXINCiAgICAgICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7DQogICAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IERhdGUubm93KCk7DQoNCiAgICAgICAgICBjb25zdCB1cGRhdGVDb250ZW50ID0gKG5ld0NvbnRlbnQpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTsNCiAgICAgICAgICAgIGlmIChjdXJyZW50VGltZSAtIGxhc3RVcGRhdGVUaW1lID49IDUwKSB7DQogICAgICAgICAgICAgIGFpTWVzc2FnZS5jb250ZW50ID0gbmV3Q29udGVudDsNCiAgICAgICAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBjdXJyZW50VGltZTsNCiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICAgIGlmIChjaGF0TWVzc2FnZXMpIHsNCiAgICAgICAgICAgICAgICAgIGNoYXRNZXNzYWdlcy5zY3JvbGxUb3AgPSBjaGF0TWVzc2FnZXMuc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfTsNCg0KICAgICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICAgIGlmICh0aGlzLmlzQWJvcnRlZCkgew0KICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTsNCiAgICAgICAgICAgIGlmIChkb25lKSB7DQogICAgICAgICAgICAgIGlmIChidWZmZXIubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc3QgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSk7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCJcbiIpOw0KDQogICAgICAgICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykgew0KICAgICAgICAgICAgICAgIGlmICghbGluZS50cmltKCkgfHwgIWxpbmUuc3RhcnRzV2l0aCgiZGF0YTogIikpIGNvbnRpbnVlOw0KDQogICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGxpbmUuc2xpY2UoNSk7DQogICAgICAgICAgICAgICAgaWYgKGRhdGEgPT09ICJbRE9ORV0iKSBicmVhazsNCg0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UoZGF0YSk7DQogICAgICAgICAgICAgICAgICBpZiAoanNvbkRhdGEuY2hvaWNlcz8uWzBdPy5kZWx0YT8uY29udGVudCkgew0KICAgICAgICAgICAgICAgICAgICBsZXQgY29udGVudCA9IGpzb25EYXRhLmNob2ljZXNbMF0uZGVsdGEuY29udGVudDsNCg0KICAgICAgICAgICAgICAgICAgICAvLyDot7Pov4fnibnmrorlrZfnrKYNCiAgICAgICAgICAgICAgICAgICAgaWYgKGNvbnRlbnQgPT09ICJgYGAiIHx8IGNvbnRlbnQgPT09ICJtYXJrZG93biIpIHsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBjb250ZW50Ow0KICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikgew0KICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgcGFyc2luZyBKU09OOiIsIHBhcnNlRXJyb3IpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvciBwcm9jZXNzaW5nIGNodW5rOiIsIGUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIlJlcXVlc3QgZmFpbGVkIik7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIC8vIOWIpOaWreaYr+WQpuaYr+S4reaWreWvvOiHtOeahOmUmeivrw0KICAgICAgICBpZiAoZXJyb3IubWVzc2FnZSA9PT0gIkFib3J0RXJyb3IiKSB7DQogICAgICAgICAgY29uc29sZS5sb2coIuivt+axguW3suiiq+S4reaWrSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmVycm9yKCJBSSBDaGF0IEVycm9yOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigiQUnop6Por7vlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgaWYgKHRoaXMuY2hhdE1lc3NhZ2VzWzFdKSB7DQogICAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXNbMV0uY29udGVudCA9ICLmirHmrYnvvIzmnI3liqHlmajnuYHlv5nvvIzor7fnqI3lkI7lho3or5UiOw0KICAgICAgICB9DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOyAvLyDmuIXnkIblvZPliY3nmoQgcmVhZGVyDQogICAgICAgIC8vIOWPquacieWcqOayoeacieiiq+S4reaWreeahOaDheWGteS4i+aJjemHjee9rueKtuaAgQ0KICAgICAgICBpZiAodGhpcy5haURpYWxvZ1Zpc2libGUpIHsNCiAgICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDlhbPpl61BSeWvueivnQ0KICAgIGNsb3NlQWlEaWFsb2coKSB7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7IC8vIOiuvue9ruS4reaWreagh+W/lw0KICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7IC8vIOS4reaWreW9k+WJjeeahOivu+WPlg0KICAgICAgfQ0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOw0KICAgIH0sDQogICAgYXJ0aWNsZUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmFpUGxhdGZvcm0gPT09ICJkaWZ5Iikgew0KICAgICAgICB0aGlzLmRpZnlBaUNoYXQoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5haVBsYXRmb3JtID09PSAib2xsYW1hIikgew0KICAgICAgICB0aGlzLm9sbGFtYUFpQ2hhdCgpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmFpUGxhdGZvcm0gPT09ICJkZWVwc2VlayIpIHsNCiAgICAgICAgdGhpcy5kZWVwc2Vla0FpQ2hhdCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgY2hhcnRBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGlmeSIpIHsNCiAgICAgICAgdGhpcy5kaWZ5Q2hhcnRBaUNoYXQoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGVlcHNlZWsiKSB7DQogICAgICAgIHRoaXMuZGVlcHNlZWtDaGFydEFpQ2hhdCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gZGlmeeWbvuihqOeci+advw0KICAgIGFzeW5jIGRpZnlDaGFydEFpQ2hhdCgpIHsNCiAgICAgIC8vIOWPguaVsOajgOafpQ0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPiAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi55Sf5oiQRGVlcHNlZWvlm77ooajnnIvmnb/lj6rog73pgInmi6nkuIDnr4flhoXlrrkiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmmL7npLrlr7nor53moYbkuI7liqDovb3nirbmgIENCiAgICAgIHRoaXMuY2hhcnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gdHJ1ZTsNCg0KICAgICAgLy8g56Gu5L+d5riF56m65LiK5qyh55qE5YaF5a65DQogICAgICBpZiAodGhpcy4kcmVmcy5jaGFydENvbnRlbnQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5jaGFydENvbnRlbnQuaW5uZXJIVE1MID0gIiI7DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIDEuIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlUmVzdWx0ID0gYXdhaXQgQVBJLkFyZWFJbmZvKHRoaXMuaWRzWzBdKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlUmVzdWx0LmRhdGEgfHwgIWFydGljbGVSZXN1bHQuZGF0YS5jb250ZW50KSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLojrflj5bmlofnq6DlhoXlrrnlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIDIuIOiwg+eUqEFJ5o6l5Y+jDQogICAgICAgIGNvbnN0IGFpUmVzdWx0ID0gYXdhaXQgZGlmeUFpUWEoDQogICAgICAgICAgYXJ0aWNsZVJlc3VsdC5kYXRhLmNvbnRlbnQsDQogICAgICAgICAgImJsb2NraW5nIiwNCiAgICAgICAgICAiZGlmeS5jaGFydC5hcGlrZXkiDQogICAgICAgICk7DQoNCiAgICAgICAgaWYgKCFhaVJlc3VsdC5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGFpRGF0YSA9IGF3YWl0IGFpUmVzdWx0Lmpzb24oKTsNCiAgICAgICAgaWYgKCFhaURhdGEgfHwgIWFpRGF0YS5hbnN3ZXIpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ6L+U5Zue5pWw5o2u5qC85byP6ZSZ6K+vIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyAzLiDlpITnkIZIVE1M5YaF5a65DQogICAgICAgIGxldCBjb250ZW50MiA9ICIiOw0KDQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g5bCd6K+V6Kej5p6QSlNPTuagvOW8j++8iOacieS6m+i/lOWbnuWPr+iDveaYr0pTT07lrZfnrKbkuLLvvIkNCiAgICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gSlNPTi5wYXJzZShhaURhdGEuYW5zd2VyKTsNCiAgICAgICAgICBjb250ZW50MiA9DQogICAgICAgICAgICBwYXJzZWREYXRhLmFuc3dlciB8fA0KICAgICAgICAgICAgcGFyc2VkRGF0YS5odG1sIHx8DQogICAgICAgICAgICBwYXJzZWREYXRhLmNvbnRlbnQgfHwNCiAgICAgICAgICAgIGFpRGF0YS5hbnN3ZXI7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAvLyDlpoLmnpzkuI3mmK9KU09O5qC85byP77yM55u05o6l5L2/55So5Y6f5aeL5YaF5a65DQogICAgICAgICAgY29udGVudDIgPSBhaURhdGEuYW5zd2VyOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5oCd6ICD5qCH6K6wDQogICAgICAgIGNvbnN0IHRoaW5rU3RhcnRJbmRleCA9IGNvbnRlbnQyLmluZGV4T2YoIjx0aGluaz4iKTsNCiAgICAgICAgY29uc3QgdGhpbmtFbmRJbmRleCA9IGNvbnRlbnQyLmluZGV4T2YoIjwvdGhpbms+Iik7DQoNCiAgICAgICAgLy8g5o+Q5Y+W5pyJ5pWI5YaF5a65DQogICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggIT09IC0xICYmIHRoaW5rRW5kSW5kZXggIT09IC0xKSB7DQogICAgICAgICAgLy8g5aaC5p6c5a2Y5Zyo5oCd6ICD5qCH6K6w77yM5Y+q5Y+WPC90aGluaz7lkI7pnaLnmoTlhoXlrrkNCiAgICAgICAgICBjb250ZW50MiA9IGNvbnRlbnQyLnN1YnN0cmluZyh0aGlua0VuZEluZGV4ICsgOCkudHJpbSgpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5riF55CGaHRtbOagh+iusOWSjOWFtuS7lueJueauiuWtl+espg0KICAgICAgICBjb250ZW50MiA9IGNvbnRlbnQyDQogICAgICAgICAgLy8g56e76ZmkaHRtbOS7o+eggeWdl+agh+iusA0KICAgICAgICAgIC5yZXBsYWNlKC9gYGBodG1sXHMqfGBgYFxzKi9nLCAiIikNCiAgICAgICAgICAvLyDnp7vpmaTlj6/og73lrZjlnKjnmoTlhbbku5ZodG1s6K+t6KiA5qCH6K6w77yM5aaCYGBganNvbuetiQ0KICAgICAgICAgIC5yZXBsYWNlKC9gYGBbYS16QS1aXSpccyovZywgIiIpDQogICAgICAgICAgLy8g56e76Zmk5aSa5L2Z55qE56m66KGMDQogICAgICAgICAgLnJlcGxhY2UoL1xuXHMqXG5ccypcbi9nLCAiXG5cbiIpDQogICAgICAgICAgLy8g56e76Zmk6KGM6aaW6KGM5bC+56m655m95a2X56ymDQogICAgICAgICAgLnRyaW0oKTsNCg0KICAgICAgICAvLyDnoa7kv53lhoXlrrnpnZ7nqboNCiAgICAgICAgaWYgKCFjb250ZW50MiB8fCBjb250ZW50Mi5sZW5ndGggPCAxMCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6L+U5Zue55qE5Zu+6KGo5YaF5a655peg5pWIIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmo4Dmn6VIVE1M57uT5p6E55qE5a6M5pW05oCn77yM5aaC5p6c5LiN5a6M5pW05YiZ5re75Yqg5b+F6KaB55qE5qCH562+DQogICAgICAgIC8vIOi/meaYr+S4uuS6huWFvOWuueWPr+iDveS4jei/lOWbnuWujOaVtEhUTUznmoTmg4XlhrUNCiAgICAgICAgbGV0IGZpbmFsSHRtbCA9IGNvbnRlbnQyOw0KDQogICAgICAgIC8vIOWwhuWQhOenjeW9ouW8j+eahOWklumDqENETuW8leeUqOabv+aNouS4uuacrOWcsOaWh+S7tg0KICAgICAgICAvLyDmm7/mjaLlj4zlvJXlj7fniYjmnKwNCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL2h0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzL2csDQogICAgICAgICAgIi9jaGFydC5qcyINCiAgICAgICAgKTsNCiAgICAgICAgLy8g5pu/5o2i5Y2V5byV5Y+354mI5pysDQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9cJ2h0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzXCcvZywNCiAgICAgICAgICAiJy9jaGFydC5qcyciDQogICAgICAgICk7DQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9cImh0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzXCIvZywNCiAgICAgICAgICAnIi9jaGFydC5qcyInDQogICAgICAgICk7DQogICAgICAgIC8vIOabv+aNouWPr+iDveW4puacieeJiOacrOWPt+eahOW8leeUqA0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvaHR0cHM6XC9cL2NkblwuanNkZWxpdnJcLm5ldFwvbnBtXC9jaGFydFwuanNAXGQrXC5cZCtcLlxkKy9nLA0KICAgICAgICAgICIvY2hhcnQuanMiDQogICAgICAgICk7DQogICAgICAgIC8vIOabv+aNouWFtuS7luWPr+iDveeahENETg0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvaHR0cHM6XC9cL2NkbmpzXC5jbG91ZGZsYXJlXC5jb21cL2FqYXhcL2xpYnNcL0NoYXJ0XC5qc1wvXGQrXC5cZCtcLlxkK1wvY2hhcnQoXC5taW4pP1wuanMvZywNCiAgICAgICAgICAiL2NoYXJ0LmpzIg0KICAgICAgICApOw0KDQogICAgICAgIGlmICghZmluYWxIdG1sLmluY2x1ZGVzKCI8IURPQ1RZUEUiKSAmJiAhZmluYWxIdG1sLmluY2x1ZGVzKCI8aHRtbCIpKSB7DQogICAgICAgICAgLy8g5YaF5a655Y+q5pivSFRNTOeJh+aute+8jOmcgOimgea3u+WKoOWujOaVtOe7k+aehA0KICAgICAgICAgIGZpbmFsSHRtbCA9DQogICAgICAgICAgICAiPCFET0NUWVBFIGh0bWw+IiArDQogICAgICAgICAgICAiPGh0bWw+IiArDQogICAgICAgICAgICAiPGhlYWQ+IiArDQogICAgICAgICAgICAnICA8bWV0YSBjaGFyc2V0PSJVVEYtOCI+JyArDQogICAgICAgICAgICAnICA8bWV0YSBuYW1lPSJ2aWV3cG9ydCIgY29udGVudD0id2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCI+JyArDQogICAgICAgICAgICAnICA8c2NyaXB0IHNyYz0iL2NoYXJ0LmpzIj48XC9zY3JpcHQ+JyArDQogICAgICAgICAgICAiPC9oZWFkPiIgKw0KICAgICAgICAgICAgIjxib2R5PiIgKw0KICAgICAgICAgICAgIiAgIiArDQogICAgICAgICAgICBjb250ZW50MiArDQogICAgICAgICAgICAiPC9ib2R5PiIgKw0KICAgICAgICAgICAgIjwvaHRtbD4iOw0KICAgICAgICB9IGVsc2UgaWYgKA0KICAgICAgICAgICFmaW5hbEh0bWwuaW5jbHVkZXMoIjxzY3JpcHQiKSAmJg0KICAgICAgICAgIGZpbmFsSHRtbC5pbmNsdWRlcygiPGNhbnZhcyIpDQogICAgICAgICkgew0KICAgICAgICAgIC8vIOaciWNhbnZhc+S9huayoeaciXNjcmlwdOagh+etvu+8jOWPr+iDvee8uuWwkUNoYXJ0Lmpz5byV55SoDQogICAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgICAiPGhlYWQ+IiwNCiAgICAgICAgICAgICI8aGVhZD4iICsgJyAgPHNjcmlwdCBzcmM9Ii9jaGFydC5qcyI+PFwvc2NyaXB0PicNCiAgICAgICAgICApOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8gNC4g5Yib5bu6aWZyYW1l5bm25riy5p+TDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBpZiAodGhpcy4kcmVmcy5jaGFydENvbnRlbnQpIHsNCiAgICAgICAgICAgIC8vIOa4heeQhuS5i+WJjeeahGlmcmFtZQ0KICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudENoYXJ0SWZyYW1lKSB7DQogICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUub25sb2FkID0gbnVsbDsNCiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5vbmVycm9yID0gbnVsbDsNCiAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIua4heeQhuS5i+WJjeeahGlmcmFtZeWksei0pToiLCBlKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDliJvlu7ppZnJhbWUNCiAgICAgICAgICAgIGNvbnN0IGlmcmFtZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImlmcmFtZSIpOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLndpZHRoID0gIjEwMCUiOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLmhlaWdodCA9ICI2MDBweCI7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUuYm9yZGVyID0gIm5vbmUiOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLmRpc3BsYXkgPSAiYmxvY2siOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLm92ZXJmbG93ID0gImF1dG8iOw0KDQogICAgICAgICAgICAvLyDkv53lrZhpZnJhbWXlvJXnlKgNCiAgICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lID0gaWZyYW1lOw0KDQogICAgICAgICAgICAvLyDmuIXnqbrlrrnlmajlubbmt7vliqBpZnJhbWUNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmlubmVySFRNTCA9ICIiOw0KICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFydENvbnRlbnQuYXBwZW5kQ2hpbGQoaWZyYW1lKTsNCg0KICAgICAgICAgICAgLy8g5ZyoaWZyYW1l5Yqg6L295a6M5oiQ5ZCO6YeN5paw5omn6KGM6ISa5pys5bm26ZqQ6JeP5Yqg6L2954q25oCBDQogICAgICAgICAgICBpZnJhbWUub25sb2FkID0gKCkgPT4gew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpumcgOimgeWKoOi9veacrOWcsENoYXJ0LmpzDQogICAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgICAgIWlmcmFtZS5jb250ZW50V2luZG93LkNoYXJ0ICYmDQogICAgICAgICAgICAgICAgICAhaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQucXVlcnlTZWxlY3RvcigNCiAgICAgICAgICAgICAgICAgICAgJ3NjcmlwdFtzcmMqPSJjaGFydC5qcyIgaV0nDQogICAgICAgICAgICAgICAgICApDQogICAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgICAvLyDlpoLmnpxpZnJhbWXlhoXmsqHmnInliqDovb1DaGFydC5qc++8jOaJi+WKqOa3u+WKoOacrOWcsENoYXJ0LmpzDQogICAgICAgICAgICAgICAgICBjb25zdCBjaGFydFNjcmlwdCA9DQogICAgICAgICAgICAgICAgICAgIGlmcmFtZS5jb250ZW50V2luZG93LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoInNjcmlwdCIpOw0KICAgICAgICAgICAgICAgICAgY2hhcnRTY3JpcHQuc3JjID0gIi9jaGFydC5qcyI7DQogICAgICAgICAgICAgICAgICBpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKGNoYXJ0U2NyaXB0KTsNCg0KICAgICAgICAgICAgICAgICAgLy8g562J5b6FQ2hhcnQuanPliqDovb3lrozmiJDlkI7lho3miafooYzlkI7nu63ohJrmnKwNCiAgICAgICAgICAgICAgICAgIGNoYXJ0U2NyaXB0Lm9ubG9hZCA9ICgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5leGVjdXRlSWZyYW1lU2NyaXB0cyhpZnJhbWUpOw0KICAgICAgICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c6ISa5pys5Yqg6L295aSx6LSl77yM5Lmf6ZyA6KaB6ZqQ6JeP5Yqg6L295Yqo55S7DQogICAgICAgICAgICAgICAgICBjaGFydFNjcmlwdC5vbmVycm9yID0gKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3mnKzlnLBDaGFydC5qc+Wksei0pSIpOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgLy8g55u05o6l5omn6KGM5YaF6IGU6ISa5pysDQogICAgICAgICAgICAgICAgICB0aGlzLmV4ZWN1dGVJZnJhbWVTY3JpcHRzKGlmcmFtZSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6ISa5pys5omn6KGM5Ye66ZSZOiIsIGUpOw0KICAgICAgICAgICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgIC8vIOa3u+WKoOmUmeivr+WkhOeQhg0KICAgICAgICAgICAgaWZyYW1lLm9uZXJyb3IgPSAoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoImlmcmFtZeWKoOi9veWksei0pSIpOw0KICAgICAgICAgICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgLy8g5YaZ5YWl5YaF5a65DQogICAgICAgICAgICBjb25zdCBkb2MgPSBpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudDsNCiAgICAgICAgICAgIGRvYy5vcGVuKCk7DQogICAgICAgICAgICBkb2Mud3JpdGUoZmluYWxIdG1sKTsNCiAgICAgICAgICAgIGRvYy5jbG9zZSgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLnlJ/miJDlm77ooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIueUn+aIkOWbvuihqOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICB0aGlzLmNsb3NlQ2hhcnREaWFsb2coKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGRlZXBzZWVr5Zu+6KGo55yL5p2/DQogICAgYXN5bmMgZGVlcHNlZWtDaGFydEFpQ2hhdCgpIHsNCiAgICAgIC8vIOWPguaVsOajgOafpQ0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPiAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi55Sf5oiQRGVlcHNlZWvlm77ooajnnIvmnb/lj6rog73pgInmi6nkuIDnr4flhoXlrrkiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmmL7npLrlr7nor53moYbkuI7liqDovb3nirbmgIENCiAgICAgIHRoaXMuY2hhcnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gdHJ1ZTsNCg0KICAgICAgLy8g56Gu5L+d5riF56m65LiK5qyh55qE5YaF5a65DQogICAgICBpZiAodGhpcy4kcmVmcy5jaGFydENvbnRlbnQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5jaGFydENvbnRlbnQuaW5uZXJIVE1MID0gIiI7DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIDEuIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlUmVzdWx0ID0gYXdhaXQgQVBJLkFyZWFJbmZvKHRoaXMuaWRzWzBdKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlUmVzdWx0LmRhdGEgfHwgIWFydGljbGVSZXN1bHQuZGF0YS5jb250ZW50KSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLojrflj5bmlofnq6DlhoXlrrnlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHByb21wdCA9IHRoaXMuY2hhcnRQcm9tcHQgKyBgXG5cbiR7YXJ0aWNsZVJlc3VsdC5kYXRhLmNvbnRlbnR9YDsNCg0KICAgICAgICAvLyAyLiDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCBhaVJlc3VsdCA9IGF3YWl0IGRlZXBzZWVrQWlRYShwcm9tcHQsIGZhbHNlKTsNCg0KICAgICAgICBpZiAoIWFpUmVzdWx0Lm9rKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSeaOpeWPo+iwg+eUqOWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgYWlEYXRhID0gYXdhaXQgYWlSZXN1bHQuanNvbigpOw0KICAgICAgICBpZiAoIWFpRGF0YSB8fCAhYWlEYXRhLmNob2ljZXMpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ6L+U5Zue5pWw5o2u5qC85byP6ZSZ6K+vIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyAzLiDlpITnkIZIVE1M5YaF5a65DQogICAgICAgIGxldCBjb250ZW50MiA9IGFpRGF0YS5jaG9pY2VzWzBdLm1lc3NhZ2UuY29udGVudDsNCg0KICAgICAgICAvLyDmuIXnkIZodG1s5qCH6K6w5ZKM5YW25LuW54m55q6K5a2X56ymDQogICAgICAgIGNvbnRlbnQyID0gY29udGVudDINCiAgICAgICAgICAvLyDnp7vpmaRodG1s5Luj56CB5Z2X5qCH6K6wDQogICAgICAgICAgLnJlcGxhY2UoL2BgYGh0bWxccyp8YGBgXHMqL2csICIiKQ0KICAgICAgICAgIC8vIOenu+mZpOWPr+iDveWtmOWcqOeahOWFtuS7lmh0bWzor63oqIDmoIforrDvvIzlpoJgYGBqc29u562JDQogICAgICAgICAgLnJlcGxhY2UoL2BgYFthLXpBLVpdKlxzKi9nLCAiIikNCiAgICAgICAgICAvLyDnp7vpmaTlpJrkvZnnmoTnqbrooYwNCiAgICAgICAgICAucmVwbGFjZSgvXG5ccypcblxzKlxuL2csICJcblxuIikNCiAgICAgICAgICAvLyDnp7vpmaTooYzpppbooYzlsL7nqbrnmb3lrZfnrKYNCiAgICAgICAgICAudHJpbSgpOw0KDQogICAgICAgIC8vIOehruS/neWGheWuuemdnuepug0KICAgICAgICBpZiAoIWNvbnRlbnQyIHx8IGNvbnRlbnQyLmxlbmd0aCA8IDEwKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLov5Tlm57nmoTlm77ooajlhoXlrrnml6DmlYgiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOajgOafpUhUTUznu5PmnoTnmoTlrozmlbTmgKfvvIzlpoLmnpzkuI3lrozmlbTliJnmt7vliqDlv4XopoHnmoTmoIfnrb4NCiAgICAgICAgLy8g6L+Z5piv5Li65LqG5YW85a655Y+v6IO95LiN6L+U5Zue5a6M5pW0SFRNTOeahOaDheWGtQ0KICAgICAgICBsZXQgZmluYWxIdG1sID0gY29udGVudDI7DQoNCiAgICAgICAgLy8g5bCG5ZCE56eN5b2i5byP55qE5aSW6YOoQ0RO5byV55So5pu/5o2i5Li65pys5Zyw5paH5Lu2DQogICAgICAgIC8vIOabv+aNouWPjOW8leWPt+eJiOacrA0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvaHR0cHM6XC9cL2NkblwuanNkZWxpdnJcLm5ldFwvbnBtXC9jaGFydFwuanMvZywNCiAgICAgICAgICAiL2NoYXJ0LmpzIg0KICAgICAgICApOw0KICAgICAgICAvLyDmm7/mjaLljZXlvJXlj7fniYjmnKwNCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL1wnaHR0cHM6XC9cL2NkblwuanNkZWxpdnJcLm5ldFwvbnBtXC9jaGFydFwuanNcJy9nLA0KICAgICAgICAgICInL2NoYXJ0LmpzJyINCiAgICAgICAgKTsNCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL1wiaHR0cHM6XC9cL2NkblwuanNkZWxpdnJcLm5ldFwvbnBtXC9jaGFydFwuanNcIi9nLA0KICAgICAgICAgICciL2NoYXJ0LmpzIicNCiAgICAgICAgKTsNCiAgICAgICAgLy8g5pu/5o2i5Y+v6IO95bim5pyJ54mI5pys5Y+355qE5byV55SoDQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9odHRwczpcL1wvY2RuXC5qc2RlbGl2clwubmV0XC9ucG1cL2NoYXJ0XC5qc0BcZCtcLlxkK1wuXGQrL2csDQogICAgICAgICAgIi9jaGFydC5qcyINCiAgICAgICAgKTsNCiAgICAgICAgLy8g5pu/5o2i5YW25LuW5Y+v6IO955qEQ0RODQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9odHRwczpcL1wvY2RuanNcLmNsb3VkZmxhcmVcLmNvbVwvYWpheFwvbGlic1wvQ2hhcnRcLmpzXC9cZCtcLlxkK1wuXGQrXC9jaGFydChcLm1pbik/XC5qcy9nLA0KICAgICAgICAgICIvY2hhcnQuanMiDQogICAgICAgICk7DQoNCiAgICAgICAgaWYgKCFmaW5hbEh0bWwuaW5jbHVkZXMoIjwhRE9DVFlQRSIpICYmICFmaW5hbEh0bWwuaW5jbHVkZXMoIjxodG1sIikpIHsNCiAgICAgICAgICAvLyDlhoXlrrnlj6rmmK9IVE1M54mH5q6177yM6ZyA6KaB5re75Yqg5a6M5pW057uT5p6EDQogICAgICAgICAgZmluYWxIdG1sID0NCiAgICAgICAgICAgICI8IURPQ1RZUEUgaHRtbD4iICsNCiAgICAgICAgICAgICI8aHRtbD4iICsNCiAgICAgICAgICAgICI8aGVhZD4iICsNCiAgICAgICAgICAgICcgIDxtZXRhIGNoYXJzZXQ9IlVURi04Ij4nICsNCiAgICAgICAgICAgICcgIDxtZXRhIG5hbWU9InZpZXdwb3J0IiBjb250ZW50PSJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MS4wIj4nICsNCiAgICAgICAgICAgICcgIDxzY3JpcHQgc3JjPSIvY2hhcnQuanMiPjxcL3NjcmlwdD4nICsNCiAgICAgICAgICAgICI8L2hlYWQ+IiArDQogICAgICAgICAgICAiPGJvZHk+IiArDQogICAgICAgICAgICAiICAiICsNCiAgICAgICAgICAgIGNvbnRlbnQyICsNCiAgICAgICAgICAgICI8L2JvZHk+IiArDQogICAgICAgICAgICAiPC9odG1sPiI7DQogICAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgICAgIWZpbmFsSHRtbC5pbmNsdWRlcygiPHNjcmlwdCIpICYmDQogICAgICAgICAgZmluYWxIdG1sLmluY2x1ZGVzKCI8Y2FudmFzIikNCiAgICAgICAgKSB7DQogICAgICAgICAgLy8g5pyJY2FudmFz5L2G5rKh5pyJc2NyaXB05qCH562+77yM5Y+v6IO957y65bCRQ2hhcnQuanPlvJXnlKgNCiAgICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAgICI8aGVhZD4iLA0KICAgICAgICAgICAgIjxoZWFkPiIgKyAnICA8c2NyaXB0IHNyYz0iL2NoYXJ0LmpzIj48XC9zY3JpcHQ+Jw0KICAgICAgICAgICk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyA0LiDliJvlu7ppZnJhbWXlubbmuLLmn5MNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLmNoYXJ0Q29udGVudCkgew0KICAgICAgICAgICAgLy8g5riF55CG5LmL5YmN55qEaWZyYW1lDQogICAgICAgICAgICBpZiAodGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUpIHsNCiAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5vbmxvYWQgPSBudWxsOw0KICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLm9uZXJyb3IgPSBudWxsOw0KICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5riF55CG5LmL5YmN55qEaWZyYW1l5aSx6LSlOiIsIGUpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWIm+W7umlmcmFtZQ0KICAgICAgICAgICAgY29uc3QgaWZyYW1lID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiaWZyYW1lIik7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUud2lkdGggPSAiMTAwJSI7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUuaGVpZ2h0ID0gIjYwMHB4IjsNCiAgICAgICAgICAgIGlmcmFtZS5zdHlsZS5ib3JkZXIgPSAibm9uZSI7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUuZGlzcGxheSA9ICJibG9jayI7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUub3ZlcmZsb3cgPSAiYXV0byI7DQoNCiAgICAgICAgICAgIC8vIOS/neWtmGlmcmFtZeW8leeUqA0KICAgICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUgPSBpZnJhbWU7DQoNCiAgICAgICAgICAgIC8vIOa4heepuuWuueWZqOW5tua3u+WKoGlmcmFtZQ0KICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFydENvbnRlbnQuaW5uZXJIVE1MID0gIiI7DQogICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJ0Q29udGVudC5hcHBlbmRDaGlsZChpZnJhbWUpOw0KDQogICAgICAgICAgICAvLyDlnKhpZnJhbWXliqDovb3lrozmiJDlkI7ph43mlrDmiafooYzohJrmnKzlubbpmpDol4/liqDovb3nirbmgIENCiAgICAgICAgICAgIGlmcmFtZS5vbmxvYWQgPSAoKSA9PiB7DQogICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm6ZyA6KaB5Yqg6L295pys5ZywQ2hhcnQuanMNCiAgICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgICAhaWZyYW1lLmNvbnRlbnRXaW5kb3cuQ2hhcnQgJiYNCiAgICAgICAgICAgICAgICAgICFpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudC5xdWVyeVNlbGVjdG9yKA0KICAgICAgICAgICAgICAgICAgICAnc2NyaXB0W3NyYyo9ImNoYXJ0LmpzIiBpXScNCiAgICAgICAgICAgICAgICAgICkNCiAgICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICAgIC8vIOWmguaenGlmcmFtZeWGheayoeacieWKoOi9vUNoYXJ0Lmpz77yM5omL5Yqo5re75Yqg5pys5ZywQ2hhcnQuanMNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGNoYXJ0U2NyaXB0ID0NCiAgICAgICAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgic2NyaXB0Iik7DQogICAgICAgICAgICAgICAgICBjaGFydFNjcmlwdC5zcmMgPSAiL2NoYXJ0LmpzIjsNCiAgICAgICAgICAgICAgICAgIGlmcmFtZS5jb250ZW50V2luZG93LmRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoY2hhcnRTY3JpcHQpOw0KDQogICAgICAgICAgICAgICAgICAvLyDnrYnlvoVDaGFydC5qc+WKoOi9veWujOaIkOWQjuWGjeaJp+ihjOWQjue7reiEmuacrA0KICAgICAgICAgICAgICAgICAgY2hhcnRTY3JpcHQub25sb2FkID0gKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmV4ZWN1dGVJZnJhbWVTY3JpcHRzKGlmcmFtZSk7DQogICAgICAgICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICAgICAgICAvLyDlpoLmnpzohJrmnKzliqDovb3lpLHotKXvvIzkuZ/pnIDopoHpmpDol4/liqDovb3liqjnlLsNCiAgICAgICAgICAgICAgICAgIGNoYXJ0U2NyaXB0Lm9uZXJyb3IgPSAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWKoOi9veacrOWcsENoYXJ0Lmpz5aSx6LSlIik7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAvLyDnm7TmjqXmiafooYzlhoXogZTohJrmnKwNCiAgICAgICAgICAgICAgICAgIHRoaXMuZXhlY3V0ZUlmcmFtZVNjcmlwdHMoaWZyYW1lKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLohJrmnKzmiafooYzlh7rplJk6IiwgZSk7DQogICAgICAgICAgICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgLy8g5re75Yqg6ZSZ6K+v5aSE55CGDQogICAgICAgICAgICBpZnJhbWUub25lcnJvciA9ICgpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigiaWZyYW1l5Yqg6L295aSx6LSlIik7DQogICAgICAgICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICAvLyDlhpnlhaXlhoXlrrkNCiAgICAgICAgICAgIGNvbnN0IGRvYyA9IGlmcmFtZS5jb250ZW50V2luZG93LmRvY3VtZW50Ow0KICAgICAgICAgICAgZG9jLm9wZW4oKTsNCiAgICAgICAgICAgIGRvYy53cml0ZShmaW5hbEh0bWwpOw0KICAgICAgICAgICAgZG9jLmNsb3NlKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIueUn+aIkOWbvuihqOWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAi55Sf5oiQ5Zu+6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIHRoaXMuY2xvc2VDaGFydERpYWxvZygpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5YWz6Zet5Zu+6KGo5a+56K+d5qGGDQogICAgY2xvc2VDaGFydERpYWxvZygpIHsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhcnREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmNoYXJ0SHRtbCA9ICIiOw0KICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQoNCiAgICAgIC8vIOa4heeQhkNoYXJ05a6e5L6LDQogICAgICBpZiAodGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUgJiYgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUuY29udGVudFdpbmRvdykgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOWwneivlemUgOavgeaJgOaciUNoYXJ05a6e5L6LDQogICAgICAgICAgaWYgKHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLmNvbnRlbnRXaW5kb3cuQ2hhcnQpIHsNCiAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlcyA9DQogICAgICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLmNvbnRlbnRXaW5kb3cuQ2hhcnQuaW5zdGFuY2VzOw0KICAgICAgICAgICAgaWYgKGluc3RhbmNlcykgew0KICAgICAgICAgICAgICBPYmplY3QudmFsdWVzKGluc3RhbmNlcykuZm9yRWFjaCgoaW5zdGFuY2UpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoaW5zdGFuY2UgJiYgdHlwZW9mIGluc3RhbmNlLmRlc3Ryb3kgPT09ICJmdW5jdGlvbiIpIHsNCiAgICAgICAgICAgICAgICAgIGluc3RhbmNlLmRlc3Ryb3koKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIua4heeQhkNoYXJ05a6e5L6L5aSx6LSlOiIsIGUpOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOa4heepuuWbvuihqOWuueWZqOWGheWuuQ0KICAgICAgaWYgKHRoaXMuJHJlZnMuY2hhcnRDb250ZW50KSB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmlubmVySFRNTCA9ICIiOw0KICAgICAgfQ0KDQogICAgICAvLyDmuIXnkIZpZnJhbWXlvJXnlKgNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRDaGFydElmcmFtZSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLm9ubG9hZCA9IG51bGw7DQogICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUub25lcnJvciA9IG51bGw7DQogICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUgPSBudWxsOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5riF55CGaWZyYW1l5aSx6LSlOiIsIGUpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmiafooYxpZnJhbWXlhoXnmoTmiYDmnInlhoXogZTohJrmnKwNCiAgICBleGVjdXRlSWZyYW1lU2NyaXB0cyhpZnJhbWUpIHsNCiAgICAgIC8vIOeugOWMluWQjueahOaWueazle+8jOS4jeWGjeWwneivleaJi+WKqOaJp+ihjOiEmuacrA0KICAgICAgY29uc29sZS5sb2coIuWbvuihqGlmcmFtZeW3suWKoOi9ve+8jOetieW+heiHqueEtua4suafky4uLiIpOw0KDQogICAgICAvLyDnoa7kv53miYDmnInlm77ooajpg73mnInmnLrkvJrmuLLmn5PlkI7lho3pmpDol49sb2FkaW5nDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0sIDgwMCk7DQogICAgfSwNCg0KICAgIC8vIOaWh+eroOWOu+mHjeaWueazlQ0KICAgIGRlZHVwbGljYXRlQXJ0aWNsZXMoYXJ0aWNsZXMpIHsNCiAgICAgIGlmICghYXJ0aWNsZXMgfHwgYXJ0aWNsZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBhcnRpY2xlczsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGl0bGVNYXAgPSBuZXcgTWFwKCk7DQogICAgICBjb25zdCByZXN1bHQgPSBbXTsNCg0KICAgICAgLy8g57uf6K6h55u45ZCM5qCH6aKY55qE5paH56ug5pWw6YePDQogICAgICBhcnRpY2xlcy5mb3JFYWNoKChhcnRpY2xlKSA9PiB7DQogICAgICAgIC8vIOWOu+mZpEhUTUzmoIfnrb7lkozmiYDmnInnqbrmoLzmnaXmr5TovoPmoIfpopgNCiAgICAgICAgY29uc3QgY2xlYW5UaXRsZSA9IGFydGljbGUudGl0bGUNCiAgICAgICAgICA/IGFydGljbGUudGl0bGUucmVwbGFjZSgvPFtePl0qPi9nLCAiIikucmVwbGFjZSgvXHMrL2csICIiKQ0KICAgICAgICAgIDogIiI7DQoNCiAgICAgICAgaWYgKHRpdGxlTWFwLmhhcyhjbGVhblRpdGxlKSkgew0KICAgICAgICAgIHRpdGxlTWFwLmdldChjbGVhblRpdGxlKS5jb3VudCsrOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRpdGxlTWFwLnNldChjbGVhblRpdGxlLCB7DQogICAgICAgICAgICBhcnRpY2xlOiB7IC4uLmFydGljbGUgfSwNCiAgICAgICAgICAgIGNvdW50OiAxLA0KICAgICAgICAgICAgb3JpZ2luYWxUaXRsZTogYXJ0aWNsZS50aXRsZSwgLy8g5L+d5a2Y5Y6f5aeL5qCH6aKY77yI5Y+v6IO95YyF5ZCrSFRNTOagh+etvu+8iQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgLy8g55Sf5oiQ5Y676YeN5ZCO55qE5paH56ug5YiX6KGoDQogICAgICB0aXRsZU1hcC5mb3JFYWNoKCh7IGFydGljbGUsIGNvdW50LCBvcmlnaW5hbFRpdGxlIH0pID0+IHsNCiAgICAgICAgaWYgKGNvdW50ID4gMSkgew0KICAgICAgICAgIC8vIOWmguaenOaciemHjeWkje+8jOWcqOagh+mimOWQjumdouWKoOS4iuaVsOmHj+agh+iusA0KICAgICAgICAgIC8vIOS9v+eUqOWOn+Wni+agh+mimO+8iOS/neaMgUhUTUzmoLzlvI/vvIkNCiAgICAgICAgICBhcnRpY2xlLnRpdGxlID0gYCR7b3JpZ2luYWxUaXRsZSB8fCAiIn3vvIgke2NvdW50fe+8iWA7DQogICAgICAgIH0NCiAgICAgICAgcmVzdWx0LnB1c2goYXJ0aWNsZSk7DQogICAgICB9KTsNCg0KICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["Wechat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Wechat.vue", "sourceRoot": "src/views/InfoEscalation", "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n        />\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"Form\"\r\n            label-width=\"90px\"\r\n            @submit.native.prevent\r\n          >\r\n            <el-form-item label=\"发布日期:\" prop=\"dateType\">\r\n              <el-radio-group v-model=\"queryParams.dateType\" size=\"small\">\r\n                <el-radio-button :label=\"1\">今天</el-radio-button>\r\n                <el-radio-button :label=\"2\">近2天</el-radio-button>\r\n                <el-radio-button :label=\"4\">近7天</el-radio-button>\r\n                <el-radio-button :label=\"5\">近30天</el-radio-button>\r\n                <el-radio-button :label=\"10\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <div style=\"display: flex\">\r\n              <el-form-item\r\n                label=\"小信优选:\"\r\n                prop=\"isTechnology\"\r\n                style=\"margin-right: 20px\"\r\n              >\r\n                <el-radio-group v-model=\"queryParams.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item label=\"小信精选:\" prop=\"emotion\">\r\n                <el-radio-group v-model=\"queryParams.emotion\" size=\"small\">\r\n                  <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                    >选中</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item label=\"检索词库:\" prop=\"tags\">\r\n              <el-radio-group v-model=\"queryParams.tags\" size=\"small\">\r\n                <el-radio :label=\"''\">全部</el-radio>\r\n                <el-radio\r\n                  v-for=\"item in tagsList1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.id\"\r\n                  >{{ item.name }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              style=\"width: 100%; overflow: auto\"\r\n              label=\"\"\r\n              prop=\"tagsSubset\"\r\n              v-if=\"queryParams.tags != ''\"\r\n            >\r\n              <el-checkbox\r\n                style=\"float: left; margin-right: 30px\"\r\n                :indeterminate=\"isIndeterminate\"\r\n                v-model=\"checkAll\"\r\n                @change=\"handleCheckAllTagsSubset\"\r\n                >全选</el-checkbox\r\n              >\r\n              <el-checkbox-group v-model=\"queryParams.tagsSubset\">\r\n                <el-checkbox\r\n                  v-for=\"item in tagsList\"\r\n                  :key=\"item.name\"\r\n                  :label=\"item.name\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item class=\"keyword\" label=\"关键词:\" prop=\"keywords\">\r\n              <el-input\r\n                ref=\"keywordRef\"\r\n                placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                style=\"width: 430px\"\r\n                v-model=\"queryParams.keywords\"\r\n                @focus=\"showHistoryList()\"\r\n                @blur=\"hideHistoryList()\"\r\n                @keyup.enter.native=\"handleSearch()\"\r\n              >\r\n              </el-input>\r\n              <div class=\"history\" v-show=\"showHistory\">\r\n                <div\r\n                  class=\"historyItem\"\r\n                  v-for=\"(history, index) in historyList\"\r\n                  :key=\"index\"\r\n                  v-loading=\"historyLoading\"\r\n                >\r\n                  <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                    {{ history.keyword }}\r\n                  </div>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"removeHistory(history, 1)\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >删除</el-button\r\n                  >\r\n                </div>\r\n                <div class=\"historyItem\">\r\n                  <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"clearHistory()\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >清空</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                style=\"margin-left: 10px; height: 36px\"\r\n                @click=\"handleSearch\"\r\n                >搜索</el-button\r\n              >\r\n            </el-form-item>\r\n            <div class=\"keyword-tip\">\r\n              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n            </div>\r\n          </el-form>\r\n          <div class=\"TopBtnGroup\">\r\n            <div class=\"TopBtnGroup_left\">\r\n              <el-checkbox v-model=\"checked\" @change=\"handleCheckAllChange\"\r\n                >全选</el-checkbox\r\n              >\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  title=\"批量删除文章\"\r\n                  class=\"icon-shanchu\"\r\n                  @click=\"batchDelete\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"icon-shuaxin-copy\"\r\n                  title=\"刷新\"\r\n                  @click=\"handleSearch('refresh')\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p class=\"toolTitle\">\r\n              <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"openReport\" v-hasPermi=\"['result:report:add']\"></i>\r\n            </p> -->\r\n              <!-- <p class=\"toolTitle\">\r\n              <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\"\r\n                v-hasPermi=\"['article:collection:snapshot']\" @click=\"resultEvent()\"></i>\r\n            </p> -->\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"添加到工作台账\"\r\n                  @click=\"openTaizhang\"\r\n                  v-hasPermi=\"['article:work:add']\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document-add\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"发布到每日最新热点\"\r\n                  @click=\"publishHot\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-chat-dot-round\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"Deepseek深度解读\"\r\n                  @click=\"articleAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n                  >Deepseek深度解读</span\r\n                >\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-pie-chart\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"生成Deepseek图表看板\"\r\n                  @click=\"chartAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"chartAiChat\"\r\n                  >生成Deepseek图表看板</span\r\n                >\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <el-checkbox\r\n                v-model=\"showSummary\"\r\n                @change=\"(e) => (showSummary = e)\"\r\n                style=\"margin-right: 10px\"\r\n                >是否显示摘要</el-checkbox\r\n              >\r\n              <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n              <el-select v-model=\"queryParams.sortMode\" size=\"mini\">\r\n                <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n                <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n                <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n                <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n                <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate0\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技无关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate1\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技有关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate2\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为其他</el-button\r\n              >\r\n            </el-col>\r\n          </el-row> -->\r\n          <el-table\r\n            :data=\"ArticleList\"\r\n            style=\"width: 100%; user-select: text\"\r\n            :show-header=\"false\"\r\n            ref=\"table\"\r\n            :height=\"\r\n              'calc(100vh - ' +\r\n              (374 + (queryParams.tags != '' ? 51 : 0)) +\r\n              'px)'\r\n            \"\r\n            @selection-change=\"handleTableSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"35\" align=\"center\" />\r\n            <el-table-column width=\"50\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #080808; font-size: 15px\">\r\n                  {{\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                    scope.$index +\r\n                    1\r\n                  }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"编号\"\r\n              align=\"center\"\r\n              key=\"id\"\r\n              prop=\"id\"\r\n              width=\"100\"\r\n              v-if=\"false\"\r\n            />\r\n            <el-table-column prop=\"title\" label=\"日期\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span class=\"article_title\" @click=\"openNewView(scope.row)\">\r\n                  <span\r\n                    style=\"color: #080808\"\r\n                    v-html=\"scope.row.title || scope.row.cnTitle\"\r\n                  ></span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ \"(\" + scope.row.publishTime + \")\" }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ scope.row.sourceName }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    大模型筛选:{{ scope.row.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                  </span>\r\n                </span>\r\n                <div\r\n                  class=\"ArticlMain\"\r\n                  style=\"\r\n                    display: -webkit-box;\r\n                    -webkit-box-orient: vertical;\r\n                    -webkit-line-clamp: 2;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    word-break: break-all;\r\n                  \"\r\n                  v-if=\"\r\n                    showSummary &&\r\n                    hasActualContent(scope.row.cnSummary || scope.row.summary)\r\n                  \"\r\n                >\r\n                  <span style=\"color: #9b9b9b\">摘要：</span>\r\n                  <span\r\n                    style=\"color: #4b4b4b\"\r\n                    v-html=\"\r\n                      changeColor(\r\n                        scope.row.cnSummary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        ) ||\r\n                          scope.row.summary.replace(\r\n                            /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                            'span'\r\n                          )\r\n                      )\r\n                    \"\r\n                    @click=\"openNewView(scope.row)\"\r\n                  ></span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\">\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"操作\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"></i>\r\n              </template>\r\n            </el-table-column> -->\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"handlePagination\"\r\n            :autoScroll=\"true\"\r\n          />\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"500px\"\r\n      :before-close=\"closeReport\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeReport\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"handleHistoryPagination\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n        :autoScroll=\"true\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek图表看板\"\r\n      :visible.sync=\"chartDialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"closeChartDialog\"\r\n      custom-class=\"chart-dialog\"\r\n      destroy-on-close\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div\r\n        v-if=\"chartDialogVisible\"\r\n        class=\"chart-container\"\r\n        v-loading=\"chartLoading\"\r\n        element-loading-text=\"正在生成图表看板...\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n      >\r\n        <div class=\"chart-content\" ref=\"chartContent\"></div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeChartDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listWork,\r\n  getWork,\r\n  delWork,\r\n  addWork,\r\n  updateWork,\r\n} from \"@/api/article/work\";\r\nimport { listKeywords } from \"@/api/article/keywords\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n  getListByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport { mapGetters } from \"vuex\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  components: { Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableLoading: false, // 表格loading状态\r\n      queryParams: {\r\n        id: 100,\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        dateType: 4,\r\n        tags: \"\",\r\n        tagsSubset: [],\r\n        keywords: \"\",\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      },\r\n      total: 0,\r\n      treeDataTransfer: [], // 原始树形数据\r\n      filterText: \"\", // 左侧树搜索栏\r\n      checkList: [], // 左侧勾选数据\r\n      ArticleList: [], // 列表数据\r\n      checked: false, // 全选\r\n      ids: [], // 选中的数据\r\n      // 非多个禁用\r\n      multiple: true,\r\n      dialogVisible: false, // 添加到报告弹框\r\n      reportOptions: [], // 报告列表\r\n      reportId: \"\", // 已选择的报告\r\n      tagsList: [], // 检索词库二级列表\r\n      tagsList1: [], // 检索词库一级列表\r\n      checkAll: false, // 检索词库全选\r\n      isIndeterminate: true, // 检索词库选了值\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      showSummary: true,\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 初始化完成标记 */\r\n      initializationCompleted: false,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 查询防抖 */\r\n      queryDebounceTimer: null,\r\n      /* 防止重复查询 */\r\n      isQuerying: false,\r\n      /* 标记右侧筛选条件是否发生变化 */\r\n      isRightFilter: false,\r\n      /* 标记左侧树是否重置 */\r\n      isLeftReset: false,\r\n      /* 选中的数据源分类 */\r\n      selectedClassify: \"5\",\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n      nodeCheckList: [],\r\n      chartDialogVisible: false,\r\n      chartHtml: \"\",\r\n      chartLoading: true,\r\n      currentChartIframe: null, // 添加变量跟踪当前iframe\r\n      difyApikey: {\r\n        article: \"\",\r\n        chart: \"\",\r\n      },\r\n      chartPrompt: \"\",\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"queryParams.dateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.tags\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n\r\n        // this.queryParams.tagsSubset = [];\r\n        this.checkAll = true;\r\n        this.isIndeterminate = false;\r\n\r\n        if (newVal != \"\") {\r\n          // 不在这里设置tableLoading，让后续的queryArticleList来处理\r\n          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })\r\n            .then((res) => {\r\n              this.tagsList = res.data;\r\n              this.handleCheckAllTagsSubset(true);\r\n              // this.handleRightFilterChange();\r\n            })\r\n            .catch((error) => {\r\n              console.error(\"获取检索词库失败:\", error);\r\n              this.$message.error(\"获取检索词库失败\");\r\n            });\r\n        } else {\r\n          this.handleRightFilterChange();\r\n        }\r\n      },\r\n    },\r\n    \"queryParams.tagsSubset\": {\r\n      handler(newVal, oldVal) {\r\n        if (\r\n          !this.initializationCompleted ||\r\n          JSON.stringify(newVal) === JSON.stringify(oldVal)\r\n        )\r\n          return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      },\r\n    },\r\n    dialogVisible(val) {\r\n      if (val) {\r\n        api.getNewBuilt({ sourceType: \"1\" }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n            this.closeReport();\r\n          }\r\n        });\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\"]),\r\n  },\r\n  async created() {\r\n    getConfigKey(\"sys.ai.platform\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.aiPlatform = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.articleAiPrompt = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.chartPrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.chartPrompt = res.msg;\r\n      }\r\n    });\r\n    // 获取用户头像\r\n    this.userAvatar = this.$store.getters.avatar;\r\n    try {\r\n      // 先加载基础数据\r\n      Promise.all([\r\n        this.getArticleHistory(),\r\n        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {\r\n          this.tagsList1 = res.data.filter((item) => item.parentId == 0);\r\n        }),\r\n      ]);\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      if (this.roles.includes(\"information\")) {\r\n        this.showSummary = false;\r\n      }\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 默认全选第一页数据源\r\n        if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {\r\n          // 全选第一页的所有数据源\r\n          const firstPageData = [...this.treeDataTransfer];\r\n          this.checkList = firstPageData;\r\n          this.savedCheckboxData = firstPageData;\r\n\r\n          // 通知 TreeTable 组件设置选中状态\r\n          this.$nextTick(() => {\r\n            if (this.$refs.treeTable) {\r\n              this.$refs.treeTable.restoreSelectionSilently(firstPageData);\r\n            }\r\n          });\r\n\r\n          // 延迟一下再查询文章列表，确保选中状态已设置\r\n          setTimeout(() => {\r\n            this.queryArticleList();\r\n          }, 100);\r\n        } else {\r\n          // 如果没有数据源，直接查询文章列表\r\n          this.queryArticleList();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理右侧筛选条件变化\r\n    handleRightFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handlePagination() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 历史记录分页处理\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n      this.$nextTick(() => {\r\n        const dialogContent = document.querySelector(\".el-dialog__body\");\r\n        if (dialogContent) {\r\n          dialogContent.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          platformType: 0,\r\n          id: this.queryParams.id,\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          m: 1,\r\n          dateType:\r\n            this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n          tags: this.queryParams.tags,\r\n          tagsSubset: this.queryParams.tagsSubset,\r\n          keywords: this.queryParams.keywords,\r\n          isTechnology: this.queryParams.isTechnology,\r\n          emotion: this.queryParams.emotion,\r\n          label: this.queryParams.tagsSubset.join(\",\"),\r\n          // 添加关键字过滤参数\r\n          filterwords: this.filterText || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          hasCache: this.queryParams.hasCache,\r\n        };\r\n\r\n        if (!this.queryParams.tags) {\r\n          params.tagsSubset = [];\r\n          params.label = \"\";\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.queryParams.pageNum,\r\n            pageSize: this.queryParams.pageSize,\r\n            id: this.queryParams.id,\r\n            isSort: this.queryParams.sortMode,\r\n            dateType:\r\n              this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n            tags: this.queryParams.tags,\r\n            tagsSubset: this.queryParams.tagsSubset,\r\n            keywords: this.queryParams.keywords,\r\n            isTechnology: this.queryParams.isTechnology,\r\n            emotion: this.queryParams.emotion,\r\n            label: this.queryParams.tagsSubset.join(\",\"),\r\n            platformType: 0,\r\n          };\r\n\r\n          if (!this.queryParams.tags) {\r\n            params.tagsSubset = [];\r\n            params.label = \"\";\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 1 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.esRetrieval(params);\r\n\r\n          if (res.code == 200) {\r\n            let articleList = res.data.list\r\n              ? res.data.list.map((item) => {\r\n                  item.cnTitle = item.cnTitle\r\n                    ? this.changeColor(item.cnTitle)\r\n                    : null;\r\n                  item.title = this.changeColor(item.title);\r\n                  return item;\r\n                })\r\n              : [];\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.queryParams.keywords ||\r\n              this.queryParams.keywords.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n            this.total = res.data.total || 0;\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=\r\n                this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.queryParams.pageNum = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.queryParams.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.queryParams.pageNum = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤文本，避免触发 handleFilterSearch\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 重置树选择（保留原方法名以兼容）\r\n    treeClear() {\r\n      this.handleReset();\r\n    },\r\n\r\n    // 处理树分页\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库全选处理\r\n    handleCheckAllTagsSubset(val) {\r\n      this.queryParams.tagsSubset = val\r\n        ? this.tagsList.map((item) => item.name)\r\n        : [];\r\n      this.isIndeterminate = false;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 更新过滤文本\r\n      this.filterText = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库多选处理\r\n    handleCheckedChange(value) {\r\n      let checkedCount = value.length;\r\n      this.checkAll = checkedCount === this.tagsList.length;\r\n      this.isIndeterminate =\r\n        checkedCount > 0 && checkedCount < this.tagsList.length;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n      this.handleRightFilterChange();\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch(flag) {\r\n      this.scrollToTopImmediately();\r\n      if (!flag) {\r\n        this.queryParams.pageNum = 1;\r\n      }\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 关键词历史选择\r\n    keywordsChange(item) {\r\n      this.queryParams.keywords = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.queryParams.pageNum = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 右侧表格多选框选中数据\r\n    handleTableSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      if (selection.length == this.ArticleList.length) {\r\n        this.checked = true;\r\n      } else {\r\n        this.checked = false;\r\n      }\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 全选\r\n    handleCheckAllChange(val) {\r\n      if (val) {\r\n        this.$refs[\"table\"].toggleAllSelection();\r\n      } else {\r\n        this.$refs[\"table\"].clearSelection();\r\n      }\r\n    },\r\n    // 打开添加到报告\r\n    openReport() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确定添加到报告\r\n    async reportSubmit() {\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      let keyWordList = this.ids.map((item) => {\r\n        return { reportId: this.reportId, listId: item };\r\n      });\r\n      let res = await api.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.queryArticleList();\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.$refs[\"table\"].clearSelection();\r\n      this.checked = false;\r\n      this.closeReport();\r\n    },\r\n    // 关闭添加到报告\r\n    closeReport() {\r\n      this.reportId = \"\";\r\n      this.dialogVisible = false;\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要删除的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.ids.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 添加到台账\r\n    openTaizhang() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认添加已勾选的数据项到台账统计?\")\r\n        .then(() => {\r\n          addWork(this.ids).then(() => {\r\n            this.$message({ type: \"success\", message: \"添加成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 发布到每日最新热点\r\n    publishHot() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.ids.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 历史记录相关方法\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id]);\r\n      if (type == 1) {\r\n        this.$refs[\"keywordRef\"].focus();\r\n        this.getArticleHistory();\r\n      } else {\r\n        this.getArticleHistory();\r\n        this.getArticleHistory1();\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n\r\n    getArticleHistory() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(\r\n        (response) => {\r\n          this.historyList = response.rows;\r\n          this.historyLoading = false;\r\n        }\r\n      );\r\n    },\r\n\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.$refs[\"keywordRef\"].focus();\r\n      await cleanArticleHistory(1);\r\n      this.getArticleHistory();\r\n    },\r\n\r\n    moreHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.showHistory = false;\r\n      this.historyLoading = true;\r\n      this.getArticleHistory1();\r\n      this.dialogVisible1 = true;\r\n    },\r\n\r\n    getArticleHistory1() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false;\r\n      });\r\n    },\r\n\r\n    // 文章详情\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 检查文本是否有实际内容\r\n    hasActualContent(text) {\r\n      if (!text) return false;\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      if (this.$refs.rightMain) {\r\n        this.$refs.rightMain.scrollTop = 0;\r\n      }\r\n\r\n      if (this.$refs.table) {\r\n        const bodyWrapper = this.$refs.table.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (bodyWrapper) {\r\n          bodyWrapper.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关键字高亮\r\n    changeColor(str) {\r\n      const regex = /<img\\b[^>]*>/gi;\r\n      let Str = str && str.replace(regex, \"\");\r\n      if (\r\n        Str &&\r\n        ((this.queryParams.tags &&\r\n          this.queryParams.tagsSubset &&\r\n          this.queryParams.tagsSubset.length) ||\r\n          this.queryParams.keywords)\r\n      ) {\r\n        let keywords = [\r\n          ...this.queryParams.tagsSubset,\r\n          ...(this.queryParams.keywords\r\n            ? this.queryParams.keywords.split(\",\")\r\n            : []),\r\n        ];\r\n        keywords.forEach((keyitem) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            let replaceString =\r\n              '<span class=\"highlight\" style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n\r\n    // 快照生成\r\n    resultEvent() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message.warning(\"请先选择文章\");\r\n      }\r\n      let ids = this.ids;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (ids.length == 1) {\r\n        let row = this.ArticleList.filter((item) => item.id == ids[0]);\r\n        if (row && row.snapshotUrl) zhuangtai = \"查看\";\r\n        url = row.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        this.$msgbox({\r\n          title: \"提示\",\r\n          message: \"快照正在生成中，请稍后查看\",\r\n          showCancelButton: false,\r\n          confirmButtonText: \"关闭\",\r\n          beforeClose: (_, __, done) => {\r\n            done();\r\n          },\r\n        });\r\n        API.downLoadExportKe(ids)\r\n          .then((response) => {\r\n            if (response.code != 200) {\r\n              this.$message({\r\n                message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                type: \"error\",\r\n              });\r\n            }\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.ids.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n    chartAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyChartAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekChartAiChat();\r\n      }\r\n    },\r\n    // dify图表看板\r\n    async difyChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await difyAiQa(\r\n          articleResult.data.content,\r\n          \"blocking\",\r\n          \"dify.chart.apikey\"\r\n        );\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.answer) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = \"\";\r\n\r\n        try {\r\n          // 尝试解析JSON格式（有些返回可能是JSON字符串）\r\n          const parsedData = JSON.parse(aiData.answer);\r\n          content2 =\r\n            parsedData.answer ||\r\n            parsedData.html ||\r\n            parsedData.content ||\r\n            aiData.answer;\r\n        } catch (e) {\r\n          // 如果不是JSON格式，直接使用原始内容\r\n          content2 = aiData.answer;\r\n        }\r\n\r\n        // 处理思考标记\r\n        const thinkStartIndex = content2.indexOf(\"<think>\");\r\n        const thinkEndIndex = content2.indexOf(\"</think>\");\r\n\r\n        // 提取有效内容\r\n        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {\r\n          // 如果存在思考标记，只取</think>后面的内容\r\n          content2 = content2.substring(thinkEndIndex + 8).trim();\r\n        }\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // deepseek图表看板\r\n    async deepseekChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        const prompt = this.chartPrompt + `\\n\\n${articleResult.data.content}`;\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await deepseekAiQa(prompt, false);\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.choices) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = aiData.choices[0].message.content;\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // 关闭图表对话框\r\n    closeChartDialog() {\r\n      this.isAborted = true;\r\n      this.chartDialogVisible = false;\r\n      this.chartHtml = \"\";\r\n      this.chartLoading = false;\r\n      this.isRequesting = false;\r\n\r\n      // 清理Chart实例\r\n      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {\r\n        try {\r\n          // 尝试销毁所有Chart实例\r\n          if (this.currentChartIframe.contentWindow.Chart) {\r\n            const instances =\r\n              this.currentChartIframe.contentWindow.Chart.instances;\r\n            if (instances) {\r\n              Object.values(instances).forEach((instance) => {\r\n                if (instance && typeof instance.destroy === \"function\") {\r\n                  instance.destroy();\r\n                }\r\n              });\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"清理Chart实例失败:\", e);\r\n        }\r\n      }\r\n\r\n      // 清空图表容器内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      // 清理iframe引用\r\n      if (this.currentChartIframe) {\r\n        try {\r\n          this.currentChartIframe.onload = null;\r\n          this.currentChartIframe.onerror = null;\r\n          this.currentChartIframe = null;\r\n        } catch (e) {\r\n          console.error(\"清理iframe失败:\", e);\r\n        }\r\n      }\r\n    },\r\n    // 执行iframe内的所有内联脚本\r\n    executeIframeScripts(iframe) {\r\n      // 简化后的方法，不再尝试手动执行脚本\r\n      console.log(\"图表iframe已加载，等待自然渲染...\");\r\n\r\n      // 确保所有图表都有机会渲染后再隐藏loading\r\n      setTimeout(() => {\r\n        this.chartLoading = false;\r\n      }, 800);\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input_Fixed {\r\n  width: 100%;\r\n}\r\n\r\n.treeBox {\r\n  // margin-top:70px;\r\n  width: 100%;\r\n  height: calc(100vh - 178px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.rightMain {\r\n  height: calc(100vh - 60px);\r\n  overflow: hidden;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    // background: #dbdbd8;\r\n    // margin-bottom: 20px;\r\n    height: 60px;\r\n    box-shadow: 0 0px 10px 0px #cecdcd;\r\n    border-bottom: solid 1px #e2e2e2;\r\n\r\n    .TopBtnGroup_left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .toolTitle {\r\n      margin: 0 10px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      .deepseek-text {\r\n        color: #5589f5; // 使用与图标相同的颜色\r\n        margin-left: 4px;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      &:nth-of-type(1) {\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ArticlMain {\r\n    padding: 0 0 0 30px;\r\n    color: #3f3f3f;\r\n    font-size: 14px;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .ArticlMain > span:hover {\r\n    color: #1889f3;\r\n    border-bottom: solid 1px #0798f8;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n::v-deep .drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-document:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-pie-chart:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-table td.el-table__cell div {\r\n  padding-left: 10px;\r\n}\r\n\r\n::v-deep .el-table-column--selection .cell {\r\n  padding-right: 0px;\r\n  padding-left: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 0;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 90px;\r\n  line-height: 1;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-table--medium .el-table__cell {\r\n  padding: 10px 0;\r\n}\r\n\r\n.article_title {\r\n  margin-left: 10px;\r\n  font-size: 15px;\r\n}\r\n\r\n.article_title:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n  cursor: pointer;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  min-height: 600px;\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 0;\r\n  position: relative;\r\n\r\n  .chart-content {\r\n    width: 100%;\r\n    height: 600px;\r\n    overflow: auto;\r\n    display: block;\r\n  }\r\n}\r\n\r\n::v-deep .chart-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // padding: 15px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 10px 15px;\r\n    border-top: 1px solid #e4e7ed;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0 !important;\r\n}\r\n</style>\r\n"]}]}