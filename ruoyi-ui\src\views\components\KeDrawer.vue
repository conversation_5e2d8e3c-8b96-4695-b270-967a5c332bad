<template>
  <div>
    <el-drawer
      :title="title"
      :visible.sync="visble"
      direction="rtl"
      :before-close="handleClose"
      size="800px"
    >
      <el-form
        :model="FormModel"
        size="mini"
        ref="form"
        class="formSytle"
        label-width="120px"
        label-position="top"
        :rules="FormRules"
      >
        <el-form-item :label="title == '修改科情' || title == '新增科情' ? '科情名称' : '专题名称'" prop="KeName">
          <el-input
            v-model="FormModel.KeName"
            :placeholder="title == '修改科情' || title == '新增科情' ? '请输入科情名称' : '请输入专题名称'"
          ></el-input>
        </el-form-item>
        <el-form-item label="平台类型" prop="platformType">
          <el-select
            placeholder="请选择平台类型"
            v-model="FormModel.platformType"
            style="width: 100%;"
            multiple
          >
          <el-option v-for="(item, index) in sourceTypeList" :key="index" :label="item.name"
                    :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布地区" prop="region">
          <el-radio-group v-model="FormModel.region" style="margin-right: 20px;">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">国内</el-radio>
            <el-radio :label="2">境外</el-radio>
          </el-radio-group>
          <el-select
            v-model="FormModel.countryOrCrea"
            placeholder="请选择地区"
            v-if="FormModel.region == 1"
            multiple
            collapse-tags
          >
            <el-option
              v-for="(item, index) in areaList"
              :key="index"
              :label="item.regionName"
              :value="item.regionName"
            ></el-option>
          </el-select>
          <el-select
            v-model="FormModel.countryOrCrea"
            placeholder="请选择国家"
            v-if="FormModel.region == 2"
            multiple
            collapse-tags
          >
            <el-option
              v-for="(item, index) in  countryList"
              :key="index"
              :label="item.regionName"
              :value="item.regionName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="mainBody">
          <el-input
            v-model="FormModel.mainBody"
            placeholder="请输入关键词,多个关键词之间用','分割"
            type="textarea"
            rows="3"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="分析词" prop="analysis">
          <el-input v-model="FormModel.analysis" placeholder="请输入分析词,多个分析词之间用','分割"></el-input>
        </el-form-item>-->
        <el-form-item label="排除词" prop="exclude">
          <el-input v-model="FormModel.exclude" placeholder="请输入排除词,多个排除词之间用','分割"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            style="margin-left: 38%;width: 150px;"
            v-if="title == '修改科情' || title == '新增科情'"
            @click="saveEvent"
            v-hasPermi="['article:monitoring:add']"
          >保存</el-button>
          <el-button
            type="primary"
            style="margin-left: 38%;width: 150px;"
            v-else
            @click="saveEvent"
            v-hasPermi="[ 'article:special:add']"
          >保存</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import { getListClassify } from '@/api/article/classify'

export default {
  props: {
    title: {
      required: true
    },
    visble: {
      required: true
    },
    editData: {
      required: false
    },
    countryList: {
      type: Array
    },
    areaList: {
      type: Array
    }
  },
  data() {
    return {
      timer: null,
      FormModel: {
        KeName: '' /* 科情名称 */,
        platformType: [] /* 平台类型 */,
        region: 0 /* 地域词 */,
        mainBody: '' /* 主体词 */,
        analysis: '' /* 分析词 */,
        exclude: '' /* 排除词 */,
        countryOrCrea: ''
      },
      FormRules: {
        KeName: [{ required: true, message: '请输入科情名称' }],
        platformType: [{ required: true, message: '请选择平台类型', trigger: 'change' }]
      },
      creaList: ['北京市', '天津市', '河北省'],
      country: ['俄罗斯', '英国', '德国'],
      sourceTypeList:[]
    }
  },
  watch: {
    'FormModel.region': {
      handler(newVal, oldVal) {
        if (oldVal) {
          this.FormModel.countryOrCrea = ''
        }
      },
      deep: true
    },
    editData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.FormModel = {
            id: newVal.id,
            KeName: newVal.title /* 科情名称 */,
            platformType: newVal.type ? newVal.type.split(',') : newVal.type /* 平台类型 */,
            region: Number(newVal.areaType) /* 地域词 */,
            mainBody: newVal.keywords /* 主体词 */,
            exclude: newVal.exclusions /* 排除词 */,
            countryOrCrea: newVal.publishArea ? newVal.publishArea.split(',') : [newVal.publishArea]
          }
        }
      },
      deep: true
    }
  },
  created() {
    getListClassify().then(res => {
      this.sourceTypeList = res.data
    })
  },
  methods: {
    handleClose() {
      this.$emit('beforCloe', false)
      this.FormModel = {
        KeName: '' /* 科情名称 */,
        platformType: '' /* 平台类型 */,
        region: '' /* 地域词 */,
        mainBody: '' /* 主体词 */,
        exclude: '' /* 排除词 */,
        countryOrCrea: ''
      }
      this.$refs.form.resetFields()
    },
    /* 确认提交 */
    saveEvent() {
      this.$refs.form.validate(vald => {
        if (vald) {
          this.$emit('submitKe', this.FormModel)
          this.timer = setTimeout(() => {
            this.$refs.form.resetFields()
          }, 100)
        }
      })
    }
  },
  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="scss" scoped></style>